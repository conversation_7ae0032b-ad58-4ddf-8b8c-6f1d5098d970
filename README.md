# [[PROJECT_NAME]]
## [-> see below for new project setup <-](#boilerplate-setup-for-new-projects)

## Initial Setup

Follow these steps after cloning the repository into an empty folder.
All shell commands should be executed inside this folder, if not mentioned differently.

### Start ddev and install TYPO3
On first start, TYPO3 is installed automatically and the configuration wizard runs.
```bash
ddev start
```
Use the following settings if asked:
- Database connection: ```mysqli```
- Database username: ```db```
- Database password: ```db```
- Databse hostname: ```db```
- Databse port: ```3306```
- Unix socket: _empty_
- Use existing database: ```y```
- Name of database: ```db```

The following is required but _can be chosen randomly_ as it will be replaced on DB sync anyway.
- Username for admin account: ```admin```
- Password for admin account: ```admin123``` _(needs to fulfill TYPO3 minimum security requirements)_
- Name of the TYPO3 site: _empty_
- Site type: ```no```
- Web server type: ```none```

### Use environment configuration from dist
```bash
cp .env.dist .env
cp vendor/typo3/cms-install/Resources/Private/FolderStructureTemplateFiles/root-htaccess public/.htaccess
```

### Get current database and fileadmin and restart ddev
```bash
ddev dump all stage # you may also use: ddev dump all live
ddev restart
```

## Database and Fileadmin

Database and Fileadmin sync works via ddev command:

```bash
# Database Stage
ddev dump db stage

# Database Live
ddev dump db live

# Fileadmin Stage
ddev dump fileadmin stage

# Fileadmin Live
ddev dump fileadmin live

# Database + Fileadmin all Stage
ddev dump all stage

# Database + Fileadmin all Live
ddev dump all live
```

### Prerequisites

The following packages are required in the host system to make the dump scripts work:
- sshpass _(optional if SSH login via password is used)_
- sshfs
- rsync

Install them all at once with this command:
```bash
# Required packages
sudo apt-get install sshpass sshfs rsync
```

### Dump configuration

The available actions and the corresponding commands can be configured in the script:
```
.ddev/commands/host/dump
```

### Frontend Stack

Using Vite AssetCollector (https://docs.typo3.org/p/praetorius/vite-asset-collector/main/en-us/)  & ddev-viteserve (https://github.com/torenware/ddev-viteserve)

```bash

# install frontend depdencies
ddev npm i
ddev add-on get torenware/ddev-viteserve

# change or create: .ddev/.env

VITE_PROJECT_DIR=.
VITE_PRIMARY_PORT=5173
VITE_PRIMARY_URL=typo3-boilerplate.ddev.site
VITE_SECONDARY_PORT=5273
VITE_JS_PACKAGE_MGR=npm

# run vite
ddev vite-serve start

# run watcher while coding
ddev npm run watch

# build assets
ddev npm run build:production

# Individualised for every project
# change "***.typo3-boilerplate.ddev.site" in .ddev/.env
# change "***.typo3-boilerplate.ddev.site" in vite.config.js

```

# Boilerplate Setup for new Projects

Use this repo as template for new projects:
https://docs.github.com/de/repositories/creating-and-managing-repositories/creating-a-repository-from-a-template

Before running ddev in this new repo for the first time you should prepare the following:
* create and checkout develop branch
* .ddev/config.yaml : set name
* .github/workflows : as long you work in a develop branch you can ignore this and edit it later (see below)
* config/sites : move configuration to newly named directory and change settings (URLs etc.) in config.yml - for ids see "Import Example Content" below
* Import Example Content (see below) - you can delete the data directory afterward
* ./documentation : you can delete this
* .env.dist : edit and copy it to .env
* composer.json : change name and description
* For new mandants see "Add new mandant" below (you can do this later)
* README.md : change project name and remove this section

Now you can run ddev start and follow the instructions in "Initial Setup" above.
If you want, you can use
ddev dump all
to import example content from the boilerplate stage environment.
After that you can set the following steps for deployment:

* .ddev/commands/host/dump : you can use this file to configure hosts and paths
* .github/workflows : edit staging.yml and create production.yml
* deploy.php : setup host(s) and application name

## Import Example Content

### In boilerplate-development context
After changes on stage environment stage.vancado.de: Sync Example Content (db and fileadmin) from stage and export it into the data directory:

```
ddev sync2data stage
```
The contents of the data directory can be used as a database/fileadmin kickstarter for new projects ans ist part of the repository.

### In template context
Please import fileadmin and db.sql (i.e. to stage environment); remove ./data/fileadmin, ./data/db.sql and export script Directory after Projects.


## Einen neuen Mandanten hinzufügen

### Notwendige Schritte

1. Im TYPO3 (live) Backend den Dummy Mandanten Seitenbaum duplizieren (bitte rekursives anlegen von Unterseiten im Backend User aktivieren)
2. In der IDE (lokal) eine Mandanten Konfigurations-Extension anlegen auf Basis der data/vnc_mandant/packages nach packages Extension (kopieren und einfügen in den packages), diese kann dann beispielsweise "vnc_mandant_dummy" genannt werden.
3. In dieser Extension müssen in den folgenden Dateien/Dinge angepasst werden:
    1. "constants.typoscript": Zu überschreibende Konstanten wie z.B.: bestimmte Page Uid's oder die CSS Farb Hex RGB Werte.
    2. TCA/"sys_template.php": Neuen Extension Key, sowie Mandantenname ändern.
    3. "composer.json": Anpassen des composer package Namens, sowie des extension-keys.
    4. "ext_emconf.php": Extension key und Titel der Extension
4. Kopieren/Umbenennen der TYPO3 data/vnc_mandant/config/sites/dummy-mandant/config.yaml nach config/sites/neuermandant und daraus neue erzeugen für den neuen Partner. Dort dann die 3 Domains für Live, Stage und ddev anpassen. Die "rootPageId" und die uid für die 404 Seite (liegt unter Sonstige Seiten) anpassen "errorContentSource". Und: websiteTitle anpassen
5. Hinzufügen eines additional_hostnames für eine lokale ddev Domain, diese wird in der config.yaml von ddev (.ddev/config.yaml) ergänzt. (Alternativ: Subdirectory. Kein zusätzlicher Eintrag in der .ddev/config.yaml notwendig)
6. ```ddev restart``` ausführen
7. Die Partner Konfigurations-Extension in die composer.json hinzufügen ```ddev composer req vancado/vnc-mandant-dummy:^1.0```
8. Im TYPO3 Backend einen News Ordner für den Partner anlegen und diesen dann auch auf der Übersichtsseite (Gruppe/News Übersicht) und auf der Detailseite (Gruppe/News Übersicht/News Detailansicht) der News in den jeweiligen Plugins neu verknüpfen.
9. Auf der Rootseite des neuen Seitenbaums im Template Module das TypoScript Template des Partners (auch live) hinzufügen und den Dummy vnc_mandant entfernen (Include Static TypoScript Template).
