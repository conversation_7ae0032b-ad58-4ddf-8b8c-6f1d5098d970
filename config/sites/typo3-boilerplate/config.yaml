base: 'https://www.typo3-boilerplate.de/'
baseVariants:
  -
    base: 'https://stage.vancado.de/'
    condition: 'applicationContext == "Production/Stage"'
  -
    base: 'https://stage-v13.vancado.de/'
    condition: 'applicationContext == "Development/v13"'
  -
    base: 'https://typo3-boilerplate.ddev.site/'
    condition: 'applicationContext == "Development"'
dependencies:
  - vnc_sitepackage/fluid-styled-content
  - typo3/seo-sitemap
  - typo3/felogin
  - vnc_sitepackage/indexed-search
errorHandling:
  -
    errorCode: 404
    errorHandler: Page
    errorContentSource: 't3://page?uid=17'
languages:
  -
    title: Deutsch
    enabled: true
    base: /
    typo3Language: de
    locale: de_DE
    iso-639-1: de
    navigationTitle: DE
    hreflang: de
    direction: ''
    flag: de
    languageId: 0
rootPageId: 1
routeEnhancers:
  PageTypeSuffix:
    type: PageType
    map:
      /: 0
      sitemap.xml: 1533906435
routes:
  -
    route: robots.txt
    type: staticText
    content: "User-agent: *\r\nDisallow: /typo3/\r\nDisallow: /typo3_src/\r\n"
websiteTitle: 'TYPO3 Boilerplate'
imports:
  -
    resource: 'EXT:vnc_sitepackage/SubPackages/news/Configuration/YAML/news-config.yaml'
