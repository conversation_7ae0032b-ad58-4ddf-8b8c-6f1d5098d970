export const ScrollTable = (() => {
    console.log("ScrollTable");
    window.addEventListener("click", (event) => {
        const button = event.target.closest("[data-table-scroll]");
        const container = event.target.closest("[data-table-scroller]");
        const viewport =
            container && container.querySelector("[data-table-viewport]");
        if (button && viewport) {
            const dir = button.dataset.tableScroll;
            if (dir === "start") {
                viewport.scrollLeft = 0;
            } else if (dir === "end") {
                viewport.scrollLeft =
                    viewport.scrollWidth - viewport.clientWidth;
            }
        }
    });
    window.addEventListener("DOMContentLoaded", (event) => {
        const tables = Array.from(
            document.querySelectorAll("[data-table-scroller]")
        );
        tables.forEach((table) => {
            console.log(table.dataset.tableScroller);
            if (table.dataset.tableScroller) {
                const options = JSON.parse(table.dataset.tableScroller);
                if (options && options.maxCols) {
                    const cols = Array.from(table.querySelectorAll("thead td"));
                    if (cols && cols.length > options.maxCols) {
                        const viewport = table.querySelector(
                            "[data-table-viewport]"
                        );
                        const container = viewport.closest(".col-12");
                        if (container) {
                            container.classList.add("col-xl-10", "mx-xl-auto");
                        }
                    }
                }
            }
        });
    });
})();

export default ScrollTable;
