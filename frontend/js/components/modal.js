export const ModalDialog = (() => {
  // async function fetchUrl(url) {
  //   const html = await fetch(url)
  //     .then((response) => response.text());

  //   const parser = new DOMParser();
  //   const doc = parser.parseFromString(html, 'text/html');
  //   // const update = doc.querySelector('.page-section');

  //   return doc;
  // }

  function close(dialog) {
    const keyFrame = dialog && new KeyframeEffect(
      dialog,
      [{ translate: "0 -100%", opacity: "0" }],
      { duration: 320, easing: "ease", direction: "normal" }
    );

    const animation = new Animation(keyFrame, document.timeline);
    animation.play();
    animation.addEventListener('finish', () => {
      dialog?.close();
      const scrollpos = getComputedStyle(document.documentElement).getPropertyValue('--scroll-pos');
      window.scrollTo({ top: parseInt(scrollpos, 10), behavior: 'instant' });

      // onClose();
    });
  }

  document.addEventListener('click', async (event) => {
    const toggle = event.target.closest('[data-modal-toggle]');
    if (toggle) {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();

      const id = toggle && toggle.dataset.modalToggle;
      const dialog = id && document.querySelector(`[data-modal-dialog=${id}]`);
      if (dialog) {
        if (dialog.open) {
          close(dialog);
        } else {
          document.documentElement.style.setProperty('--scroll-pos', `${Math.round(document.documentElement.scrollTop)}px`);
          dialog?.showModal();

          dialog.addEventListener('keydown', event => {
            if(event.key === 'Escape') {
              event.preventDefault();
              event.stopPropagation();
              event.stopImmediatePropagation();
              close(dialog);
            }
          }, { once: true });
        }
      }
    }
  });

  document.addEventListener('click', async (event) => {
    const button = event.target.closest('[data-modal-close]');
    if (button) {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();

      const id = button && button.dataset.modalClose;
      const dialog = button.closest('[data-modal-dialog]') || document.querySelector(`[data-modal-dialog=${id}]`);
      if (dialog) {
        close(dialog);
      }
    }
  });

  document.addEventListener('click', async ({target}) => {
    const dialog = target.closest('dialog[data-modal-dismiss]');
    if (dialog && target.nodeName === 'DIALOG') {
      close(dialog);
    }
  });

  document.addEventListener('DOMContentLoaded', () => {
    const modal = document.querySelector('[data-modal-open]');
    if (modal) {
      modal.showModal();
    }
  });


})();

export default ModalDialog;
