import '@splidejs/splide/css';
import Splide from '@splidejs/splide';

export const VncEvents = (() => {
    window.addEventListener('load', function () {
        setTimeout(() => {
            const elements = document.querySelectorAll('[data-vnc-events-carousel]');

            Array.from(elements).forEach((el) => {
                let arrows, breakpoints, pagination;
                let itemsPerSlide = parseInt(el.dataset.itemsPerSlide);

                if (itemsPerSlide > 1) {
                    breakpoints = {
                        breakpoints: {
                            768: {
                                perPage: Math.min(itemsPerSlide, 2), // Max 2 auf Tablet
                                gap: '1rem',
                            },
                            480: {
                                perPage: 1, // Nur 1 auf Mobile
                                gap: '1rem',
                            },
                        }
                    }
                }
                const splide = new Splide(el, {
                    perPage: itemsPerSlide,
                    rewind: true,
                    accessibility: {
                        slideFocus: true,
                        slideTabindex: false,
                    },
                    ...breakpoints,
                });
                const arrowFirst = splide.root.querySelector('.splide__arrow--first');
                const arrowLast = splide.root.querySelector('.splide__arrow--last');
                const arrowPrev = splide.root.querySelector('.splide__arrow--prev');
                const arrowNext = splide.root.querySelector('.splide__arrow--next');

                function updatePagination() {
                    const pages = Math.ceil(splide.length / itemsPerSlide);
                    let recentPage = Math.floor(splide.index / itemsPerSlide) + 1;

                    if (Math.floor(splide.index / itemsPerSlide) !== splide.index / itemsPerSlide) {
                        recentPage = Math.ceil((splide.index + itemsPerSlide) / itemsPerSlide);
                    }
                    if (pagination) {
                        pagination.textContent = `Seite ${recentPage} von ${pages}`;
                    }
                }

                function updateArrowStates() {
                    const isAtStart = splide.index === 0;
                    const isAtEnd = splide.index >= splide.length - itemsPerSlide;

                    if (arrowFirst) {
                        arrowFirst.disabled = isAtStart;
                    }
                    if (arrowPrev) {
                        arrowPrev.disabled = isAtStart;
                    }

                    if (arrowLast) {
                        arrowLast.disabled = isAtEnd;
                    }
                    if (arrowNext) {
                        arrowNext.disabled = isAtEnd;
                    }
                }

                function fixTabIndexForVisibleSlides() {
                    const slides = splide.root.querySelectorAll('.splide__slide');
                    const currentPerPage = splide.options.perPage;

                    slides.forEach((slide, index) => {
                        const isVisible = index >= splide.index && index < splide.index + currentPerPage;

                        if (isVisible) {
                            slide.removeAttribute('tabindex');

                            const focusableElements = slide.querySelectorAll('a, button, [data-teaser-cta]');
                            focusableElements.forEach(element => {
                                element.removeAttribute('tabindex');
                            });
                        } else {
                            slide.setAttribute('tabindex', '-1');

                            const focusableElements = slide.querySelectorAll('a, button, [data-teaser-cta]');
                            focusableElements.forEach(element => {
                                element.setAttribute('tabindex', '-1');
                            });
                        }
                    });
                }

                // Craeate a mutation observer to watch for tabindex changes
                function createTabIndexObserver() {
                    const observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'attributes' && mutation.attributeName === 'tabindex') {
                                setTimeout(() => {
                                    fixTabIndexForVisibleSlides();
                                }, 10);
                            }
                        });
                    });

                    const slides = splide.root.querySelectorAll('.splide__slide');
                    slides.forEach(slide => {
                        observer.observe(slide, { attributes: true, attributeFilter: ['tabindex'] });

                        const focusableElements = slide.querySelectorAll('a, button, [data-teaser-cta]');
                        focusableElements.forEach(element => {
                            observer.observe(element, { attributes: true, attributeFilter: ['tabindex'] });
                        });
                    });

                    return observer;
                }

                // create custom paginastion
                splide.on('pagination:mounted', function() {
                    pagination = splide.root.querySelector('.splide__pagination');
                    arrows = splide.root.querySelector('.splide__arrows');

                    if (splide.length <= itemsPerSlide) {
                        arrows.remove();
                        return;
                    }

                    pagination.removeAttribute('role');
                    pagination.setAttribute('aria-label', 'Aktuelle Seite');
                    updatePagination();
                    updateArrowStates();
                    fixTabIndexForVisibleSlides();
                });

                splide.on('move', function() {
                    updatePagination();
                    updateArrowStates();
                    fixTabIndexForVisibleSlides();
                });

                splide.on('resized', function() {
                    itemsPerSlide = splide.options.perPage;
                    updatePagination();
                    updateArrowStates();
                    fixTabIndexForVisibleSlides();
                });

                splide.on('mounted', function() {
                    fixTabIndexForVisibleSlides();
                    createTabIndexObserver();
                });

                arrowFirst.addEventListener('click', () => {
                    splide.go(0);
                });

                arrowLast.addEventListener('click', () => {
                    let ratio = splide.length / itemsPerSlide;
                    let ratioFloor = Math.floor(splide.length / itemsPerSlide);
                    let itemToGo = 0;

                    itemToGo = (ratioFloor * itemsPerSlide) - itemsPerSlide;
                    if (ratio !== ratioFloor) {
                        itemToGo += splide.length % itemsPerSlide;
                    }
                    splide.go(itemToGo);
                });

                splide.mount();
            });
        }, 0);
    });
})();

export default VncEvents;
