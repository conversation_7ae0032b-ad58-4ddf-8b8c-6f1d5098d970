@layer components {
    .powermail_error_message {
        @apply text-(--styling-form-field-error-error_msg) text-xs w-full block;
    }

    .powermail_input_field {
        @apply h-12 md:h-16 px-2 md:px-4 w-full rounded-(--styling-form-border-radius) bg-(--styling-form-field-enabled-fill) border-(--styling-form-field-enabled-border) border-2;
    }

  

    .input--checkbox input[type="checkbox"]:checked::after {
        content: "";
        @apply absolute w-[6px] h-[14px] border-2 border-(--styling-form-field-active-border) bg-transparent rotate-45;
        border-top: 0;
        border-left: 0;
        top: 4px;
        left: 7px;
    }

    .input--checkbox input[type="checkbox"]:focus {
        @apply border-(--styling-form-field-active-border);
    }
}
