
@layer components {
    .button {
        @apply inline-flex gap-3 items-center justify-center transition-all cursor-pointer px-8 py-0 h-12 
        whitespace-nowrap
        focus:-outline-offset-4 focus:outline-2
        disabled:pointer-events-none 
        rounded-(--styling-cta-button-border-radius)
        text-(length:--styling-cta-button-font-font_size)
        leading-(--styling-cta-button-font-line_height)
        font-(--styling-cta-button-font-font_weight);

        svg {
            @apply stroke-current fill-current w-6 h-6;
        }
    }

    .button--large {
        @apply !h-16 !w-16 !p-4;
    }

    .button--primary {
        @apply bg-(--styling-cta-button-color-primary-default-fill)
      text-(--styling-cta-button-color-primary-default-text)
      hover:bg-(--styling-cta-button-color-primary-hover-fill)
      hover:text-(--styling-cta-button-color-primary-hover-text)
      outline-(--styling-cta-button-color-primary-default-border)
      active:bg-(--styling-cta-button-color-primary-active-fill)
      active:text-(--styling-cta-button-color-primary-active-text)
      focus:outline-(--styling-cta-button-color-primary-focus-border)
      focus:bg-(--styling-cta-button-color-primary-focus-fill)
      focus:text-(--styling-cta-button-color-primary-focus-text)
      disabled:bg-(--styling-cta-button-color-primary-disabled-fill)
      disabled:text-(--styling-cta-button-color-primary-disabled-text)
      disabled:outline-(--styling-cta-button-color-primary-disabled-border);
    }
    .button--secondary {
        @apply bg-(--styling-cta-button-color-secondary-default-fill)
      text-(--styling-cta-button-color-secondary-default-text)
      hover:bg-(--styling-cta-button-color-secondary-hover-fill)
      hover:text-(--styling-cta-button-color-secondary-hover-text)
      outline-(--styling-cta-button-color-secondary-default-border)
      active:bg-(--styling-cta-button-color-secondary-active-fill)
      active:text-(--styling-cta-button-color-secondary-active-text)
      focus:outline-(--styling-cta-button-color-secondary-focus-border)
      focus:bg-(--styling-cta-button-color-secondary-focus-fill)
      focus:text-(--styling-cta-button-color-secondary-focus-text)
      disabled:bg-(--styling-cta-button-color-secondary-disabled-fill)
      disabled:text-(--styling-cta-button-color-secondary-disabled-text)
      disabled:outline-(--styling-cta-button-color-secondary-disabled-border);
    }
    .button--outline {
        @apply bg-(--styling-cta-button-color-outline-default-fill)
      text-(--styling-cta-button-color-outline-default-text)
      border-(--styling-cta-button-color-outline-default-border)
      border-2

      hover:bg-(--styling-cta-button-color-outline-hover-fill)
      hover:text-(--styling-cta-button-color-outline-hover-text)
      hover:border-(--styling-cta-button-color-outline-hover-border)

      active:bg-(--styling-cta-button-color-outline-active-fill)
      active:text-(--styling-cta-button-color-outline-active-text)
      active:border-(--styling-cta-button-color-outline-active-border)

      focus:outline-(--styling-cta-button-color-outline-focus-border)
      focus:bg-(--styling-cta-button-color-outline-focus-fill)
      focus:text-(--styling-cta-button-color-outline-focus-text)
      focus:border-(--styling-cta-button-color-outline-focus-border)
  
      disabled:bg-transparent
      disabled:text-(--styling-cta-button-color-outline-disabled-text)
      disabled:outline-(--styling-cta-button-color-outline-disabled-border);
    }
    .button--icon {
        @apply p-3 w-12 relative;
        svg {
            @apply stroke-current fill-current w-full h-full;
            use {
                @apply w-4 h-4;
            }
        }
    }

    .link,
    .btn {
        @apply text-s font-(--value-weight-bold) text-(--styling-cta-link-dark-default-text) hover:text-(--styling-cta-link-dark-hover-text)  focus:text-(--styling-cta-link-dark-focus-text) focus:shadow-[0_0_0_1px_var(--styling-cta-link-dark-focus-border)] outline-none hover:underline underline-offset-2;
    }

    .btn.-icon {
        @apply inline-block align-middle items-center text-(--styling-cta-link-dark-default-icon) hover:text-(--styling-cta-link-dark-hover-icon) focus:text-(--styling-cta-link-dark-focus-icon);

        &::before {
            display: inline-block;
            font: normal normal normal 1em / 1 "Nucleo";
            color: currentColor;
            flex-shrink: 0;
            text-transform: none;
            text-decoration: none;
            margin-right: 0.5rem;
            vertical-align: middle;
        }
        &.-text::before {
            content: "\ec26";
        }
        &.-isDownload::before {
            content: "\ed3c";
        }
        &.-isExternal::before {
            content: "\f001";
        }
        &.-isPhone::before {
            content: "\f196";
        }
        &.-isEmail::before {
            content: "\f044";
        }
        &.-isWarning::before {
            content: "\f11f";
        }
        &.-svg {
          @apply inline-flex items-center justify-center gap-2;
            &:before {
                @apply hidden;
            }
        }
    }
}
