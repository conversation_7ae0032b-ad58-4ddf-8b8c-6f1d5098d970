/* Header Component Styles */

.header-large {
    color: var(--styling-text-dark-title, #000);
    font-feature-settings: 'liga' off, 'clig' off;
    
    /* Project Headlines Large/h2 L */
    font-family: var(--global-font-large-headline-font_family, Roboto);
    font-size: var(--global-font-large-headline-l-font_size, 40px);
    font-style: normal;
    font-weight: var(--global-font-large-headline-l-font_weight, 700);
    line-height: var(--global-font-large-headline-l-line_height, 52px); /* 130% */
}

/* Responsive Varianten */
.header-large-responsive {
    @apply header-large;
}

@media (max-width: 768px) {
    .header-large-responsive {
        font-size: var(--global-font-large-headline-m-font_size, 32px);
        line-height: var(--global-font-large-headline-m-line_height, 42px);
    }
}

@media (max-width: 480px) {
    .header-large-responsive {
        font-size: var(--global-font-large-headline-s-font_size, 28px);
        line-height: var(--global-font-large-headline-s-line_height, 36px);
    }
}

/* Tailwind-kompatible Utility-Klassen */
@layer components {
    .text-header-large {
        @apply header-large;
    }
    
    .text-header-large-responsive {
        @apply header-large-responsive;
    }
}

/* Varianten für verschiedene Header-Größen */
.header-medium {
    color: var(--styling-text-dark-title, #000);
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: var(--global-font-medium-headline-font_family, Roboto);
    font-size: var(--global-font-medium-headline-font_size, 32px);
    font-weight: var(--global-font-medium-headline-font_weight, 600);
    line-height: var(--global-font-medium-headline-line_height, 42px);
}

.header-small {
    color: var(--styling-text-dark-title, #000);
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: var(--global-font-small-headline-font_family, Roboto);
    font-size: var(--global-font-small-headline-font_size, 24px);
    font-weight: var(--global-font-small-headline-font_weight, 600);
    line-height: var(--global-font-small-headline-line_height, 32px);
}
