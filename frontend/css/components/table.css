@theme {
    --table-border-width: 0.125rem;
}

@layer components {
    .table {
        @apply w-full table-fixed border-collapse;
        font-variant-numeric: lining-nums tabular-nums;
    }

    .table caption {
        @apply caption-bottom;
    }

    .table th,
    .table td {
        @apply p-2 border bg-(--styling-table-cell-data-background-light);
    }

    .table .is-active td {
    }

    .table thead th,
    .table thead td {
        @apply font-bold align-top;
    }

    .table thead td,
    .table th {
        @apply bg-(--styling-table-cell-headline-background-dark) text-(--styling-table-cell-headline-text);
    }
    .table thead td {
        @apply text-(--styling-table-cell-headline-text);
    }

    .table tbody td {
        @apply text-(--styling-table-cell-data-text);
    }

    .table--transparent-header thead th,
    .table--transparent-header thead td {
        @apply bg-transparent;
    }

    .table--custom-borders th,
    .table--custom-borders td {
        @apply border-0;
        box-shadow: inset -0.125rem 0 0 0 var(--styling-table-line-color),
            inset 0 -0.125rem 0 0 var(--styling-table-line-color);
    }

    .table--custom-borders th:last-child,
    .table--custom-borders td:last-child {
        box-shadow: inset 0 -0.125rem 0 0 var(--styling-table-line-color);
    }

    .table--custom-borders tr:last-child:not(tr:only-child) th,
    .table--custom-borders tr:last-child:not(tr:only-child) td {
        box-shadow: inset -0.125rem 0 0 0 var(--styling-table-line-color);
    }

    .table--custom-borders tr:last-child:not(tr:only-child) th:last-child,
    .table--custom-borders tr:last-child:not(tr:only-child) td:last-child {
        @apply shadow-none;
    }

    .table--custom-borders thead th,
    .table--custom-borders thead td {
        @apply relative;
        box-shadow: inset -0.125rem 0 0 0 var(--styling-table-line-color),
            inset 0 -0.125rem 0 0 var(--styling-table-line-color);
    }

    .table--custom-borders thead th:last-child,
    .table--custom-borders thead td:last-child {
        box-shadow: inset 0 -0.125rem 0 0 var(--styling-table-line-color);
    }

    .table--custom-borders thead th:not(:last-child)::after,
    .table--custom-borders thead td:not(:last-child)::after {
        @apply content-[''] absolute bottom-0 right-0 z-[1] block w-[0.125rem] h-[0.125rem];
        background-color: var(--styling-table-line-color);
    }

    .table--custom-borders tfoot tr th,
    .table--custom-borders tfoot tr td {
        box-shadow: inset -0.125rem 0 0 0 var(--styling-table-line-color);
    }

    .table--custom-borders tfoot tr th:last-child,
    .table--custom-borders tfoot tr td:last-child {
        @apply shadow-none;
    }

    .table--custom-borders tfoot ~ tbody tr:last-child:not(tr:only-child) th,
    .table--custom-borders tfoot ~ tbody tr:last-child:not(tr:only-child) td {
        box-shadow: inset -0.125rem 0 0 0 var(--styling-table-line-color),
            inset 0 -0.125rem 0 0 var(--styling-table-line-color);
    }

    .table--custom-borders
        tfoot
        ~ tbody
        tr:last-child:not(tr:only-child)
        th:last-child,
    .table--custom-borders
        tfoot
        ~ tbody
        tr:last-child:not(tr:only-child)
        td:last-child {
        box-shadow: inset 0 -0.125rem 0 0 var(--styling-table-line-color);
    }

    .table--custom-borders--th thead th,
    .table--custom-borders--th thead td {
        box-shadow: inset -0.125rem 0 0 0 var(--styling-table-line-color),
            inset 0 -0.125rem 0 0 var(--styling-table-line-color);
    }

    .table--custom-borders--th thead th:last-child,
    .table--custom-borders--th thead td:last-child {
        box-shadow: inset 0 -0.125rem 0 0 var(--styling-table-line-color);
    }

    .table--custom-borders--th th:first-child,
    .table--custom-borders--th td:first-child {
        box-shadow: inset -0.125rem 0 0 0 var(--styling-table-line-color),
            inset 0 -0.125rem 0 0 var(--styling-table-line-color);
    }

    .table--custom-borders--th thead th:first-child,
    .table--custom-borders--th thead td:first-child {
        box-shadow: inset -0.125rem 0 0 0 var(--styling-table-line-color),
            inset 0 -0.125rem 0 0 var(--styling-table-line-color);
    }

    .table--custom-borders--th tfoot tr:last-child th:first-child,
    .table--custom-borders--th tfoot tr:last-child td:first-child {
        box-shadow: inset -0.125rem 0 0 0 var(--styling-table-line-color);
    }

    .table--custom-borders--th tbody tr:last-child th:first-child,
    .table--custom-borders--th tbody tr:last-child td:first-child,
    .table--custom-borders--th
        tfoot
        ~ tbody
        tr:last-child:not(tr:only-child)
        th:first-child,
    .table--custom-borders--th
        tfoot
        ~ tbody
        tr:last-child:not(tr:only-child)
        td:first-child {
        box-shadow: inset -0.125rem 0 0 0 var(--styling-table-line-color),
            inset 0 -0.125rem 0 0 var(--styling-table-line-color);
    }

    .table--responsive {
        @apply lg:hidden flex flex-col-reverse;
    }

    .table--responsive tbody,
    .table--responsive tr {
        @apply block mb-2;
    }

    .table--responsive thead {
        @apply hidden;
    }

    .table--responsive th,
    .table--responsive td {
        @apply flex justify-between gap-2 p-0 border-0 font-normal mb-0.5 shadow-none first:shadow-none;
    }

    .table--responsive th::before,
    .table--responsive td::before {
        @apply content-[attr(data-content)] block flex-[0_0_50%] border-r border-white p-4 font-bold;
    }

    .table--responsive th span,
    .table--responsive td span {
        @apply block p-4;
    }

    .table--responsive tfoot td {
        @apply block p-4 before:content-none;
    }

    .table--responsive .is-collapsed td {
        @apply hidden first:block;
    }

    .table__controls {
        @apply flex justify-center;
    }
}

.table--scroll {
    @apply relative w-full;
}

.table--scroll__viewport {
    @apply max-w-full overflow-x-auto;
    scroll-behavior: smooth;
}

.table--scroll__viewport::-webkit-scrollbar {
    width: 8px;
    height: 12px;
}

.table--scroll__viewport::-webkit-scrollbar-track {
    background: var(--styling-navigation-scroll-background);
}

.table--scroll__viewport::-webkit-scrollbar-thumb {
    background: var(--styling-navigation-scroll-bar);
    border-radius: 4px;
}

.table--scroll__viewport::-webkit-scrollbar {
    width: 12px;
}

.table--scroll__viewport::-webkit-scrollbar-track {
    background: var(--styling-navigation-scroll-background);
}

.table--scroll__viewport::-webkit-scrollbar-thumb {
    background-color: var(--styling-navigation-scroll-bar);
    border-radius: 1rem;
    border: 1px solid var(--styling-navigation-scroll-background);
}

.table--scroll__viewport .table {
    @apply w-auto;
}

.table--scroll__viewport th,
.table--scroll__viewport td {
    @apply min-w-[8rem];
}

@media (min-width: 1440px) {
    .table--scroll .col-xl-10 {
        @apply relative z-[1];
    }

    .table--scroll .col-xl-10 + .table__controls {
        @apply absolute z-0 top-1/2 -translate-y-1/2 w-full justify-between;
    }
}
