@utility container-viewport {
    @apply block px-0 mx-auto w-full max-w-[1800px];
}

@utility container-grid {
  @apply px-3 sm:px-4 lg:px-6 mx-auto w-full max-w-[1440px] grid grid-cols-12 gap-4 lg:gap-6;
}

@utility container {
    @apply block px-3 sm:px-4 lg:px-6 mx-auto w-full max-w-[1440px];
}
@utility grid-default {
    @apply grid grid-cols-12 gap-4 lg:gap-6;
}


@utility container-medium {
    @apply block px-3 sm:px-4 lg:px-6 mx-auto w-full max-w-[1200px];
}
@utility container-small {
    @apply block px-3 sm:px-4 lg:px-6 mx-auto w-full max-w-[920px];
}

@utility grid-small {
    @apply grid grid-cols-12 gap-4;
}

@utility container-tiny {
    @apply block px-3 sm:px-4 lg:px-6 mx-auto w-full max-w-[680px];
}

@utility gap-default {
    @apply gap-6 lg:gap-8;
}

@utility gap-content {
    @apply gap-4 lg:gap-4;
}
