@font-face {
  font-family: <PERSON><PERSON>;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("/frontend/fonts/roboto-v30-latin-regular.woff2") format("woff2");
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("/frontend/fonts/roboto-v30-latin-700.woff2") format("woff2");
}


@utility headline-xl {
    @apply 
    text-(length:--global-font-small-headline-xl-font-size)
    leading-(--global-font-small-headline-xl-line-height)
    font-(--global-font-small-headline-xl-weight)
    lg:text-[clamp(var(--global-font-small-headline-xl-font-size),0.25rem+3.13vw,var(--global-font-large-headline-xl-font-size))]
    lg:leading-[clamp(var(--global-font-small-headline-xl-line-height),0.71rem+3.57vw,var(--global-font-large-headline-xl-line-height))]
    lg:font-(--global-font-large-headline-xl-font-weight);
}


@utility headline-l {
    @apply  
    text-(length:--global-font-small-headline-l-font-size)
    leading-(--global-font-small-headline-l-line-height)
    font-(--global-font-small-headline-l-weight)
    lg:text-(length:--global-font-large-headline-l-font-size)
    lg:leading-(--global-font-large-headline-l-line-height)
    lg:font-(--global-font-large-headline-l-font-weight);
}

@utility headline-m {
  @apply 
  text-(length:--global-font-small-headline-m-font-size)
  leading-(--global-font-small-headline-m-line-height)
  font-(--global-font-small-headline-m-weight)
  lg:text-(length:--global-font-large-headline-m-font-size)
  lg:leading-(--global-font-large-headline-m-line-height)
  lg:font-(--global-font-large-headline-m-font-weight);
}

@utility headline-s {
    @apply 
    text-(length:--global-font-small-headline-s-font-size)
    leading-(--global-font-small-headline-s-line-height)
    font-(--global-font-small-headline-s-weight)
    lg:text-(length:--global-font-large-headline-s-font-size)
    lg:leading-(--global-font-large-headline-s-line-height)
    lg:font-(--global-font-large-headline-s-font-weight);
    
}

@utility headline-xs {
  @apply
  text-(length:--global-font-small-headline-xs-font-size)
  leading-(--global-font-small-headline-xs-line-height)
  font-(--global-font-small-headline-xs-weight)
  lg:text-(length:--global-font-large-headline-xs-font-size)
  lg:leading-(--global-font-large-headline-xs-line-height)
  lg:font-(--global-font-large-headline-xs-font-weight);
}

@utility headline-xxs {
    @apply
    text-(length:--global-font-small-headline-xxs-font-size)
    leading-(--global-font-small-headline-xxs-line-height)
    font-(--global-font-small-headline-xxs-weight)
    lg:text-(length:--global-font-large-headline-xxs-font-size)
    lg:leading-(--global-font-large-headline-xxs-line-height)
    lg:font-(--global-font-large-headline-xxs-font-weight);
}

@utility text-xl {
    @apply
    text-(length:--global-font-small-paragraph-xl-font-size)
    leading-(--global-font-small-paragraph-xl-line-height)
    font-(--global-font-small-paragraph-xl-weight)
    lg:text-(length:--global-font-large-paragraph-xl-font-size)
    lg:leading-(--global-font-large-paragraph-xl-line-height)
    lg:font-(--global-font-large-paragraph-xl-font-weight);
}

@utility text-l {
    @apply
    text-(length:--global-font-small-paragraph-l-font-size)
    leading-(--global-font-small-paragraph-l-line-height)
    font-(--global-font-small-paragraph-l-weight)
    lg:text-(length:--global-font-large-paragraph-l-font-size)
    lg:leading-(--global-font-large-paragraph-l-line-height)
    lg:font-(--global-font-large-paragraph-l-font-weight);
}

@utility text-m {
    @apply
    text-(length:--global-font-small-paragraph-m-font-size)
    leading-(--global-font-small-paragraph-m-line-height)
    font-(--global-font-small-paragraph-m-weight)
    lg:text-(length:--global-font-large-paragraph-m-font-size)
    lg:leading-(--global-font-large-paragraph-m-line-height)
    lg:font-(--global-font-large-paragraph-m-font-weight);
}

@utility text-s {
    @apply
    text-(length:--global-font-small-paragraph-s-font-size)
    leading-(--global-font-small-paragraph-s-line-height)
    font-(--global-font-small-paragraph-s-weight)
    lg:text-(length:--global-font-large-paragraph-s-font-size)
    lg:leading-(--global-font-large-paragraph-s-line-height)
    lg:font-(--global-font-large-paragraph-s-font-weight);
}

@utility text-xs {
    @apply
    text-(length:--global-font-small-paragraph-xs-font-size)
    leading-(--global-font-small-paragraph-xs-line-height)
    font-(--global-font-small-paragraph-xs-weight)
    lg:text-(length:--global-font-large-paragraph-xs-font-size)
    lg:leading-(--global-font-large-paragraph-xs-line-height)
    lg:font-(--global-font-large-paragraph-xs-font-weight);
}

@utility text-xxs {
    @apply
    text-(length:--global-font-small-paragraph-xxs-font-size)
    leading-(--global-font-small-paragraph-xxs-line-height)
    font-(--global-font-small-paragraph-xxs-weight)
    lg:text-(length:--global-font-large-paragraph-xxs-font-size)
    lg:leading-(--global-font-large-paragraph-xxs-line-height)
    lg:font-(--global-font-large-paragraph-xxs-font-weight);
}


@utility text-primary {
    @apply text-(--color-title-primary);

}

@utility text-secondary {
    @apply text-(--color-title-secondary);
}