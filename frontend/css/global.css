@import "tailwindcss";

/**
* base
*/
@import "./base/figma";
@import "./base/layout";
@import "./base/theme";
@import "./base/utilities";
@import "./base/typography";

/**
* vendors
*/
@import "../../node_modules/@splidejs/splide/dist/css/splide.min.css";

/**
* components
*/
@import "./components/button";
@import "./components/icon";
@import "./components/navigation";
@import "./components/powermail";
@import "./components/text";
@import "./components/splide";
@import "./components/table";

/**
* extensions
*/
@import './extensions/vnc-events';

@utility debug-marker {
  @apply bg-red-200 text-red-500 absolute py-2 px-4 z-50;
}
