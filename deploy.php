<?php

namespace Deployer;

require 'recipe/typo3.php';
require 'contrib/rsync.php';
require 'contrib/cachetool.php';

// Hosts
host('stage')
    ->setHostname('vancado-service.de')
    ->setRemoteUser('ssh-27471-master')
    ->set('keep_releases', 5)
    ->set('deploy_path', '/kunden/vancado-service.de/_SITES/vancado/vancado.de/typo3-boilerplate/stage')
    ->setSshArguments(['-o StrictHostKeyChecking=no'])
    ->set('php_bin', 'php83'); // PHP 8.3 für TYPO3 13

host('v13')
    ->setHostname('vancado-service.de')
    ->setRemoteUser('ssh-27471-master')
    ->set('keep_releases', 5)
    ->set('deploy_path', '/kunden/vancado-service.de/_SITES/vancado/vancado.de/typo3-boilerplate/v13-stage')
    ->setSshArguments(['-o StrictHostKeyChecking=no'])
    ->set('php_bin', 'php83'); // PHP 8.3 für TYPO3 13

// Project name
set('application', 'Typo3 Boilerplate');
set('ssh_multiplexing', true); // Speed up deployment

set('rsync_src', function () {
    return __DIR__; // If your project isn't in the root, you'll need to change this.
});

set('typo3_webroot', 'public');

set('release_name', function () {
    return (string)run('date +"%Y-%m-%d_%H-%M-%S"');
});

set('shared_files', array(
    'config/system/settings.php',
    'public/.htaccess',
    '.env'
));

set('shared_dirs', [
    '{{typo3_webroot}}/fileadmin',
    '{{typo3_webroot}}/typo3temp',
    '{{typo3_webroot}}/uploads',
    'var'
]);

add('rsync', [
    'exclude' => [
        '.ddev',
        '.git',
        '/.env',
        '/storage/',
        '/node_modules/',
        '.github',
        'deploy.php'
    ],
    'options' => [
        'links'
    ]
]);

// Writable dirs by web server
add('writable_dirs', []);
set('allow_anonymous_stats', false);

// [Optional] if deploy fails automatically unlock.
after('deploy:failed', 'deploy:unlock');

// TYPO3-specific tasks

desc('Update TYPO3 language files');
task('typo3:languageUpdate', function () {
    cd('{{release_path}}');
    run('{{php_bin}} vendor/bin/typo3 language:update');
});

desc('Update TYPO3 db schema');
task('typo3:updateSchema', function () {
    cd('{{release_path}}');
    run('{{php_bin}} -d memory_limit=8G vendor/bin/typo3 database:updateschema');
});

desc('Clear TYPO3 caches');
task('typo3:clearCacheAll', function () {
    cd('{{release_path}}');
    run('{{php_bin}} -d memory_limit=8G vendor/bin/typo3 cache:flush');
});

task('deploy', [
    'deploy:info',
    'deploy:setup',
    'deploy:lock',
    'deploy:release',
    'rsync', // Deploy code & built assets
    'deploy:shared',
    'deploy:symlink',
    'deploy:unlock',
    'typo3:clearCacheAll',
    'typo3:updateSchema',
    'typo3:languageUpdate',
    'typo3:clearCacheAll',
    'deploy:cleanup',
]);
