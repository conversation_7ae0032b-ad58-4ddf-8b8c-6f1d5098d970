<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" original="labels.xlf" datatype="plaintext" product-name="vnc/teaser100-textimage" date="2025-06-13T15:19:48+02:00">
		<header></header>
		<body>
			<trans-unit id="title">
				<source>Image + Text Box Highlight</source>
			</trans-unit>
			<trans-unit id="description">
				<source>Full-width image with text box (further content as carousel), e.g. for highlight content.</source>
			</trans-unit>
			<trans-unit id="header.label">
				<source>Title (for information only)</source>
			</trans-unit>
			<trans-unit id="items.label">
				<source>Teaser</source>
			</trans-unit>
			<trans-unit id="items.header.label">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="items.subheader.label">
				<source>Sub Headline</source>
			</trans-unit>
			<trans-unit id="items.bodytext.label">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="items.link.label">
				<source>Link</source>
			</trans-unit>
			<trans-unit id="items.linktext.label">
				<source>Linktext</source>
			</trans-unit>
			<trans-unit id="items.image.label">
				<source>image</source>
			</trans-unit>
			<trans-unit id="format.label">
				<source>Image Format</source>
			</trans-unit>
			<trans-unit id="format.items.widescreen.label">
				<source>Widescreen (16:9)</source>
			</trans-unit>
			<trans-unit id="format.items.univisium.label">
				<source>Univisium (2:1)</source>
			</trans-unit>
			<trans-unit id="format.items.wide.label">
				<source>Wide (5:2)</source>
			</trans-unit>
			<trans-unit id="format.items.ultrawide.label">
				<source>Ultrawide (3:1)</source>
			</trans-unit>
		</body>
	</file>
</xliff>
