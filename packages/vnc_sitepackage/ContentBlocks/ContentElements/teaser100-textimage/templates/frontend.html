
<f:layout name="Default"/>

<f:section name="Main">
        <div class="container-xxxl">

          
            <f:variable name="carouselType" value="stage"></f:variable>
            <f:variable name="carouselOptions" value="{
                &quot;arrows&quot;: true
            }"></f:variable>
            <f:variable name="sliderContent">
                <f:for each="{data.items}" as="slide" key="label">
                    <div class="carousel__item splide__slide">
                        
                        <f:variable name="sizes" value="{0:'xxl', 1:'xl', 2:'lg', 3:'xs'}"/>
                        <f:variable name="cropVariants" value="{
                            default:'wide',
                            tablet:'wide',
                            mobile:'upright'
                        }"/>
                        <h1 class="debug-marker">TEASER 100 TEXT IMAGE</h1>
                        <f:variable name="record" value="{ data: slide, files: slide.image }"></f:variable>
                        <f:variable name="teaserContentLayout" value="overlay"></f:variable>
                        <f:variable name="teaserContentPosition" value="right"></f:variable>
                        <f:variable name="teaserContentBackground" value="default"></f:variable>

                        
                        <f:render partial="Teaser" arguments="{_all}"></f:render>
                    </div>
                </f:for>
            </f:variable>
            <f:render partial="Carousel" arguments="{_all}"></f:render>
        </div>
</f:section>


