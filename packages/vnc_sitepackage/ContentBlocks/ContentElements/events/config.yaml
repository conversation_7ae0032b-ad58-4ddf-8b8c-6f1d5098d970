name: vnc-sitepackage/events
typeName: vnc_events_custom
group: vnccontent
prefixFields: true
prefixType: full
fields:
  - identifier: header
    useExistingField: true
  - identifier: header_layout
    useExistingField: true
  - identifier: subheader
    useExistingField: true
  - identifier: bodytext
    useExistingField: true
  - identifier: layout
    type: Select
    renderType: selectSingle
    items:
      - label: 'Under each other'
        value: 0
      - label: 'Next to each other'
        value: 1
    onChange: reload
  - identifier: items_per_slide
    displayCond: FIELD:layout:=:0
    type: Select
    renderType: selectSingle
    items:
      - label: 4
        value: 4
      - label: 3
        value: 3
      - label: 2
        value: 2
  - identifier: categories
    type: Relation
    minitems: 1
    maxitems: 99
    allowed: 'pages'
    suggestOptions:
      default:
        addWhere: 'AND pages.doktype = 254'
  - identifier: listPid
    type: Relation
    maxitems: 1
    allowed: 'pages'
    size: 1
  - identifier: singlePid
    type: Relation
    maxitems: 1
    allowed: 'pages'
    size: 1
  - identifier: space_after_class
    type: Select
    useExistingField: true
