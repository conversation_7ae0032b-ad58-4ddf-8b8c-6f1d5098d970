<div class="custom-event-item">
    <f:if condition="{event.categoryIcon}">
        <i class="svg-icon {event.categoryIcon}"></i>
    </f:if>
    <div class="event-category">{event.category}</div>
    <h3 class="event-title">{event.title}</h3>
    <div class="event-date">
        <i class="svg-icon icon nc-calendar-date"></i>
        <f:format.date format="d.m.Y">{event.eventStartdate}</f:format.date>
        <f:if condition="{event.eventEnddate}">
            - <f:format.date format="d.m.Y">{event.eventEnddate}</f:format.date>
        </f:if>
    </div>
    <div class="event-time">
        <i class="svg-icon icon nc-clock"></i>
        <f:format.date format="H:i">{event.eventStarttime}</f:format.date>
        <f:if condition="{event.eventEndtime}">
            - <f:format.date format="H:i">{event.eventEndtime}</f:format.date>
        </f:if>
    </div>
    <div class="event-location">
        {event.location}
    </div>
    <div class="event-price">
        {event.price}
    </div>
    <div class="event-actions">
        <f:if condition="{event.link}">
            <f:then>
                <f:link.typolink parameter="{event.link}"
                                 class="button button--primary event-link"
                                 additionalAttributes="{'data-teaser-cta': 'true'}"
                >
                    <f:if condition="{event.linkText}">
                        <f:then>
                            {event.linkText}
                        </f:then>
                        <f:else>
                            <f:translate key="LLL:EXT:vnc_sitepackage/ContentBlocks/ContentElements/events/language/frontend.xlf:show_details" />
                        </f:else>
                    </f:if>
                </f:link.typolink>
            </f:then>
            <f:else>
                <f:if condition="{data.singlePid.0}">
                    <f:link.typolink parameter="{data.singlePid.0.uid}"
                                     additionalParams="event={event.uid}"
                                     class="button button--primary event-link"
                                     additionalAttributes="{'data-teaser-cta': 'true'}"
                    >
                        <f:translate key="LLL:EXT:vnc_sitepackage/ContentBlocks/ContentElements/events/language/frontend.xlf:show_details" />
                    </f:link.typolink>
                </f:if>
            </f:else>
        </f:if>
    </div>
</div>
