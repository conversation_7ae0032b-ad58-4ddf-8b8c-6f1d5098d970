# Custom Events ContentBlock

Dieses ContentBlock ist eine angepasste Version des ursprünglichen `vnc/events` ContentBlocks aus dem `vancado/vnc-events` Package.

## Warum eine eigene Kopie?

- **Problem**: Änderungen am ursprünglichen Template in `vendor/vancado/vnc-events/` werden bei `composer update` überschrieben
- **Lösung**: Eigene ContentBlock-Kopie in unserem Sitepackage, die nicht überschrieben wird

## Unterschiede zum Original

### Konfiguration (config.yaml)
- **Name**: `vnc-sitepackage/events` (statt `vnc/events`)
- **TypeName**: `vnc_events_custom` (statt `vnc_events`)
- Dadurch wird ein neuer ContentBlock-Typ erstellt, der parallel zum Original existiert

### Templates
- **Frontend-Template**: Zusätzliche CSS-Klassen für bessere Anpassbarkeit
  - `custom-events-container`
  - `custom-button`
  - `custom-events-carousel`
  - `no-events-message`
- **Event-Partial**: Verbesserte HTML-Struktur mit semantischen CSS-Klassen
- **Sprachdatei-Referenzen**: Zeigen auf unser Sitepackage statt auf das Original

### Sprachdateien
- Angepasste Labels zur Unterscheidung vom Original
- Titel: "Events (Custom)" / "Veranstaltungen (Angepasst)"

## Verwendung

1. **Cache leeren** nach der Erstellung
2. **ContentBlock ist verfügbar** unter "Events (Custom)" in der Content-Element-Auswahl
3. **Anpassungen vornehmen** in den Templates ohne Angst vor Überschreibung

## Anpassungen vornehmen

### Frontend-Template anpassen
```
packages/vnc_sitepackage/ContentBlocks/ContentElements/events/templates/frontend.html
```

### Event-Darstellung anpassen
```
packages/vnc_sitepackage/ContentBlocks/ContentElements/events/templates/partials/Events/Event.html
```

### CSS-Styling
Verwenden Sie die neuen CSS-Klassen:
- `.custom-events-container`
- `.custom-events-carousel`
- `.custom-event-item`
- `.event-title`, `.event-date`, `.event-time`, etc.

### JavaScript anpassen
```
packages/vnc_sitepackage/ContentBlocks/ContentElements/events/assets/frontend.js
```

## Wichtige Hinweise

- **Beide ContentBlocks** (Original und Custom) können parallel verwendet werden
- **Original bleibt unverändert** und wird weiterhin durch Composer-Updates aktualisiert
- **Custom-Version** ist vollständig unter Ihrer Kontrolle
- **ViewHelpers** des vnc-events Packages werden weiterhin verwendet (vnc:countEvents, vnc:getEvents)

## Nach Updates

Nach Updates des `vancado/vnc-events` Packages sollten Sie prüfen, ob neue Features oder Bugfixes in Ihre Custom-Version übernommen werden sollen.
