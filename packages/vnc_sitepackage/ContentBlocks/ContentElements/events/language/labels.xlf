<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file datatype="plaintext" original="labels.xlf" source-language="en" date="2025-05-26T09:49:28+00:00" product-name="vnc-sitepackage/events">
		<header/>
		<body>
			<trans-unit id="title" resname="title">
				<source>Events (Custom)</source>
			</trans-unit>
			<trans-unit id="description" resname="description">
				<source>Show event carousel (customized version)</source>
			</trans-unit>
			<trans-unit id="layout.label" resname="layout.label">
				<source>Layout</source>
			</trans-unit>
			<trans-unit id="layout.items.0.label" resname="layout.items.0.label">
				<source>Under each other</source>
			</trans-unit>
			<trans-unit id="layout.items.1.label" resname="layout.items.1.label">
				<source>Next to each other</source>
			</trans-unit>
			<trans-unit id="items_per_slide.label" resname="items_per_slide.label">
				<source>Items per slide</source>
			</trans-unit>
			<trans-unit id="items_per_slide.items.4.label" resname="items_per_slide.items.4.label">
				<source>4</source>
			</trans-unit>
			<trans-unit id="items_per_slide.items.3.label" resname="items_per_slide.items.3.label">
				<source>3</source>
			</trans-unit>
			<trans-unit id="items_per_slide.items.2.label" resname="items_per_slide.items.2.label">
				<source>2</source>
			</trans-unit>
			<trans-unit id="categories.label" resname="categories.label">
				<source>Categories</source>
			</trans-unit>
			<trans-unit id="listPid.label" resname="listPid.label">
				<source>Page with list view</source>
			</trans-unit>
			<trans-unit id="singlePid.label" resname="singlePid.label">
				<source>Page width detail view</source>
			</trans-unit>
			<trans-unit id="categories.properties" resname="categories.properties">
				<source>Event category properties</source>
			</trans-unit>
			<trans-unit id="icon" resname="icon">
				<source>Event category icon</source>
			</trans-unit>
		</body>
	</file>
</xliff>
