<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file datatype="plaintext" original="labels.xlf" source-language="en" date="2025-05-26T09:49:28+00:00" product-name="vnc-sitepackage/events">
		<header/>
		<body>
			<trans-unit id="title" approved="yes">
				<source>Events (Custom)</source>
				<target>Veranstaltungen (Angepasst)</target>
			</trans-unit>
			<trans-unit id="description" approved="yes">
				<source>Show event carousel (customized version)</source>
				<target>Anzeige von Veranstaltungen im Karussell (angepasste Version)</target>
			</trans-unit>
			<trans-unit id="layout.label" approved="yes">
				<source>Layout</source>
				<target>Darstellung</target>
			</trans-unit>
			<trans-unit id="layout.items.0.label" approved="yes">
				<source>Under each other</source>
				<target><PERSON><PERSON><PERSON> unter Text</target>
			</trans-unit>
			<trans-unit id="layout.items.1.label" approved="yes">
				<source>Next to each other</source>
				<target>Karussell neben Text</target>
			</trans-unit>
			<trans-unit id="items_per_slide.label" approved="yes">
				<source>Items per slide</source>
				<target>Anzahl pro Karussell-Slide</target>
			</trans-unit>
			<trans-unit id="items_per_slide.items.4.label" approved="yes">
				<source>4</source>
				<target>4</target>
			</trans-unit>
			<trans-unit id="items_per_slide.items.3.label" approved="yes">
				<source>3</source>
				<target>3</target>
			</trans-unit>
			<trans-unit id="items_per_slide.items.2.label" approved="yes">
				<source>2</source>
				<target>2</target>
			</trans-unit>
			<trans-unit id="categories.label" approved="yes">
				<source>Categories</source>
				<target>Kategorien</target>
			</trans-unit>
			<trans-unit id="listPid.label" approved="yes">
				<source>Page with list view</source>
				<target>Seite mit Listenansicht</target>
			</trans-unit>
			<trans-unit id="singlePid.label" approved="yes">
				<source>Page width detail view</source>
				<target>Seite mit Detailansicht</target>
			</trans-unit>
			<trans-unit id="categories.properties" approved="yes">
				<source>Event category properties</source>
				<target>Eigenschaften der Event-Kategorie</target>
			</trans-unit>
			<trans-unit id="icon" approved="yes">
				<source>Event category icon</source>
				<target>Icon für Event-Kategorie</target>
			</trans-unit>
		</body>
	</file>
</xliff>
