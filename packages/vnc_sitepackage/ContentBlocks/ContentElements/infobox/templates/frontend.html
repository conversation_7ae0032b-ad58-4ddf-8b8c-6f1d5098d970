<f:layout name="Default" />

<f:section name="Main">
    <section class="page-section">
      <div class="bg-(--styling-background-light)">
        <div class="container py-6 md:py-12">
            <div class="flex flex-col md:flex-row gap-4 md:gap-8 lg:gap-12">
                <f:if condition="{data.icon}">
                    <f:then>
                        <div>
                            <div class="h-12 !text-[2rem] {data.icon}"></div>
                        </div>
                    </f:then>
                </f:if>
                <div>
                    <h1 class="debug-marker">INFOBOX</h1>
                    <f:variable name="headerSize" value="m"></f:variable>
                    <f:variable name="embeddedTextPartial" value="true" />
                    <f:render partial="Text" arguments="{_all}"></f:render>
                </div>
            </div>
        </div>
      </div>
    </section>

</f:section>
