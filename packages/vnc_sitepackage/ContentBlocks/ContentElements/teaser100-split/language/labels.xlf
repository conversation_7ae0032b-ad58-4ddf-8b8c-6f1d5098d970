<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" original="labels.xlf" datatype="plaintext" product-name="vnc/teaser100-split" date="2025-06-13T15:18:36+02:00">
		<header></header>
		<body>
			<trans-unit id="title">
				<source>Image + Text Highlight</source>
			</trans-unit>
			<trans-unit id="description">
				<source>Full-width display of image and text (further content as carousel), e.g. for highlight topics.</source>
			</trans-unit>
			<trans-unit id="header.label">
				<source>Identifier (for information only)</source>
			</trans-unit>
			<trans-unit id="items.label">
				<source>Teaser</source>
			</trans-unit>
			<trans-unit id="items.header.label">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="items.subheader.label">
				<source>Sub Headline</source>
			</trans-unit>
			<trans-unit id="items.bodytext.label">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="items.link.label">
				<source>Link</source>
			</trans-unit>
			<trans-unit id="items.linktext.label">
				<source>Linktext</source>
			</trans-unit>
			<trans-unit id="items.image.label">
				<source>image</source>
			</trans-unit>
			<trans-unit id="format.label">
				<source>Image Format</source>
			</trans-unit>
			<trans-unit id="format.items.upright.label">
				<source>Upright (3:4)</source>
			</trans-unit>
			<trans-unit id="format.items.square.label">
				<source>Square (1:1)</source>
			</trans-unit>
			<trans-unit id="format.items.classicscreen.label">
				<source>Classicscreen (5:4)</source>
			</trans-unit>
			<trans-unit id="format.items.television.label">
				<source>Television (4:3)</source>
			</trans-unit>
			<trans-unit id="format.items.default.label">
				<source>Photo (3:2)</source>
			</trans-unit>
			<trans-unit id="vnc_content_layout.label">
				<source>Layout</source>
			</trans-unit>
			<trans-unit id="vnc_content_layout.items.left.label">
				<source>Image Left</source>
			</trans-unit>
			<trans-unit id="vnc_content_layout.items.right.label">
				<source>Image Right</source>
			</trans-unit>
			<trans-unit id="items.headline.label">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="items.subheadline.label">
				<source>Sub Headline</source>
			</trans-unit>
			<trans-unit id="format.items.widescreen.label">
				<source>Widescreen (16:9)</source>
			</trans-unit>
			<trans-unit id="format.items.ultrawide.label">
				<source>Ultrawide (3:1)</source>
			</trans-unit>
			<trans-unit id="format.items.univisium.label">
				<source>Univisium (2:1)</source>
			</trans-unit>
			<trans-unit id="format.items.wide.label">
				<source>Wide (5:2)</source>
			</trans-unit>
		</body>
	</file>
</xliff>
