<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" target-language="de" original="" datatype="plaintext" date="2025-06-13T15:18:36+02:00">
		<body>
			<trans-unit id="title">
				<source>Image + Text Highlight</source>
				<target>Image + Text Highlight</target>
			</trans-unit>
			<trans-unit id="description">
				<source>Full-width display of image and text (further content as carousel), e.g. for highlight topics.</source>
				<target>Vollflächige Darstellung aus Bild und Text (weitere Inhalte als Carousel), z.B. für Highlight-Themen.</target>
			</trans-unit>
			<trans-unit id="header.label">
				<source>Identifier (for information only)</source>
				<target>Bezeichner (auf der Webseite nicht sichtbar)</target>
			</trans-unit>
			<trans-unit id="items.label">
				<source>Teaser</source>
				<target>Teaser</target>
			</trans-unit>
			<trans-unit id="items.header.label">
				<source>Headline</source>
				<target>Überschrift</target>
			</trans-unit>
			<trans-unit id="items.subheader.label">
				<source>Sub Headline</source>
				<target>Unter-Überschrift</target>
			</trans-unit>
			<trans-unit id="items.bodytext.label">
				<source>Text</source>
				<target>Text</target>
			</trans-unit>
			<trans-unit id="items.link.label">
				<source>Link</source>
				<target>Link</target>
			</trans-unit>
			<trans-unit id="items.linktext.label">
				<source>Linktext</source>
				<target>Linktext</target>
			</trans-unit>
			<trans-unit id="items.image.label">
				<source>image</source>
				<target>Bild</target>
			</trans-unit>
			<trans-unit id="format.label">
				<source>Image Format</source>
				<target>Seitenverhältnis der Bilder</target>
			</trans-unit>
			<trans-unit id="format.items.upright.label">
				<source>Upright (3:4)</source>
				<target>Hochkant (3:4)</target>
			</trans-unit>
			<trans-unit id="format.items.square.label">
				<source>Square (1:1)</source>
				<target>Quadratisch (1:1)</target>
			</trans-unit>
			<trans-unit id="format.items.classicscreen.label">
				<source>Classicscreen (5:4)</source>
				<target>Klassischer Monitor (5:4)</target>
			</trans-unit>
			<trans-unit id="format.items.television.label">
				<source>Television (4:3)</source>
				<target>Klassischer Fernseher (4:3)</target>
			</trans-unit>
			<trans-unit id="format.items.default.label">
				<source>Photo (3:2)</source>
				<target>Foto (3:2)</target>
			</trans-unit>
			<trans-unit id="vnc_content_layout.label">
				<source>Layout</source>
				<target>Darstellung</target>
			</trans-unit>
			<trans-unit id="vnc_content_layout.items.left.label">
				<source>Image Left</source>
				<target>Bild links</target>
			</trans-unit>
			<trans-unit id="vnc_content_layout.items.right.label">
				<source>Image Right</source>
				<target>Bild rechts</target>
			</trans-unit>
			<trans-unit id="items.headline.label">
				<source>Headline</source>
				<target>Überschrift</target>
			</trans-unit>
			<trans-unit id="items.subheadline.label">
				<source>Sub Headline</source>
				<target>Unter-Überschrift</target>
			</trans-unit>
			<trans-unit id="format.items.widescreen.label">
				<source>Widescreen (16:9)</source>
				<target>Breitbild (16:9)</target>
			</trans-unit>
			<trans-unit id="format.items.ultrawide.label">
				<source>Ultrawide (3:1)</source>
				<target>Ultrawide (3:1)</target>
			</trans-unit>
			<trans-unit id="format.items.univisium.label">
				<source>Univisium (2:1)</source>
				<target>Univisium (2:1)</target>
			</trans-unit>
			<trans-unit id="format.items.wide.label">
				<source>Wide (5:2)</source>
				<target>Wide (5:2)</target>
			</trans-unit>
		</body>
	</file>
</xliff>
