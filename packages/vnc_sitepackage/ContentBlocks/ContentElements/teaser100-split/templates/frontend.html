<f:layout name="Default" />

<f:section name="Main">
    <f:variable name="carouselType" value="tiles"></f:variable>
    <f:variable name="showStones" value="true"></f:variable>
    <f:variable name="sliderContent">
        <f:for each="{data.items}" as="teaser">
            <f:render section="teaser" arguments="{_all}" />
        </f:for>
    </f:variable>

    <section class="page-section">
        <div class="container-xxxl">
            <div class="row">
                <div class="col-12">
                    <f:render partial="Carousel" arguments="{_all}"></f:render>
                </div>
            </div>
        </div>
    </section>
</f:section>

<f:section name="teaser">
    <div class="carousel__item splide__slide">
        <f:variable
            name="record"
            value="{ data: teaser, files: { 0: teaser.image.0} }"
        ></f:variable>
        <h1 class="debug-marker">TEASER 100 SPLIT</h1>
        <f:variable name="teaserContentBackground" value="default"></f:variable>
        <f:variable name="teaserContentLayout" value="half-boxed"></f:variable>
        <f:variable
            name="teaserContentPosition"
            value="{data.vnc_content_layout}"
        ></f:variable>

        <f:render partial="Teaser" arguments="{_all}" />
    </div>
</f:section>
