<f:comment>
    <f:asset.css
        identifier="content-block-css-vnc-vnc/counter"
        file="Frontend.css"
    />
    <f:asset.script
        identifier="content-block-js-vnc-vnc/counter"
        file="Frontend.js"
    />
</f:comment>

<f:layout name="Default" />
<f:section name="Main">
    <!-- <section class="vnccounters page-section mb-5 mb-lg-8 px-0" id="c373"> -->
    <div class="bg-(--styling-background-light) py-4">
        <div class="container">
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-none lg:flex gap-4">
                <f:alias map="{l: '{', r: '}'}">
                    <f:variable name="iconexists" value="0" />
                    <f:for each="{data.counters}" as="counter">
                        <f:if condition="{counter.icon}">
                            <f:then>
                                <f:variable name="iconexists" value="1" />
                            </f:then>
                        </f:if>
                    </f:for>
                    <f:for each="{data.counters}" as="counter">
                        <div class="flex flex-col gap-2 items-center">
                            <f:if condition="{counter.icon}">
                                <f:then>
                                    <div
                                        class="h-12 !text-[3rem] {counter.icon}"
                                    ></div>
                                </f:then>
                                <f:else>
                                    <f:if condition="{iconexists}">
                                        <div class="h-12"></div>
                                    </f:if>
                                </f:else>
                            </f:if>
                            <p class="headline-l whitespace-nowrap">
                                {counter.prefix}
                                <span
                                    data-animated-counter='{l}"from":{counter.number_from},"to":{counter.number_to},"duration":{counter.duration},"locale":"de"{r}'
                                >
                                    {counter.number_to}
                                </span>
                                {counter.suffix}
                            </p>
                            <f:if condition="{counter.headline}">
                                <f:then>
                                    <p class="headline-s">{counter.headline}</p>
                                </f:then>
                            </f:if>
                            <f:if condition="{counter.description}">
                                <f:then>
                                    <p class="text-s">{counter.description}</p>
                                </f:then>
                            </f:if>
                        </div>
                    </f:for>
                </f:alias>
            </div>
        </div>
    </div>
</f:section>
