name: vnc/counter
title: Animated Counter
description: 'Animated Counter Element'
group: vnccontent
prefixFields: true
prefixType: full
fields:
  -
    identifier: header
    useExistingField: true
    label: 'Description (not displayed in Frontend)'
  #-
  #  identifier: header_layout
  #  useExistingField: true
  #  label: "Header Type"
  #-
  #  identifier: bodytext
  #  useExistingField: true
  #  label: "Intro Text"
  #  richtextConfiguration: 'VncContentBasic'
  - identifier: counters
    label: "Counters"
    type: Collection
    labelField: description
    minitems: 1
    maxitems: 6
    appearance:
      collapseAll: true
      levelLinksPosition: both
    fields:
      - identifier: icon
        label: "Icon"
        required: false
        type: Text
        renderType: selectIcon
        fieldControl:
          0:
            iconset-type: nucleo
            iconset-path: EXT:vnc_icon_formelement/Resources/Public/Libraries/Nucleo
      - identifier: headline
        required: false
        type: Text
        label: "Headline"
      - identifier: description
        required: false
        type: Text
        label: "Description"
      - identifier: duration
        required: true
        type: Number
        label: "Duration"
        description: "Duration in milliseconds (seconds * 1000), default is 2 seconds (2000), min: 0, max:  5000"
        default: 2000
        range:
          lower: 0
          upper: 5000
      - identifier: counter_palette
        type: Palette
        label: "Counter"
        fields:
          - identifier: prefix
            type: Text
            label: "Prefix"
            max: 3
            size: 3
            required: false
          - identifier: number_from
            type: Number
            label: "Start value"
            size: 10
            required: true
          - identifier: number_to
            type: Number
            label: "End value"
            size: 10
            required: true
          - identifier: suffix
            type: Text
            label: "Suffix"
            max: 3
            size: 3
            required: false
  - identifier: space_after_class
    type: Select
    useExistingField: true
