<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" target-language="de" original="" datatype="plaintext" date="2025-06-13T15:16:57+02:00">
		<body>
			<trans-unit id="title">
				<source>Counter</source>
				<target>Counter</target>
			</trans-unit>
			<trans-unit id="description">
				<source>Animated numbers (with prefix/suffix) with texts and icons, e.g. for sales figures.</source>
				<target>Animierte Zahlen (mit Präfix/Suffix) mit Texten und Icons, z.B. für Umsatzangaben.</target>
			</trans-unit>
			<trans-unit id="header.label">
				<source>Description (not displayed in Frontend)</source>
				<target>Beschreibung (wird nicht auf der Seite dargestellt)</target>
			</trans-unit>
			<trans-unit id="counters.label">
				<source>Counters</source>
				<target><PERSON><PERSON><PERSON></target>
			</trans-unit>
			<trans-unit id="counters.icon.label">
				<source>Icon</source>
				<target>Icon</target>
			</trans-unit>
			<trans-unit id="counters.description.label">
				<source>Description</source>
				<target>Beschreibung</target>
			</trans-unit>
			<trans-unit id="counters.duration.label">
				<source>Duration</source>
				<target>Dauer</target>
			</trans-unit>
			<trans-unit id="counters.duration.description">
				<source>Duration in milliseconds (seconds * 1000), default is 2 seconds</source>
				<target>Dauer in Millisekunden (Sekunden * 1000), Standardwert ist 2 Sekunden (2000), Min: 0, Max: 5000</target>
			</trans-unit>
			<trans-unit id="counters.palettes.counter_palette.label">
				<source>Counter</source>
				<target>Zähler</target>
			</trans-unit>
			<trans-unit id="counters.prefix.label">
				<source>Prefix</source>
				<target>Präfix</target>
			</trans-unit>
			<trans-unit id="counters.number_from.label">
				<source>Start value</source>
				<target>Startwert</target>
			</trans-unit>
			<trans-unit id="counters.number_to.label">
				<source>End value</source>
				<target>Endwert</target>
			</trans-unit>
			<trans-unit id="counters.suffix.label">
				<source>Suffix</source>
				<target>Suffix</target>
			</trans-unit>
			<trans-unit id="header_layout.label">
				<source>Header Type</source>
				<target>Überschriftstyp</target>
			</trans-unit>
			<trans-unit id="bodytext.label">
				<source>Intro Text</source>
				<target>Intro Text</target>
			</trans-unit>
		</body>
	</file>
</xliff>
