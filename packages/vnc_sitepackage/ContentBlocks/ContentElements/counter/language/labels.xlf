<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" original="labels.xlf" datatype="plaintext" product-name="vnc/counter" date="2025-06-13T15:16:57+02:00">
		<header></header>
		<body>
			<trans-unit id="title">
				<source>Counter</source>
			</trans-unit>
			<trans-unit id="description">
				<source>Animated numbers (with prefix/suffix) with texts and icons, e.g. for sales figures.</source>
			</trans-unit>
			<trans-unit id="header.label">
				<source>Description (not displayed in Frontend)</source>
			</trans-unit>
			<trans-unit id="counters.label">
				<source>Counters</source>
			</trans-unit>
			<trans-unit id="counters.icon.label">
				<source>Icon</source>
			</trans-unit>
			<trans-unit id="counters.headline.label">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="counters.description.label">
				<source>Description</source>
			</trans-unit>
			<trans-unit id="counters.duration.label">
				<source>Duration</source>
			</trans-unit>
			<trans-unit id="counters.duration.description">
				<source>Duration in milliseconds (seconds * 1000), default is 2 seconds</source>
			</trans-unit>
			<trans-unit id="counters.palettes.counter_palette.label">
				<source>Counter</source>
			</trans-unit>
			<trans-unit id="counters.prefix.label">
				<source>Prefix</source>
			</trans-unit>
			<trans-unit id="counters.number_from.label">
				<source>Start value</source>
			</trans-unit>
			<trans-unit id="counters.number_to.label">
				<source>End value</source>
			</trans-unit>
			<trans-unit id="counters.suffix.label">
				<source>Suffix</source>
			</trans-unit>
			<trans-unit id="header_layout.label">
				<source>Header Type</source>
			</trans-unit>
			<trans-unit id="bodytext.label">
				<source>Intro Text</source>
			</trans-unit>
		</body>
	</file>
</xliff>
