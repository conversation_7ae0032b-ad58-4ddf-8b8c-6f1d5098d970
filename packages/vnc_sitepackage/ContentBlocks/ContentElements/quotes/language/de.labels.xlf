<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" target-language="de" original="" datatype="plaintext" date="2025-06-13T15:15:16+02:00">
		<body>
			<trans-unit id="title">
				<source>Quote</source>
				<target>Quote</target>
			</trans-unit>
			<trans-unit id="description">
				<source>Citation with author and image (further content as carousel).</source>
				<target>Zitat mit Autor:innenangabe und Bild (weitere Inhalte als Carousel).</target>
			</trans-unit>
			<trans-unit id="header.label">
				<source>Identifier (for information only)</source>
				<target>Name (auf der Webseite nicht sichtbar)</target>
			</trans-unit>
			<trans-unit id="items.label">
				<source>Quote element</source>
				<target>Zitat Element</target>
			</trans-unit>
			<trans-unit id="items.bodytext.label">
				<source>Quote</source>
				<target>Zitat</target>
			</trans-unit>
			<trans-unit id="items.author.label">
				<source>Author</source>
				<target>Autor</target>
			</trans-unit>
			<trans-unit id="items.image.label">
				<source>image</source>
				<target>Bild</target>
			</trans-unit>
		</body>
	</file>
</xliff>
