
<f:layout name="Default"/>

<f:section name="Main">
    <f:variable name="carouselType" value="tiles"></f:variable>
    <f:variable name="carouselOptions" value="{
                            &quot;perPage&quot;:&quot;1&quot;,
                            &quot;breakpoints&quot;:{&quot;480&quot;:{&quot;perPage&quot;:1}},
                            &quot;gap&quot;: &quot;1.5rem&quot;,
                            &quot;arrows&quot;: true
                            }">

    </f:variable>

    <section class="page-section">
        <div class="container-small">
            <div
                class="carousel {f:if(condition: '{carouselType}', then: 'carousel--{carouselType}')}"
                aria-roledescription="carousel"
            >
                <div class="carousel__canvas splide" aria-label="{data.header}" data-vnc-carousel="{carouselOptions -> f:format.raw()}" tabindex="0" role="region" 
                  aria-roledescription="carousel" 
                  aria-label="Quotes Carousel">
                    <div class="splide__track">
                        <ul class="splide__list">
                              <f:for each="{data.items}" as="data">
                                <f:render section="cite" arguments="{_all}" />
                            </f:for>
                        </ul>
                    </div>
                    <f:render section="carouselControls" />
                </div>
            </div>
        </div>
    </section>
</f:section>
</html>

<f:section name="cite">
    <figure class="splide__slide flex flex-col items-center justify-center gap-8">
      <f:variable name="file" value="{data.image.0}"/>
      <f:variable name="files" value="{data.images.0}"/>
      <f:if condition="{file}">
          <f:variable name="cropVariant">square</f:variable>
          <f:variable name="sizes" value="{0:'sm'}" />
          <f:variable name="mediaWrapperClassName" value="relative"/>
          <f:variable name="imageClassName">rounded-full w-[220px] h-[220px]  object-cover</f:variable>
          <f:render partial="Media/Media" arguments="{_all}"></f:render>
      </f:if>
      <blockquote cite="{data.author}" class="text-l text-center"><p>„{data.bodytext -> f:format.nl2br()}“</p></blockquote>
      <figcaption>
        <cite class="text-xs not-italic">{data.author}</cite>
      </figcaption>
    </figure>
</f:section>

<f:section name="carouselControls">
        <div class="flex items-center justify-center gap-4 splide__arrows">
            <button
                type="button"
                role="button"
                class="splide__arrow--prev "
                data-table-scroll="Zurück"
                aria-label="Zurück"
            >
                <svg class="w-full h-full fill-current" preserveAspectRatio="xMaxYMin"  viewBox="0 0 33 32">
                  <path d="M23.3 15.399c-0.060-0.080-0.12-0.14-0.2-0.2l-12-9c-0.44-0.33-1.070-0.24-1.4 0.2-0.13 0.17-0.2 0.38-0.2 0.6v18c0 0.55 0.45 1 1 1 0.22 0 0.43-0.070 0.6-0.2l12-9c0.44-0.33 0.53-0.96 0.2-1.4z" ></path>
                </svg>
            </button>
            <ul class="splide__pagination"></ul>
            
            <button
                type="button"
                role="button"
                class="splide__arrow--next"
                data-table-scroll="end"
            >
                <svg class="w-full h-full fill-current"  preserveAspectRatio="xMaxYMin"  viewBox="0 0 33 32">
                  <path class="w-full h-full" d="M23.3 15.399c-0.060-0.080-0.12-0.14-0.2-0.2l-12-9c-0.44-0.33-1.070-0.24-1.4 0.2-0.13 0.17-0.2 0.38-0.2 0.6v18c0 0.55 0.45 1 1 1 0.22 0 0.43-0.070 0.6-0.2l12-9c0.44-0.33 0.53-0.96 0.2-1.4z" ></path>
                </svg>
            </button>
        </div>
    </f:section>

