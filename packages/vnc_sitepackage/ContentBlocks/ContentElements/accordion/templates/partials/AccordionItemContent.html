<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>

<div class="overflow-y-hidden">
    <div class="pt-2">
        <article class="flex flex-col gap-(--global-spacer-components-m)">
            <f:format.html>{item.bodytext}</f:format.html>
            <f:if condition="{item.image}">
                <f:then>
                    <f:for each="{item.image}" as="file" iteration="fileIteration">
                        <p>
                            <f:render partial="Media/Media" arguments="{file: file, data: item.image, settings: settings,cropVariant: cropVariant}" />
                        </p>
                    </f:for>
                </f:then>
                <f:else>
                    <f:comment><!-- no files in this record --></f:comment>
                </f:else>
            </f:if>
        </article>
    </div>
</div>

</html>
