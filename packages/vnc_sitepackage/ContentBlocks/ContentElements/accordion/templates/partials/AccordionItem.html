<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
<div
    class="border-b first:border-t p-4"
    data-accordion-item
    >
    <h3 class="headline-xs">
        <button
            type="button"
            class="cursor-pointer flex items-center justify-between w-full text-left"
            id="accordion-toggle-{uid}-{count}"
            aria-expanded="{f:if(condition: '{collapse}', then: 'false', else: 'true')}"
            aria-controls="accordion-body-{uid}-{count}"
            data-accordion-toggle
            >
            {item.header}
            <span class="w-8 h-8 svg-icon transition-transform duration-300">
                <svg data-accordion-icon class="icon transition-all duration-700 w-full h-full {f:if(condition: '{collapse}', then: '', else: 'rotate-180')}" preserveAspectRatio="xMaxYMin">
                    <use xlink:href="{f:uri.resource(path: 'EXT:vnc_sitepackage/Resources/Public/dist/icons.svg#icon-chevron-down')}"></use>
                </svg>
            </span>
        </button>
    </h3>
    <f:if condition="{collapse}">
        <f:then>
            <f:variable name="hiddenAttr" value="hidden"/>
        </f:then>
        <f:else>
            <f:variable name="hiddenAttr" value="true"/>
        </f:else>
    </f:if>
    <div
        class="grid grid-rows-[0fr] transition-all duration-700 ease-in-out {f:if(condition: '{collapse}', then: ' ')}"
        role="region" 
        data-accordion-panel
        id="accordion-panel-{uid}-{count}"
        aria-labelledby="accordion-toggle-{uid}-{count}"
        {hiddenAttr}
        >
        <f:render partial="AccordionItemContent" arguments="{_all}"></f:render>
    </div>
</div>

</html>


