<f:comment><!--
<cb:asset.css identifier="content-block-css-vnc-vnc/accordion" file="Frontend.css"/>
<cb:asset.script identifier="content-block-js-vnc-vnc/accordion" file="Frontend.js"/>
--></f:comment>
<f:layout name="Default"/>
<f:section name="Main">

<f:if condition="{data.layout}==1">
  <f:variable
      name="wrapperClass"
      value="max-w-[920px] mx-auto flex flex-col gap-4 md:gap-6"
  ></f:variable>
  <f:variable
      name="contentClass"
      value=" flex flex-col gap-(--global-spacer-components-m)"
  ></f:variable>
</f:if>
<f:if condition="{data.layout}==2 or {data.layout}==3">
  <f:variable
      name="wrapperClass"
      value="flex flex-col-reverse md:grid md:grid-cols-2 gap-4 md:gap-6"
  ></f:variable>
  <f:variable
  name="contentClass"
  value="md:order-2 flex flex-col gap-4 md:gap-6"
></f:variable>
</f:if>
<f:if condition="{data.layout}==4">
  <f:variable
      name="wrapperClass"
      value="flex flex-col gap-4 "
  ></f:variable>
  <f:variable
      name="contentClass"
      value="flex flex-col md:grid md:grid-cols-2 gap-(--global-spacer-components-m) md:gap-6"
  ></f:variable>
</f:if>

<div class="container">
  <div id="c{data.uid}" class="{wrapperClass}">


    <div class="{contentClass}">
      <div class="flex flex-col gap-content">
        <f:if condition="{data.header}">
            <p class="headline headline-l text-primary">
                <f:format.raw>{data.header}</f:format.raw>
            </p>
        </f:if>
        <f:if condition="{data.bodytext}">
            <f:format.html>{data.bodytext}</f:format.html>
        </f:if>
      </div>

      
      <f:if condition="{data.items}">
          <f:render partial="Accordion" arguments="{_all}" />
      </f:if>

    </div>

    <div class="lg:order-1 [&>picture>img]:w-full">
      <f:if condition="{data.layout}==2 or {data.layout}==3">
        <f:then>
          <f:for each="{data.image}" as="image" iteration="imageIteration">
              <f:if condition="{data.layout}==2">
                  <f:then>
                      <f:alias map="{cropVariant: 'square'}">
                          <f:render partial="Media/Type/Image" arguments="{_all}" />
                      </f:alias>
                  </f:then>
                  <f:else>
                      <f:alias map="{cropVariant: 'upright'}">
                          <f:render partial="Media/Type/Image" arguments="{_all}" />
                      </f:alias>
                  </f:else>
              </f:if>
          </f:for>
        </f:then>
        
      </f:if>
    </div>

  </div>
</div>



</div>
<div class="container hidden">
    <f:if condition="{data.layout}>1">
        <f:then>
            <f:comment><!-- Renders accordion with image left --></f:comment>
            <f:render section="one-image-left" arguments="{_all}"/>
        </f:then>
        <f:else>
            <f:comment><!-- Renders accordion w/o image --></f:comment>
            <f:render section="no-image" arguments="{_all}"/>
        </f:else>
    </f:if>
</div>
</f:section>

<f:section name="no-image">
    <f:comment><!--

        ELEMENT AKKORDION - wrapper

    --></f:comment>
    <div id="c{data.uid}" class="row {f:if(condition:'{data.space_after_class}', then: '{data.space_after_class}')}">
        <div class="max-w-[920px] mx-auto flex flex-col gap-content">
            <f:if condition="{data.header}">
                <f:if condition="{data.header}">
                    <f:variable name="className">headline-l</f:variable>
                    <f:render partial="Header" arguments="{_all}"/>
                </f:if>
            </f:if>
            <f:if condition="{data.bodytext}">
                <f:format.html>{data.bodytext}</f:format.html>
            </f:if>
            <f:if condition="{data.items}">
                <f:render partial="Accordion" arguments="{_all}" />
            </f:if>
        </div>
    </div>
</f:section>

<f:section name="one-image-left">
    <f:comment><!--

        ELEMENT AKKORDION with IMAGE - wrapper

    --></f:comment>
    <div class="grid grid-cols-12 gap-default">
        <div 
            class="{f:if(condition:'{data.layout}==2', then: 'col-span-12 md:col-span-6', else: 'col-span-12 md:col-span-6')}">
            <f:for each="{data.image}" as="image" iteration="imageIteration">
                <f:if condition="{data.layout}==2">
                    <f:then>
                        <f:alias map="{cropVariant: 'square'}">
                            <f:render partial="Media/Type/Image" arguments="{_all}" />
                        </f:alias>
                    </f:then>
                    <f:else>
                        <f:alias map="{cropVariant: 'upright'}">
                            <f:render partial="Media/Type/Image" arguments="{_all}" />
                        </f:alias>
                    </f:else>
                </f:if>
            </f:for>
        </div>
        <div class="flex flex-col gap-content {f:if(condition:'{data.layout}==2', then: 'col-span-12 md:col-span-6', else: 'col-span-12 md:col-span-6')}">
            <f:if condition="{data.header}">
                <p class="headline headline-l text-primary">
                    <f:format.raw>{data.header}</f:format.raw>
                </p>
            </f:if>
            <f:if condition="{data.bodytext}">
                <f:format.html>{data.bodytext}</f:format.html>
            </f:if>
            <f:if condition="{data.items}">
                <f:render partial="Accordion" arguments="{_all}" />
            </f:if>
        </div>
    </div>
</f:section>
