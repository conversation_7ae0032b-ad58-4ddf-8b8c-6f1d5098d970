<f:asset.css identifier="content-block-vnc-vnc-accordion-be" href="{cb:assetPath()}/EditorPreview.css" />
<section>
    <div class="container-fluid">
        <div class="row">
            <div class="col-6">
                <f:sanitize.html>{data.bodytext}</f:sanitize.html>
            </div>
            <div class="col-6">
                <div class="accordion" id="accordion{data.uid}">
                    <f:if condition="{data.items}">
                        <f:for each="{data.items}" as="tab" iteration="recordIterator">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading-{tab.uid}">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-{tab.uid}" aria-expanded="true" aria-controls="collapse-{tab.uid}">
                                        <f:sanitize.html>{tab.header}</f:sanitize.html>
                                    </button>
                                </h2>
                                <f:comment><!--
									<div id="collapse-{tab.uid}" class="accordion-collapse collapse show" aria-labelledby="heading-{tab.uid}" data-parent="#accordion{data.uid}">
										<div class="accordion-body">
											<f:if condition="{tab.files}">
												<div class="col-sm-12">
													<f:for each="{tab.files}" as="file">
														<f:switch expression="{file.originalFile.properties.type}">
															<f:case value="4">
																<f:comment>Youtube</f:comment>
																<figure class="video">
																	<div class="embed embed-responsive embed-responsive-16by9">
																		<f:media class="embed-responsive-item" file="{file}" alt="{file.properties.alternative}" title="{file.properties.title}" />
																	</div>
																</figure>
															</f:case>
															<f:defaultCase>

																<f:image image="{file}" maxWidth="250" />

															</f:defaultCase>
														</f:switch>
													</f:for>
												</div>
											</f:if>
											<f:sanitize.html>{tab.bodytext}</f:sanitize.html>
										</div>
									</div>
									--> </f:comment>
                            </div>
                        </f:for>
                    </f:if>
                </div>
            </div>
        </div>
    </div>
</section>
