<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" original="labels.xlf" datatype="plaintext" product-name="vnc/accordion" date="2025-06-13T15:23:26+02:00">
		<header></header>
		<body>
			<trans-unit id="title">
				<source>Accordion</source>
			</trans-unit>
			<trans-unit id="description">
				<source>1 or 2-column display as accordion with intro and optional image, e.g. for FAQs.</source>
			</trans-unit>
			<trans-unit id="header.label">
				<source>Header</source>
			</trans-unit>
			<trans-unit id="header_layout.label">
				<source>Header Type</source>
			</trans-unit>
			<trans-unit id="bodytext.label">
				<source>Intro Text</source>
			</trans-unit>
			<trans-unit id="layout.label">
				<source>Layout</source>
			</trans-unit>
			<trans-unit id="layout.items.1.label">
				<source>Without Image</source>
			</trans-unit>
			<trans-unit id="layout.items.2.label">
				<source>With Image square left</source>
			</trans-unit>
			<trans-unit id="layout.items.3.label">
				<source>With Image portrait left</source>
			</trans-unit>
			<trans-unit id="layout.items.4.label">
				<source>With Teaser Text left</source>
			</trans-unit>
			<trans-unit id="image.label">
				<source>Image</source>
			</trans-unit>
			<trans-unit id="items.label">
				<source>items</source>
			</trans-unit>
			<trans-unit id="items.header.label">
				<source>Header</source>
			</trans-unit>
			<trans-unit id="items.bodytext.label">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="items.image.label">
				<source>image/Video</source>
			</trans-unit>
		</body>
	</file>
</xliff>
