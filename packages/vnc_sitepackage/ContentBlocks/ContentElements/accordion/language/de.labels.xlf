<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" target-language="de" original="" datatype="plaintext" date="2025-06-13T15:23:26+02:00">
		<body>
			<trans-unit id="title">
				<source>Accordion</source>
				<target>Accordion</target>
			</trans-unit>
			<trans-unit id="description">
				<source>1 or 2-column display as accordion with intro and optional image, e.g. for FAQs.</source>
				<target>1- oder 2-spaltige Darstellung als Akkordeon mit Intro und optionalem Bild, z.B. für FAQs.</target>
			</trans-unit>
			<trans-unit id="header.label">
				<source>Header</source>
				<target>Überschrift</target>
			</trans-unit>
			<trans-unit id="header_layout.label">
				<source>Header Type</source>
				<target>Typ der Überschrift</target>
			</trans-unit>
			<trans-unit id="bodytext.label">
				<source>Intro Text</source>
				<target>Intro Text</target>
			</trans-unit>
			<trans-unit id="layout.label">
				<source>Layout</source>
				<target>Layout</target>
			</trans-unit>
			<trans-unit id="layout.items.1.label">
				<source>Without Image</source>
				<target>Ohne Bild</target>
			</trans-unit>
			<trans-unit id="layout.items.2.label">
				<source>With Image square left</source>
				<target>Mit Bild quadratisch links</target>
			</trans-unit>
			<trans-unit id="layout.items.3.label">
				<source>With Image portrait left</source>
				<target>Mit Bild hochformtig links</target>
			</trans-unit>
			<trans-unit id="layout.items.4.label">
				<source>With Teaser Text left</source>
				<target>Mit Teaser links</target>
			</trans-unit>
			<trans-unit id="image.label">
				<source>Image</source>
				<target>Bild</target>
			</trans-unit>
			<trans-unit id="items.label">
				<source>items</source>
				<target>Elemente</target>
			</trans-unit>
			<trans-unit id="items.header.label">
				<source>Header</source>
				<target>Überschrift</target>
			</trans-unit>
			<trans-unit id="items.bodytext.label">
				<source>Text</source>
				<target>Text</target>
			</trans-unit>
			<trans-unit id="items.image.label">
				<source>image/Video</source>
				<target>Bild/Video</target>
			</trans-unit>
		</body>
	</file>
</xliff>
