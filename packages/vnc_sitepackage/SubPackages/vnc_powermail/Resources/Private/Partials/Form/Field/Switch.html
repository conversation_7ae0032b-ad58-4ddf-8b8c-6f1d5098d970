<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<div class="input--radio-group powermail_fieldwrap powermail_fieldwrap_type_check powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
	<f:render partial="Form/FieldLabel" arguments="{_all}" />

	<div class="{settings.styles.framework.fieldWrappingClasses}">
		<f:for each="{field.modifiedSettings}" as="setting" iteration="index">
			<div class="{settings.styles.framework.switchClasses} {vh:Validation.ErrorClass(field:field, class:'powermail_field_error')}">

					<f:form.checkbox
							property="{field.marker}."
							value="{setting.value}"
							checked="{vh:Misc.PrefillMultiField(field:field, mail:mail, cycle:index.cycle)}"
							id="powermail_field_{field.marker}_{index.cycle}"
							additionalAttributes="{vh:Validation.ValidationDataAttribute(field:field, iteration:index)}"
							class="powermail_checkbox powermail_checkbox_{field.uid} custom-control-input" />
				<label class="custom-control-label small d-flex align-items-center" for="powermail_field_{field.marker}_{index.cycle}">
					<vh:string.escapeLabels>{setting.label}</vh:string.escapeLabels>
				</label>
			</div>
		</f:for>

		<f:if condition="{settings.validation.client}">
			<div class="powermail_field_error_container powermail_field_error_container_{field.marker}"></div>
		</f:if>
	</div>
</div>
