<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<div class="powermail_fieldwrap powermail_fieldwrap_type_input powermail_fieldwrap_type_inputnumeric--anzahl powermail_fieldwrap_{field.marker} <f:if condition="{field.css} == 'col-sm-4-4'"><f:then>col-sm-4</f:then><f:else>{field.css}</f:else></f:if> {settings.styles.framework.fieldAndLabelWrappingClasses} form-group">
    <label for="powermail_field_{field.marker}" class="{settings.styles.framework.labelClasses} <f:if condition="{field.txGwpowermailHidelabel} == 1"> hide </f:if>" title="{field.description}">
    <f:format.raw>{field.title}</f:format.raw><f:if condition="{field.mandatory}"><span class="mandatory">*</span></f:if>
    <f:if condition="{field.description}"><span class="powermail-description"><f:format.html parseFuncTSPath="lib.parseFunc">{field.description}</f:format.html></span></f:if>
    </label>
  <div class="{settings.styles.framework.fieldWrappingClasses} input-group">
    <f:form.textfield
        type="number"
		maxlength="2"
        property="{field.marker}"
        placeholder="{field.placeholder}"
        value="{vh:misc.prefillField(field:field, mail:mail)}"
        class="powermail_input_field {settings.styles.framework.fieldClasses} {vh:validation.errorClass(field:field, class:'powermail_field_error')} form-control"
        additionalAttributes="{vh:validation.validationDataAttribute(field:field)}"
        id="powermail_field_{field.marker}" />
	   </div>
    <f:if condition="{field.txGwPowermailDescription2}">
        <span class="powermail-description powermail-description2"><f:format.html parseFuncTSPath="lib.parseFunc">{field.txGwPowermailDescription2}</f:format.html></span>
    </f:if>
</div>
