<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <div
        class="input--radio-group flex flex-col gap-2 powermail_fieldwrap powermail_fieldwrap_type_radio powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}"
    >
        <f:alias map="{noForAttribute: '1'}">
            <f:render partial="Form/FieldLabel" arguments="{_all}" />
        </f:alias>
        <div
            class="{settings.styles.framework.fieldWrappingClasses} flex flex-wrap gap-4"
        >
            <f:for
                each="{field.modifiedSettings}"
                as="setting"
                iteration="index"
            >
                <span
                    class=" relative flex items-center gap-2 {settings.styles.framework.radioClasses} {vh:Validation.ErrorClass(field:field, class:'powermail_field_error')}"
                >
                    <f:form.radio
                        property="{field.marker}"
                        value="{setting.value}"
                        checked="{vh:Misc.PrefillMultiField(field:field, mail:mail, cycle:index.cycle)}"
                        id="powermail_field_{ttContentData.uid}_{field.marker}_{index.cycle}"
                        additionalAttributes="{vh:Validation.ValidationDataAttribute(field:field, iteration:index)}"
                        class="powermail_radio custom-control-input w-5 h-5 mt-0.5 border-2 rounded-full appearance-none border-(--styling-form-field-enabled-border) cursor-pointer transition-all duration-200 bg-clip-content p-1 checked:border-(--styling-form-field-active-border) checked:bg-(--styling-form-field-active-border) hover:border-(--styling-form-field-active-border) disabled:cursor-not-allowed"
                    />
                    <label
                        for="powermail_field_{ttContentData.uid}_{field.marker}_{index.cycle}"
                        class="{settings.styles.framework.labelClasses} cursor-pointer text-s "
                    >
                        <vh:string.escapeLabels
                            >{setting.label}</vh:string.escapeLabels
                        >
                    </label>
                </span>
                <f:if condition="!{validation}">
                    <f:variable
                        name="validation"
                        value="{vh:Validation.ValidationDataAttribute(field:field, iteration:index)}"
                    />
                </f:if>
            </f:for>
        </div>
        <f:if condition="{settings.validation.client}">
            <span
                class="radio-group__error powermail_field_error_container_{field.marker}"
            ></span>
        </f:if>
        <f:if condition="{validation.data-powermail-required-message}">
            <span class="powermail_error_message"
                >{validation.data-powermail-required-message}</span
            >
        </f:if>
    </div>
</html>
