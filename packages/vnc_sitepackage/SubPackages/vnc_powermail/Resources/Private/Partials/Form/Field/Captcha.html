{namespace vh=In2code\Powermail\ViewHelpers}

<div class="powermail_fieldwrap powermail_fieldwrap_type_captcha powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
	<f:render partial="Form/FieldLabel" arguments="{_all}" />

	<div class="flex gap-4 items-center {settings.styles.framework.fieldWrappingClasses} {vh:validation.errorClass(field:field, class: 'powermail_field_error')}">
		<f:form.textfield
				id="powermail_field_{field.marker}"
				property="{field.marker}"
				value=""
				class="powermail_captcha powermail_input_field w-32 {settings.styles.framework.fieldClasses} {vh:validation.errorClass(field:field, class:'powermail_field_error')}"
				additionalAttributes="{vh:validation.captchaDataAttribute(field:field)}" />

		<vh:validation.captcha field="{field}" alt="captcha" class="powermail_captchaimage" id="powermail_captchaimage" />

        <span class="powermail_error_message">Bitte lösen Sie die Rechenaufgabe</span>

        <f:comment>
		<f:if condition="{settings.validation.client}">
			<div class="powermail_field_error_container powermail_field_error_container_{field.marker}"></div>
		</f:if>
        </f:comment>
	</div>
</div>
