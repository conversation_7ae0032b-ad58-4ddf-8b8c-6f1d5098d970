{namespace vh=In2code\Powermail\ViewHelpers}

<div
    class="input--check-group powermail_fieldwrap powermail_fieldwrap_type_check powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}"
>
    <f:alias map="{noForAttribute: '1'}">
        <f:render partial="Form/FieldLabel" arguments="{_all}" />
    </f:alias>
    <div class="{settings.styles.framework.fieldWrappingClasses} flex flex-wrap items-center gap-4">
        <f:for each="{field.modifiedSettings}" as="setting" iteration="index">
            <div
                class="input input--checkbox relative flex items-center gap-2 {vh:validation.errorClass(field:field, class:'powermail_field_error')}"
            >
                <f:if condition="{field.mandatory}">
                    <f:variable name="required">true</f:variable>
                </f:if>
                <f:form.checkbox
                    property="{field.marker}"
                    multiple="true"
                    required="{required}"
                    value="{setting.value}"
                    checked="{vh:misc.prefillMultiField(field:field, mail:mail, cycle:index.cycle)}"
                    id="powermail_field_{ttContentData.uid}_{field.marker}_{index.cycle}"
                    additionalAttributes="{vh:validation.validationDataAttribute(field:field, iteration:index)}"
                    class="powermail_checkbox powermail_checkbox_{field.uid} custom-control-input  w-5 h-5 mt-0.5 border-2 rounded-sm appearance-none border-(--styling-form-field-enabled-border) cursor-pointer transition-all duration-200 bg-clip-content p-1 hover:border-(--styling-form-field-active-border) disabled:cursor-not-allowed"
                />
                <label
                    class="cursor-pointer text-s"
                    for="powermail_field_{ttContentData.uid}_{field.marker}_{index.cycle} "
                >
                    <vh:string.escapeLabels
                        >{setting.label}</vh:string.escapeLabels
                    >
                </label>
            </div>
            <f:if condition="!{validation}">
                <f:variable
                    name="validation"
                    value="{vh:validation.validationDataAttribute(field:field, iteration:index)}"
                />
            </f:if>
        </f:for>
        <f:if condition="{validation.data-powermail-required-message}">
            <span class="powermail_error_message"
                >{validation.data-powermail-required-message}</span
            >
        </f:if>

        <f:if condition="{settings.validation.client}">
            <div
                class="powermail_field_error_container powermail_field_error_container_{field.marker} checkbox__error"
            ></div>
        </f:if>
    </div>
</div>
