<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
    data-namespace-typo3-fluid="true"
>

<fieldset class="powermail_fieldset powermail_fieldset_{page.uid}">
	<div class="grid grid-cols-12 gap-4">
		<f:if condition="{page.css} != 'nolabel'">
			<div class="col-60">
				<legend class="powermail_legend">{page.title}</legend>
			</div>
		</f:if>

		<f:for each="{page.fields}" as="field" iteration="iteration">
				<vh:misc.createRowTags columns="{settings.styles.numberOfColumns}" class="{settings.styles.framework.rowClasses}" iteration="{iteration}">
					<f:render partial="Form/Field/{vh:String.Upper(string:field.type)}" arguments="{_all}" />
				</vh:misc.createRowTags>
		</f:for>
	</div>
    <f:comment><!--
    <f:if condition="{settings.main.moresteps}">
        <div class="powermail_fieldwrap powermail_tab_navigation">
            <f:if condition="!{iterationPages.isLast}">
                <button type="button" class="button button--primary float-end" data-powermail-morestep-show="{iterationPages.cycle}">
                    <span class="caption">
                     &gt;
                    </span>
                </button>
            </f:if>
            <f:if condition="!{iterationPages.isFirst}">
                <button type="button" class="button button--primary" data-powermail-morestep-show="{iterationPages.index - 1}">
                    <span class="caption">
                     &lt;
                    </span>
                </button>
            </f:if>
        </div>
    </f:if>
    --></f:comment>
</fieldset>
