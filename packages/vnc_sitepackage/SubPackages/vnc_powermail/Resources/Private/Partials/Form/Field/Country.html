<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:comment><!--
    {vh:Form.Countries()} will try to get the country list from the extension static_info_tables (and _de, _fr etc...)
    If static_info_tables is not installed, a static list of countries and the ISO3 code will be shown in frontent
    If you want to change sorting, Value or Label, please install static_info_tables
--></f:comment>

<div class="powermail_fieldwrap powermail_fieldwrap_type_country powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
	<f:render partial="Form/FieldLabel" arguments="{_all}" />

	<div class="{settings.styles.framework.fieldWrappingClasses}">
		<f:form.select
				property="{field.marker}"
				options="{vh:form.countries(key:'isoCodeA3',value:'officialNameLocal',sortbyField:'isoCodeA3',sorting:'asc')}"
				prependOptionLabel="{f:translate(key:'pleaseChoose')}"
				class="powermail_country custom-select {settings.styles.framework.fieldClasses} {vh:validation.errorClass(field:field, class:'powermail_field_error')}"
				value="{vh:misc.prefillField(field:field, mail:mail)}"
				additionalAttributes="{vh:validation.validationDataAttribute(field:field)}"
				id="powermail_field_{field.marker}" />
	</div>
</div>
