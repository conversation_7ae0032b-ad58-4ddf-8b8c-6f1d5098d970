<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:comment><!--
    Partial file for the HTML-structure of nearly all field labels
--></f:comment>
<f:if condition="{field.css} != 'nolabel'">
    <f:if condition="{noForAttribute}">
        <f:then>
            <label class="{settings.styles.framework.labelClasses}" title="{field.description}">
                <vh:string.escapeLabels>{field.title}</vh:string.escapeLabels><f:if condition="{field.mandatory} && {field.type}!='captcha'">*</f:if>
                <f:if condition="{field.description}"><span class="powermail-description"><f:format.html parseFuncTSPath="lib.parseFunc">{field.description}</f:format.html></span></f:if>
            </label>
        </f:then>
        <f:else>
            <label for="powermail_field_{field.marker}" class="{settings.styles.framework.labelClasses}" title="{field.description}">
                <vh:string.escapeLabels>{field.title}</vh:string.escapeLabels><f:if condition="{field.mandatory} && {field.type}!='captcha'">*</f:if>
                <f:if condition="{field.description}"><span class="powermail-description"><f:format.html parseFuncTSPath="lib.parseFunc">{field.description}</f:format.html></span></f:if>
            </label>
        </f:else>
    </f:if>

</f:if>
