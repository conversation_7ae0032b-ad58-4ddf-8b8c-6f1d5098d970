{namespace vh=In2code\Powermail\ViewHelpers}

<div class="powermail_fieldwrap powermail_fieldwrap_type_input powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
	<f:render partial="Form/FieldLabel" arguments="{_all}" />

	<div class="{settings.styles.framework.fieldWrappingClasses} flex flex-col gap-1">
		<f:form.textfield
				type="{vh:validation.fieldTypeFromValidation(field:field)}"
				property="{field.marker}"
				placeholder="{field.placeholder}"
				value="{vh:misc.prefillField(field:field, mail:mail)}"
				class="powermail_input_field {settings.styles.framework.fieldClasses} {vh:validation.errorClass(field:field, class:'powermail_error_message')} "
				additionalAttributes="{vh:validation.validationDataAttribute(field:field, additionalAttributes:'{autocomplete: field.autocomplete}')}"
				id="powermail_field_{field.marker}" />
        <f:variable name="validation" value="{vh:validation.validationDataAttribute(field:field)}" />
        <f:if condition="{validation.data-powermail-required-message}">
         <span class="powermail_error_message">{validation.data-powermail-required-message}</span>
        </f:if>
	</div>
</div>
