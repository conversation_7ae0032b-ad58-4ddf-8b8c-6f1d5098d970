<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:form.validationResults>
	<f:comment><!--
	<f:if condition="{validationResults.flattenedErrors}">
		<div class="alert alert-danger">
		<ul class="powermail_message powermail_message_error">
			<f:for each="{validationResults.flattenedErrors}" as="errors">
				<f:for each="{errors}" as="singleError">
					<li>
						<f:if condition="{singleError.code}">
							<f:if condition="{vh:Getter.GetFieldPropertyFromMarkerAndForm(marker:singleError.code, form:form, property:'title')}">
								<f:then>
									<vh:Getter.GetFieldPropertyFromMarkerAndForm marker="{singleError.code}" form="{form}" property="title" />:
								</f:then>
								<f:else>
									<f:translate key="validationerror_spam" /> {singleError.code}
								</f:else>
							</f:if>
						</f:if>

						<f:translate key="validationerror_{singleError.message}">{singleError.message}</f:translate>
					</li>
				</f:for>
			</f:for>
		</ul>
		</div>
	</f:if>
	--></f:comment>
</f:form.validationResults>
