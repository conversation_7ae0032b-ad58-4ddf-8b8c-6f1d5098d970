<tr><td align="center" valign="top" id="templateHeader" style="background-color:{settings.vnc_powermail.headerBackgroundColor -> f:format.raw()};">

    <!--[if (gte mso 9)|(IE)]><table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:820px;"><tr><td align="center" valign="top" width="600" style="width:820px;"><![endif]-->
    <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer"><tr><td valign="top" class="headerContainer">
        <table border="0" cellpadding="0" cellspacing="0" width="100%" class="textBlock" style="min-width:100%;"><tbody class="textBlockOuter"><tr><td valign="top" class="textBlockInner" style="padding-top:9px;">

            <!--[if mso]><table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;"><tr><td valign="top" width="600" style="width:820px;"><![endif]-->
            <table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width:100%; min-width:100%;" width="100%" class="textContentContainer">
                <tbody>
                <tr>
                    <td valign="top" class="image" align="center" style="padding: 0px 18px; padding-bottom: 0px;color: #004173; text-align: left;">
                        <f:image src="{settings.vnc_powermail.logoPath}" height="60" absolute="1" alt="Logo" />
                    </td>
                </tr>
                <tr>
                    <td valign="top" class="textContent" style="padding: 0px 0px;color: {settings.vnc_powermail.headerColor -> f:format.raw()};font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, Verdana, sans-serif;font-size: 24px;text-align: left;">
                    </td>
                </tr>
                </tbody>
            </table>
            <!--[if mso]></td></tr></table><![endif]-->

        </td></tr></tbody></table>
    </td></tr></table>
    <!--[if (gte mso 9)|(IE)]></td></tr></table><![endif]-->

</td></tr>
