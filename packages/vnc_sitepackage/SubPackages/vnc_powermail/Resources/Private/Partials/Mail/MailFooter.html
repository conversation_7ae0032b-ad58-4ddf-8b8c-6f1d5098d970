<tr><td align="center" valign="top" id="templateFooter">

    <!--[if (gte mso 9)|(IE)]><table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;"><tr><td align="center" valign="top" width="600" style="width:600px;"><![endif]-->
    <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer"><tr><td valign="top" class="footerContainer">
        <table border="0" cellpadding="0" cellspacing="0" width="100%" class="textBlock" style="min-width:100%;"><tbody class="textBlockOuter"><tr><td valign="top" class="textBlockInner">

            <!--[if mso]><table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;"><tr><td valign="top" width="600" style="width:600px;"><![endif]-->
            <table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width:100%; min-width:100%;" width="100%" class="textContentContainer"><tbody><tr><td valign="top" class="textContent" style="padding:0 36px;">

                <p style="margin-top: 0;">
                    &copy; {settings.vnc_powermail.copyrightNotice} {f:format.date(date: 'now', format: 'Y')}
                </p>
                <p style="margin-top: 0;">
                    <f:link.typolink
                        parameter="{settings.vnc_powermail.disclaimerID}"
                    ></f:link.typolink>
                    |
                    <f:link.typolink
                        parameter="{settings.vnc_powermail.privacyID}"
                    ></f:link.typolink>
                </p>

            </td></tr></tbody></table>
            <!--[if mso]></td></tr></table><![endif]-->

        </td></tr></tbody></table>
    </td></tr></table>
    <!--[if (gte mso 9)|(IE)]></td></tr></table><![endif]-->


</td></tr>
