<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
      data-namespace-typo3-fluid="true"
>
<f:layout name="Default" />

Show Confirmation Page
{powermail_rte}					Variable is filled with values from RTE in backend (from thx page)
{powermail_all}					Outputs all fields
{marker1}, {firstname}, etc..	Outputs a field
{mail}							Complete Mail Object
{ttContentData}					All values from content element with plugin
{uploadService}					All values from uploaded files


<f:section name="main">
	<div class="powermail_confirmation mt-5 mt-lg-7" data-powermail-form="{mail.form.uid}">
		<h1 class="headline  h3 mb-3 mb-lg-5 text-center">Bitte überprüfen Sie Ihre Eingaben</h1>

		<f:format.raw>{powermail_all}</f:format.raw>

		<div class="d-flex align-items-center my-5 justify-content-end" role="group">
			<f:comment>
				Link: Back to form
			</f:comment>
			<f:form
					action="form"
					section="c{ttContentData.uid}"
					name="field"
					enctype="multipart/form-data"
					class="visible-xs-inline-block visible-sm-inline-block visible-md-inline-block visible-lg-inline-block"
 					addQueryString="{settings.misc.addQueryString}"
					additionalAttributes="{vh:validation.enableParsleyAndAjax(form: mail.form)}">
				<f:render section="HiddenFields" arguments="{_all}" />
				<f:form.submit
						value="Zurück"
						additionalAttributes="{data-powermail-form-ajax:'confirmation'}"
						class="button button--animated button button--secondary button--animate" />
			</f:form>


			<f:comment>
				Link: Submit form
			</f:comment>
			<f:form
					action="create"
					section="c{ttContentData.uid}"
					name="field"
					enctype="multipart/form-data"
					class="visible-xs-inline-block visible-sm-inline-block visible-md-inline-block visible-lg-inline-block"
					addQueryString="{settings.misc.addQueryString}"
					additionalAttributes="{vh:validation.enableParsleyAndAjax(form: mail.form)}">
				<f:render section="HiddenFields" arguments="{_all}" />
				<f:form.submit
						value="Abschicken"
						additionalAttributes="{data-powermail-form-ajax:'submit'}"
						class="button button--primary button--animated button button--primary button--animate" />
			</f:form>
		</div>
	</div>
</f:section>



<f:section name="HiddenFields">
	<f:for each="{mail.answers}" as="answer">
		<f:if condition="{vh:condition.isArray(val:answer.value)}">
			<f:then>
				<f:for each="{answer.value}" as="subvalue" iteration="i">
					<f:form.hidden property="{answer.field.marker}.{i.index}" value="{subvalue}" />
				</f:for>
			</f:then>
			<f:else>
				<f:form.hidden property="{answer.field.marker}" value="{answer.value}" />
			</f:else>
		</f:if>
	</f:for>

	<f:form.hidden name="mail[form]" value="{mail.form.uid}" class="powermail_form_uid" />
</f:section>
