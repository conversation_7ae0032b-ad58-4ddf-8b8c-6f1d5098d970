<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:layout name="Default" />

    Render Powermail Form {form} All Forms with their Pages and their Fields
    {ttContentData} All values from content element with plugin

    <f:section name="main">
        <div class="flex flex-col gap-4 lg:gap-2 mb-4">
            <f:if condition="{ttContentData.header}">
                <h2 class="headline-l text-primary">{ttContentData.header}</h2>
            </f:if>

            <f:if condition="{settings.teaser.header}">
                <h3 class="headline-s text-primary">
                    {settings.teaser.header}
                </h3>
            </f:if>
            <f:if condition="{settings.teaser.body}">
                <p class="text-s bodytext">
                    <f:format.raw>{settings.teaser.body}</f:format.raw>
                </p>
            </f:if>
            <p class="text-xs">
                Mit einem * markierte Felder sind Pflichtfelder und müssen
                ausgefüllt werden.
            </p>
        </div>
        <f:render section="Form" arguments="{_all}" />
    </f:section>

    <f:section name="Form">
        <f:if condition="{form}">
            <f:then>
                <div class="form">
                    <div class="tx-powermail">
                        <f:form
                            action="{action}"
                            section="c{ttContentData.uid}"
                            name="field"
                            enctype="multipart/form-data"
                            addQueryString="{settings.misc.addQueryString}"
                            class="powermail_form powermail_form_{form.uid} {form.css} {settings.styles.framework.formClasses} {vh:misc.morestepClass(activate:settings.main.moresteps)}"
                        >
                            <f:render
                                partial="Misc/FormError"
                                arguments="{_all}"
                            />
                            <f:for each="{form.pages}" as="page">
                                <f:render
                                    partial="Form/Page"
                                    arguments="{_all}"
                                />
                            </f:for>

                            <f:form.hidden
                                name="mail[form]"
                                value="{form.uid}"
                                class="powermail_form_uid"
                            />
                            <f:render
                                partial="Misc/HoneyPod"
                                arguments="{form:form}"
                            />
                        </f:form>
                    </div>
                </div>
            </f:then>
            <f:else>
                <f:translate key="error_no_form" />
            </f:else>
        </f:if>
    </f:section>
</html>
