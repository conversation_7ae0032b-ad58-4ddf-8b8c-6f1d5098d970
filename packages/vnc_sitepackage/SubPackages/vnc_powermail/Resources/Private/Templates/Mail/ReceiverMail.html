<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:layout name="Mail" />

Render Powermail Mail to Receiver
{powermail_rte}									Variable is filled with values from RTE in backend
{powermail_all}									Outputs all fields
{marker1}, {firstname}, etc.. 					Outputs a field
{label_marker1}, {label_firstname}, etc... 		Outputs a label to a field
{mail}											Complete Mail Object
{email}											Email Configuration
{settings}										TypoScript Settings
NOTE: See example section after main section

<f:section name="main">
    <f:format.html parseFuncTSPath="lib.parseFunc_powermail">{powermail_rte}</f:format.html>
    <vh:string.escapeLabels>
        {powermail_all}
    </vh:string.escapeLabels>
</f:section>






THIS IS ONLY AN EXAMPLE SECTION

<f:section name="example">

	1. Get values from RTE from Backend:
	<vh:Misc.Variables mail="{mail}" type="mail" function="receiver">
		<f:format.html parseFuncTSPath="lib.parseFunc_powermail">{powermail_rte}</f:format.html>
	</vh:Misc.Variables>


	2. Get all Values by using powermail_all
	<vh:string.escapeLabels>
		{powermail_all}
	</vh:string.escapeLabels>

	3. Get some lonely fields
	Hi {firstname} {lastname}, thank you for your email.<br />
	{label_email}: {email}<br />
	{label_text}: {text}

</f:section>
