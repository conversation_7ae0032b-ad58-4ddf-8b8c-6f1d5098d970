<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:layout name="Mail" />

Render Powermail Mail to Sender
{powermail_rte}									Variable is filled with values from RTE in backend
{powermail_all}									Outputs all fields
{marker1}, {firstname}, etc.. 					Outputs a field
{label_marker1}, {label_firstname}, etc... 		Outputs a label to a field
{mail}											Complete Mail Object
{email}											Email Configuration
{settings}										TypoScript Settings
NOTE: See example section after main section

<f:section name="main">

	<table>
		<tr><td colspan="3">&nbsp;</td></tr>
		<tr>
			<td width="26" class="EQ-11" style="min-width: 26px;">&nbsp;</td>
			<td width="740" class="EQ-13" style="min-width: 740px;">
				<table cellspacing="0" cellpadding="0" align="left" class="layout-block-column" width="740"
					   style="min-width: 740px; ">
					<tbody>
					<tr>
						<td width="720" valign="top" align="left"
							style="padding-left: 10px; padding-right: 10px; min-width: 720px; "
							bgcolor="#ffffff">
							<table cellspacing="0" cellpadding="0" class="layout-block-box-padding"
								   width="720" style="min-width: 720px; ">
								<tbody>
								<tr>
									<td align="left" class="layout-block-column" width="720"
										style="min-width: 720px; ">

										<vh:Misc.Variables mail="{mail}" type="mail" function="sender">
											<f:format.html parseFuncTSPath="lib.parseFunc_powermail">{powermail_rte}</f:format.html>
										</vh:Misc.Variables>


									</td>
								</tr>
								</tbody>
							</table>
						</td>
					</tr>
					</tbody>
				</table>
			</td>
			<td width="26" class="EQ-12" style="min-width: 26px; ">&nbsp;</td>
		</tr>
		<tr><td colspan="3">&nbsp;</td></tr>
		<tr>
			<td width="26" class="EQ-11" style="min-width: 26px;">&nbsp;</td>
			<td width="740" class="EQ-13" style="min-width: 740px;">
				<f:render partial="Mail/DisclaimerLink" arguments="{_all}" />
			</td>
			<td width="26" class="EQ-12" style="min-width: 26px; ">&nbsp;</td>
		</tr>
		<tr><td colspan="3">&nbsp;</td></tr>
	</table>



</f:section>






THIS IS ONLY AN EXAMPLE SECTION

<f:section name="example">

	1. Get values from RTE from Backend:
	<vh:Misc.Variables mail="{mail}" type="mail" function="sender">
		<f:format.html parseFuncTSPath="lib.parseFunc_powermail">{powermail_rte}</f:format.html>
	</vh:Misc.Variables>


	2. Get all Values by using powermail_all
	<vh:string.escapeLabels>
		{powermail_all}
	</vh:string.escapeLabels>

	3. Get some lonely fields
	Hi {firstname} {lastname}, this is a test...

</f:section>
