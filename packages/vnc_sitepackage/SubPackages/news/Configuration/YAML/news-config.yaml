routeEnhancers:
  News:
    type: Extbase
    extension: News
    plugin: Pi1
    routes:
      - routePath: '/news/'
        _controller: 'News::list'
      - routePath: '/news/seite-{page}'
        _controller: 'News::list'
        _arguments:
          page: 'currentPage'
      - routePath: '/news/{news-title}'
        _controller: 'News::detail'
        _arguments:
          news-title: news
      - routePath: '/news/{category-name}'
        _controller: 'News::list'
        _arguments:
          category-name: overwriteDemand/categories
      - routePath: '/news/{tag-name}'
        _controller: 'News::list'
        _arguments:
          tag-name: overwriteDemand/tags
    defaultController: 'News::list'
    defaults:
      page: '0'
    aspects:
      news-title:
        type: NewsTitle
      page:
        type: StaticRangeMapper
        start: '1'
        end: '100'
      category-name:
        type: NewsCategory
      tag-name:
        type: NewsTag
