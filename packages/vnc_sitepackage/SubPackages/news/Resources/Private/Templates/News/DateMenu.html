<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">

<f:layout name="General" />
<!--
	=====================
		Templates/News/DateMenu.html
-->

<f:section name="content">
    <!--TYPO3SEARCH_end-->
    <f:if condition="{news} && {data.single}">
        <div class="news-container py-5">
                <div class="container" data-ajax-container="">
                    <div class="row news-menu-view justify-content-center">
                        <f:if condition="{data.single}">
                        <f:variable name="currentYear">{f:if(condition: '{overwriteDemand.year}', then: '{overwriteDemand.year}')}</f:variable>
                        <f:variable name="labelYear">{f:if(condition: '{currentYear}', then: '{currentYear}', else: 'Alle Beiträge')}</f:variable>

                        <f:variable name="currentMonth">{f:if(condition: '{overwriteDemand.month}', then: '{overwriteDemand.month}')}</f:variable>
                        <f:variable name="labelMonth">Alle Beiträge</f:variable>
                        <f:for each="{data.single}" key="year" as="months">
                          <f:for each="{months}" key="month" as="count">
                            <f:if condition="{0:year, 1:month} == {0:overwriteDemand.year, 1:overwriteDemand.month}">
                              <f:then>
                                <f:variable name="labelMonth">{f:if(condition: '{currentMonth}', then: "{f:translate(key:'month.{currentMonth}')}")}</f:variable>
                                </f:then>
                              </f:if>
                          </f:for>
                        </f:for>

      <div class="col-md-2 form-group">
        <label for="year" class="input__label" title="">
        Jahr
        </label>
        <div class="input--dropdown input--select input--select-url mb-3">

            <select data-input-dropdown="{
    &quot;caption&quot;: &quot;{labelYear}&quot;,
    &quot;toggleIcon&quot;: { &quot;src&quot;: &quot;{f:uri.resource(path: 'EXT:vnc_sitepackage/Resources/Public/Static/symbol-defs.svg')}&quot;, &quot;symbol&quot;: &quot;chevron-down&quot; }
}" class="visually-hidden" id="year" name="year">
            <option value="{f:uri.action(action: 'list', pageUid: '{listPid}')}">Alle Beiträge</option>
          <f:for each="{data.single}" key="year" as="months">
            <f:variable name="yearCount" value="{data.total.{year}}" />
            <option  value="{f:uri.action(action: 'list', pageUid: '{listPid}', arguments: '{overwriteDemand: {year: year}}')}" >{year} ({yearCount} Beiträge)</option>
          </f:for>

        </select>
        <label class="input__label visually-hidden" for="year">
        Bitte auswählen
        </label>
        <span class="powermail_error_message">Dieses Feld muss ausgefüllt werden!</span>
        </div>
      </div>

      <f:if condition="{currentYear}">
        <f:then>

      <div class="col-md-2 form-group">
        <label for="month" class="input__label" title="">
        Monat
        </label>
        <div class="input--dropdown input--select input--select-url mb-3">

          <select data-input-dropdown="{
              &quot;caption&quot;: &quot;{labelMonth}&quot;,
{ &quot;src&quot;: &quot;{f:uri.resource(path: 'EXT:vnc_sitepackage/Resources/Public/Static/symbol-defs.svg')}&quot;, &quot;symbol&quot;: &quot;chevron-down&quot; }
          }" class="visually-hidden" id="year" name="year">

          <option value="{f:uri.action(action: 'list', pageUid: '{listPid}', arguments: '{overwriteDemand: {year: currentYear}}')}">Alle Beiträge</option>

          <f:for each="{data.single}" key="year" as="months">
              <f:if condition="{year} == {currentYear}">
                  <f:for each="{months}" key="month" as="count"  reverse="true">
                      <option value="{f:uri.action(action: 'list', pageUid: '{listPid}', arguments: '{overwriteDemand: {year: year,month: month}}')}" ><f:translate key="month.{month}" /> ({count} Beiträge)</option>
                      <f:comment>
                          @stefan: Hier einmal die sprechenden Monate falls gewünscht, aber bitte beachten das immer mit Angabe von führenden Nullen.
                          <f:translate key="month.{month}" />
                      </f:comment>
                  </f:for>
              </f:if>
          </f:for>

        </select>
        <label class="input__label visually-hidden" for="year">
        Bitte auswählen
        </label>
        <span class="powermail_error_message">Dieses Feld muss ausgefüllt werden!</span>
        </div>
      </div>
      </f:then>
      </f:if>


                        </f:if>
                    </div>
                </div>
            </div>
    </f:if>
    <!--TYPO3SEARCH_begin-->

</f:section>
</html>
