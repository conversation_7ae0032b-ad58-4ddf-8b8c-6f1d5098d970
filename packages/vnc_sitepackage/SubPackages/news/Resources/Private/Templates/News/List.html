<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:n="http://typo3.org/ns/GeorgRinger/News/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:layout name="General" />
    <f:section name="content">
        <!--TYPO3SEARCH_end-->
        <f:if condition="{news}">
            <f:then>
                <div class="news-container">
                    <f:if condition="{contentObjectData.header}">
                        <f:render
                            partial="Header"
                            arguments="{data:contentObjectData,className: ''}"
                        />
                    </f:if>
                    <div class="container" data-ajax-container="">
                        <div class="flex flex-col">
                            <div
                                class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6"
                            >
                                <f:for
                                    each="{pagination.paginator.paginatedItems}"
                                    as="newsItem"
                                    iteration="iterator"
                                >
                                    <f:render
                                        partial="List/Item"
                                        arguments="{newsItem: newsItem,settings:settings,iterator:iterator}"
                                    />
                                </f:for>
                            </div>
                            <f:if
                                condition="{pageData.uid} != {settings.newsArchivPid}"
                            >
                                <f:render
                                    partial="List/NewsArchivTeaser"
                                    arguments="{settings:settings}"
                                />
                            </f:if>
                            <f:if condition="{settings.hidePagination}==0">
                                <f:render
                                    partial="Pagination"
                                    arguments="{pagination: pagination.pagination, paginator: pagination.paginator}"
                                />
                            </f:if>
                        </div>
                    </div>
                </div>
            </f:then>
            <f:else>
                <div class="container" data-ajax-container="">
                    <f:translate key="list_nonewsfound" />
                </div>
            </f:else>
        </f:if>
        <!--TYPO3SEARCH_begin-->
    </f:section>
</html>
