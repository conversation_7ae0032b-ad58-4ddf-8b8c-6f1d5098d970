<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
	  xmlns:n="http://typo3.org/ns/GeorgRinger/News/ViewHelpers"
	  data-namespace-typo3-fluid="true">

<div class="mediaelement mediaelement-image">
	<f:if condition="{mediaElement.link}">
		<f:then>
			<f:link.typolink parameter="{mediaElement.link}" target="{n:targetLink(link:mediaElement.link)}">
				<f:image class="{class}" image="{mediaElement}" title="{mediaElement.title}" alt="{mediaElement.alternative}" loading="{settings.detail.media.image.lazyLoading}" maxWidth="{f:if(condition: settings.media.maxWidth, then: settings.media.maxWidth, else: settings.detail.media.image.maxWidth)}" maxHeight="{f:if(condition: settings.media.maxHeight, then: settings.media.maxHeight, else: settings.detail.media.image.maxHeight)}" additionalAttributes="{itemprop:'image'}" />
			</f:link.typolink>
		</f:then>
		<f:else>
			<f:if condition="{settings.detail.media.image.lightbox.enabled}">
				<f:then>
					<a href="{f:uri.image(image:mediaElement, width:'{settings.detail.media.image.lightbox.width}', height:'{settings.detail.media.image.lightbox.height}')}" title="{mediaElement.title}" class="{settings.detail.media.image.lightbox.class}" rel="{settings.detail.media.image.lightbox.rel}">
						<f:image image="{mediaElement}" title="{mediaElement.title}" alt="{mediaElement.alternative}" loading="{settings.detail.media.image.lazyLoading}" maxWidth="{f:if(condition: settings.media.maxWidth, then: settings.media.maxWidth, else: settings.detail.media.image.maxWidth)}" maxHeight="{f:if(condition: settings.media.maxHeight, then: settings.media.maxHeight, else: settings.detail.media.image.maxHeight)}" additionalAttributes="{itemprop:'image'}" />
					</a>
				</f:then>
				<f:else>
					<f:image class="{class}" image="{mediaElement}" title="{mediaElement.title}" alt="{mediaElement.alternative}" loading="{settings.detail.media.image.lazyLoading}" maxWidth="{f:if(condition: settings.media.maxWidth, then: settings.media.maxWidth, else: settings.detail.media.image.maxWidth)}" maxHeight="{f:if(condition: settings.media.maxHeight, then: settings.media.maxHeight, else: settings.detail.media.image.maxHeight)}" additionalAttributes="{itemprop:'image'}" />
				</f:else>
			</f:if>
		</f:else>
	</f:if>
</div>
<f:if condition="{mediaElement.description}">
	<p class="news-img-caption">
		{mediaElement.description}
	</p>
</f:if>
</html>
