<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <div class="{class}" data-teaser>
        <div class="flex flex-col h-full">
            <div class="h-full p-6 order-1 lg:px-6 bg-(--styling-background-light)">
                <div class="flex flex-col gap-6 justify-between h-full">
                    <f:variable
                        name="newsItemId"
                        value="newsItem-{newsItem.uid}"
                    ></f:variable>

                    <div class="flex flex-col gap-4 lg:gap-2">
                        <div class="flex items-center gap-2 text-xxs">
                            <time
                                class=""
                                datetime="{f:format.date(date:newsItem.datetime, format:'Y-m-d')}"
                            >
                                <f:format.date format="d.m.Y"
                                    >{newsItem.datetime}</f:format.date
                                ></time
                            >
                        </div>

                        <p id="{newsItemId}-title" class="headline-xs">
                            {newsItem.title}
                        </p>

                        <div class="">
                            <f:if condition="{newsItem.teaser}">
                                <f:then>
                                    {newsItem.teaser ->
                                    f:format.crop(maxCharacters:
                                    '{settings.cropMaxCharacters}',
                                    respectWordBoundaries:'1') ->
                                    f:format.html()}
                                </f:then>
                                <f:else>
                                    {newsItem.bodytext ->
                                    f:format.crop(maxCharacters:
                                    '{settings.cropMaxCharacters}',
                                    respectWordBoundaries:'1') ->
                                    f:format.html()}
                                </f:else>
                            </f:if>
                        </div>
                    </div>
                    <div>
                        <n:link
                            newsItem="{newsItem}"
                            class="button button--primary"
                            additionalAttributes="{'aria-labelledby': '{newsItemId}-title {newsItemId}-readmore', 'data-teaser-cta': 'true'}"
                            settings="{settings}"
                            title="{newsItem.title}"
                            id="{newsItemId}-readmore"
                        >
                            <f:translate key="more-link" />
                        </n:link>
                    </div>
                </div>
            </div>
            <div class="order-0">
                <f:if condition="{newsItem.mediaPreviews}">
                    <f:then>
                        <f:alias map="{mediaElement: newsItem.mediaPreviews.0}">
                            <f:if
                                condition="{mediaElement.originalResource.type} == 2"
                            >
                                <f:image
                                    image="{mediaElement}"
                                    class="img-fluid"
                                    title="{mediaElement.originalResource.title}"
                                    alt="{mediaElement.originalResource.alternative}"
                                    loading="{settings.list.media.image.lazyLoading}"
                                    maxWidth="{f:if(condition: settings.media.maxWidth, then: settings.media.maxWidth, else: settings.list.media.image.maxWidth)}"
                                    maxHeight="{f:if(condition: settings.media.maxHeight, then: settings.media.maxHeight, else: settings.list.media.image.maxHeight)}"
                                />
                            </f:if>
                            <f:if
                                condition="{mediaElement.originalResource.type} == 4"
                            >
                                <f:render
                                    partial="Detail/MediaVideo"
                                    arguments="{mediaElement: mediaElement}"
                                />
                            </f:if>
                            <f:if
                                condition="{mediaElement.originalResource.type} == 5"
                            >
                                <f:image
                                    image="{mediaElement}"
                                    class=""
                                    title="{mediaElement.originalResource.title}"
                                    alt="{mediaElement.originalResource.alternative}"
                                    loading="{settings.list.media.image.lazyLoading}"
                                    maxWidth="{f:if(condition: settings.media.maxWidth, then: settings.media.maxWidth, else: settings.list.media.image.maxWidth)}"
                                    maxHeight="{f:if(condition: settings.media.maxHeight, then: settings.media.maxHeight, else: settings.list.media.image.maxHeight)}"
                                />
                            </f:if>
                        </f:alias>
                    </f:then>
                    <f:else>
                        <f:if condition="{settings.displayDummyIfNoMedia}">
                            <f:image
                                class=""
                                src="{settings.list.media.dummyImage}"
                                title=""
                                alt=""
                                loading="{settings.list.media.image.lazyLoading}"
                                maxWidth="{f:if(condition: settings.media.maxWidth, then: settings.media.maxWidth, else: settings.list.media.image.maxWidth)}"
                                maxHeight="{f:if(condition: settings.media.maxHeight, then: settings.media.maxHeight, else: settings.list.media.image.maxHeight)}"
                            />
                        </f:if>
                    </f:else>
                </f:if>
            </div>
        </div>
    </div>
</html>
