<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:if condition="{addressIterator.total} > 1">
        <f:then>
            <f:variable name="wrapperClass">col-span-1</f:variable>
            <f:variable name="teaserContentLayout" value="contact"></f:variable>
        </f:then>
        <f:else>
            <f:variable name="wrapperClass">col-span-2</f:variable>
            <f:variable name="teaserContentLayout" value="contact-split"></f:variable>
        </f:else>
    </f:if>

    <div class="{wrapperClass}">
        <f:variable name="record" value="{ files: { 0: address.firstImage.originalResource } }"></f:variable>
        
        <f:variable name="teaserContent">
            <p class="headline headline-xs">{address.fullName}</p>
            <f:if condition="{address.position}">
                <p class="text-s">{address.position -> f:format.raw()}</p>
            </f:if>
            <f:if condition="{address.phone}">
                <p class="">
                    <a class="btn -icon -svg" href="tel:{address.cleanedPhone}">
                        <i class="svg-icon">
                            <svg width="25" height="24" viewBox="0 0 25 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M14.334 15.191 12.19 17.3a17.434 17.434 0 0 1-3.096-2.376 17.61 17.61 0 0 1-2.379-3.09l2.113-2.141a.917.917 0 0 0 .19-1L6.814 3.56a.92.92 0 0 0-1.08-.525L1.696 4.1a.92.92 0 0 0-.689.919 19.386 19.386 0 0 0 5.499 12.492A19.443 19.443 0 0 0 19.019 23a.918.918 0 0 0 .919-.688l1.067-4.036a.917.917 0 0 0-.524-1.078L15.344 15a.92.92 0 0 0-1.01.191z" stroke="currentColor" stroke-width="2" fill="none" fill-rule="evenodd"/>
                            </svg>
                        </i>
                        {address.phone}
                    </a>
                </p>
            </f:if>
            <f:if condition="{address.email}">
                <p>
                    <a class="btn -icon -svg" href="mailto:{address.email}" data-teaser-cta>
                        <i class="svg-icon ">
                            <svg width="25" height="24" viewBox="0 0 25 24" xmlns="http://www.w3.org/2000/svg">
                                <g stroke="currentColor" stroke-width="2" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M1.006 13v6c0 1.1.901 2 2.003 2h18.027c1.101 0 2.003-.9 2.003-2v-6"/>
                                    <path d="M23.039 8V5c0-1.1-.902-2-2.003-2H3.009c-1.102 0-2.003.9-2.003 2v3l11.016 6L23.04 8z"/>
                                </g>
                            </svg>
                        </i>
                        {address.email}
                    </a>
                </p>
            </f:if>
        </f:variable>
        <h1 class="debug-marker">ADDRESS</h1>
        <f:render partial="Teaser" arguments="{_all}"></f:render>
    </div>
</html>
