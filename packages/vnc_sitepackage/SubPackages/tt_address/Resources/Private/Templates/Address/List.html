<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:layout name="Default" />

    <f:section name="main">
        <f:render
            section="displayMode_{settings.displayMode}"
            arguments="{_all}"
        />
    </f:section>

    <f:section name="displayMode_single">
        <section class="page-section">
            <f:variable name="addressCount" value="{addresses -> f:count()}" />
            <div
                class="{f:if(condition: '{addressCount} > 1', then: 'container-small', else: 'container-small')}"
            >
                <div class="flex flex-col gap-content">
                    <f:if condition="{contentObjectData.header}">
                        <f:then>
                            <h3 class="headline headline-m">
                                {contentObjectData.header -> f:format.raw()}
                            </h3>
                        </f:then>
                    </f:if>
                    <f:if condition="{contentObjectData.bodytext}">
                        <div class="text-s">
                            {contentObjectData.bodytext -> f:format.html()}
                        </div>
                    </f:if>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <f:for
                            each="{addresses}"
                            as="address"
                            iteration="addressIterator"
                        >
                            <f:render partial="Full" arguments="{_all}" />
                        </f:for>
                    </div>
                </div>
            </div>
        </section>
    </f:section>

    <f:comment
        ><!--
    Other displayMode options see orig tmpl in ext dir
    displayMode "list" & "map" in FF settings disabled in /tt_content.pi_flexform.tt_address.tsconfig
--></f:comment
    >
</html>
