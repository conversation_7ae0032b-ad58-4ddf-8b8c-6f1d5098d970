<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">
<li class="nav__item nav__item--icon">
        <div class="nav__search input input&#45;&#45;text input&#45;&#45;search container position-relative">
          <button type="button" class="input__button">
            <span class="svg-icon">
              <svg class="icon" preserveAspectRatio="xMaxYMin">
                <use
                    xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-magnifier"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    x="0"
                    y="0"
                ></use>
              </svg>
            </span>
          </button>
             <f:form action="search" method="post" id="tx_indexedsearch" pageUid="{settings.targetPid}">
        <div class="tx-indexedsearch-hidden-fields">
            <f:form.hidden name="search[_sections]" value="0" />
            <f:form.hidden name="search[_freeIndexUid]" id="tx_indexedsearch_freeIndexUid" value="_" />
            <f:form.hidden name="search[pointer]" id="tx_indexedsearch_pointer" value="0" />
            <f:form.hidden name="search[ext]" value="{searchParams.ext}" />
            <f:form.hidden name="search[searchType]" value="{searchParams.searchType}" />
            <f:form.hidden name="search[defaultOperand]" value="{searchParams.defaultOperand}" />
            <f:form.hidden name="search[mediaType]" value="{searchParams.mediaType}" />
            <f:form.hidden name="search[sortOrder]" value="{searchParams.sortOrder}" />
            <f:form.hidden name="search[group]" value="{searchParams.group}" />
            <f:form.hidden name="search[languageUid]" value="{searchParams.languageUid}" />
            <f:form.hidden name="search[desc]" value="{searchParams.desc}" />
            <f:form.hidden name="search[numberOfResults]" value="{searchParams.numberOfResults}" />
            <f:form.hidden name="search[extendedSearch]" value="{searchParams.extendedSearch}" />
        </div>
        <fieldset>
            <div class="tx-indexedsearch-form">
                <label for="tx-indexedsearch-searchbox-sword">Suche</label>
                <f:form.textfield name="search[sword]" value="{sword}" id="tx-indexedsearch-searchbox-sword" class="tx-indexedsearch-searchbox-sword" />
            </div>
        </fieldset>
    </f:form>
        </div>
</li>

</html>
