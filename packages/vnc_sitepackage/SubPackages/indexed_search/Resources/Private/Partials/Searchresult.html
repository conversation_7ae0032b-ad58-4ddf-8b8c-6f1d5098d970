<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">
<div class="search-result__item">
    <h3 class="headline h4 mb-3">
        <a href="{row.pathUri}" class="link link--primary">{row.title -> f:format.stripTags()}</a>
    </h3>
    <f:if condition="{row.headerOnly} == 0 || {row.headerOnly} == 1">
        <a href="{row.pathUri}" class="link">
            <p>{row.description -> f:format.raw()}</p>
        </a>
    </f:if>
    <f:if condition="{row.subresults}">
        <p class="search-result__sub">
            <f:for each="{row.subresults.items}" as="subrow">
                <f:render partial="Searchresult" arguments="{row: subrow}" />
            </f:for>
        </p>
    </f:if>
    <small class="d-block mt-3"><a class="link" href="{row.pathUri}">{row.pathTitle}</a></small>
</div>
</html>
