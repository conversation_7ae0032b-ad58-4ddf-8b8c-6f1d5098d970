@import 'EXT:vnc_events/Configuration/TypoScript/setup.typoscript'

page {
  includeCSS {
    vnc-events >
  }
  includeJSFooter {
    vnc-events >
  }
}

tt_content.vnc_events {
  templateName = Events
  templateRootPaths {
    100 = EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Templates/Events/
  }
}

tt_content.vnc_eventdetail {
  templateName = EventDetail
  templateRootPaths {
    100 = EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Templates/Events/
  }
}

tt_content.vnc_eventlist {
  templateName = EventList
  templateRootPaths {
    100 = EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Templates/Events/
  }
}
