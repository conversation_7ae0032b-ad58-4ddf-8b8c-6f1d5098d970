###########################################################
#### CTYPE: VNC CONTENT ELEMENT vnccrosssellingteaser ####
###########################################################


tt_content.vncstageslider =< lib.contentElement
tt_content.vncstageslider {

	################
	### TEMPLATE ###
	################
	templateName = VncStageslider.html
  dataProcessing {

    10 = TYPO3\CMS\Frontend\DataProcessing\DatabaseQueryProcessor
    10 {
      table = vnc_content_stageslider_item
      pidInList.field = pid
      where {
        data = field:uid
        intval = 1
        wrap = tt_content=|
      }
      orderBy = sorting
      dataProcessing {
        10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
        10 {
          table = tt_content
          references.fieldName = image
          as = images
        }
      }
    }
  }
}
