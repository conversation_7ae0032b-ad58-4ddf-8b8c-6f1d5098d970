##################################################
#### CTYPE: VNC CONTENT ELEMENT Vnc Text Video ####
##################################################


tt_content.vnctextimage =< lib.contentElement
tt_content.vnctextimage {
  templateName = VncTextImage.html
  dataProcessing {
    10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
    10 {
      references.fieldName = assets
    }
    #20 = TYPO3\CMS\Frontend\DataProcessing\GalleryProcessor
    #20 {
    #  maxGalleryWidth = {$styles.content.textmedia.maxW}
    #  maxGalleryWidthInText = {$styles.content.textmedia.maxWInText}
    #  columnSpacing = {$styles.content.textmedia.columnSpacing}
    #  borderWidth = {$styles.content.textmedia.borderWidth}
    #  borderPadding = {$styles.content.textmedia.borderPadding}
    #}
  }
  stdWrap {
    editIcons = tt_content: header [header_layout], bodytext, assets [imageorient|imagewidth|imageheight], [imagecols|imageborder], image_zoom
    editIcons {
      iconTitle.data = LLL:EXT:fluid_styled_content/Resources/Private/Language/FrontendEditing.xlf:editIcon.textmedia
    }
  }

}
