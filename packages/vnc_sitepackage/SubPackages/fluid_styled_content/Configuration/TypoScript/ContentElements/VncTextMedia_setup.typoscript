##################################################
#### CTYPE: VNC CONTENT ELEMENT Vnc Text Media ####
##################################################


tt_content.vnctextmedia =< lib.contentElement
tt_content.vnctextmedia {
  templateName = VncTextMedia.html
  dataProcessing {
    10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
    10 {
      references.fieldName = assets
    }
  }
  stdWrap {
    editIcons = tt_content: header [header_layout], bodytext, assets [imageorient|imagewidth|imageheight], [imagecols|imageborder], image_zoom
    editIcons {
      iconTitle.data = LLL:EXT:fluid_styled_content/Resources/Private/Language/FrontendEditing.xlf:editIcon.textmedia
    }
  }

}
