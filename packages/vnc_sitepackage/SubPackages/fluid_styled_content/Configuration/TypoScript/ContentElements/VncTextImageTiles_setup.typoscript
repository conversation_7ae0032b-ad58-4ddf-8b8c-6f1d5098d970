###########################################################
#### CTYPE: VNC CONTENT ELEMENT vncminimalisticteaser ####
###########################################################


tt_content.vnctextimagetiles =< lib.contentElement
tt_content.vnctextimagetiles {

	################
	### TEMPLATE ###
	################
	templateName = VncTextImageTiles.html
  dataProcessing {

    10 = TYPO3\CMS\Frontend\DataProcessing\DatabaseQueryProcessor
    10 {
      table = vnc_content_textimagetiles_item
      pidInList.field = pid
      where {
        data = field:uid
        intval = 1
        wrap = tt_content=|
      }
      orderBy = sorting
      dataProcessing {
        10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
        10 {
          table = vnc_content_textimagetiles_item
          references.fieldName = image
          #as = images
        }
      }
    }
  }
}
