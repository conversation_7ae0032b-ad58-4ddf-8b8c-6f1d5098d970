##################################################
#### CTYPE: VNC CONTENT ELEMENT Vnc Stage Split ####
##################################################


tt_content.vncstagesplit =< lib.contentElement
tt_content.vncstagesplit {
  templateName = VncStageSplit.html
  dataProcessing {
    10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
    10 {
      references.fieldName = assets
    }
  }
  stdWrap {
    editIcons = tt_content: header [header_layout], bodytext, assets [imageorient|imagewidth|imageheight], [imagecols|imageborder], image_zoom
    editIcons {
      iconTitle.data = LLL:EXT:fluid_styled_content/Resources/Private/Language/FrontendEditing.xlf:editIcon.textmedia
    }
  }

}
