###########################################################
#### CTYPE: VNC CONTENT ELEMENT vnctexttiles ####
###########################################################


tt_content.vnctexttiles =< lib.contentElement
tt_content.vnctexttiles {

	################
	### TEMPLATE ###
	################
	templateName = VncTextTiles.html
  dataProcessing {

    10 = TYPO3\CMS\Frontend\DataProcessing\DatabaseQueryProcessor
    10 {
      table = vnc_content_texttiles_item
      pidInList.field = pid
      where {
        data = field:uid
        intval = 1
        wrap = tt_content=|
      }
      orderBy = sorting
      dataProcessing {
        10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
        10 {
          table = vnc_content_texttiles_item
          references.fieldName = image
          as = images
        }
      }
    }
  }
}
