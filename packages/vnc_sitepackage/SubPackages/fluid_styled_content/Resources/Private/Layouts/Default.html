<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true"
>
<f:if condition="{data.frame_class} != none">
    <f:then>
        <f:switch expression="{data.space_after_class}">
            <f:case value="gap-section-none"><f:variable name="space_after_class" value="mb-(--global-spacer-page-small-none) lg:mb-(--global-spacer-page-large-none)" /></f:case>
            <f:case value="gap-section-s"><f:variable name="space_after_class" value="mb-(--global-spacer-page-small-s) lg:mb-(--global-spacer-page-large-s)" /></f:case>
            <f:defaultCase><f:variable name="space_after_class" value="mb-(--global-spacer-page-small-m) lg:mb-(--global-spacer-page-large-m)" /></f:defaultCase>
        </f:switch>
        <section class="{data.CType} page-section {space_after_class}" id="c{data.uid}">
            <f:render section="Main" />
        </section>
    </f:then>
    <f:else>
        <f:render section="Main" />
    </f:else>
</f:if>
