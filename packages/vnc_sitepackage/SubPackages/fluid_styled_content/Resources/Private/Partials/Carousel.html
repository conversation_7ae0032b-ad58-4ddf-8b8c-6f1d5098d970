<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:variable
        name="carouselType"
        value="{f:if(condition:'{carouselType}', then: carouselType)}"
    ></f:variable>
    <f:variable name="carouselOptions" value="{carouselOptions}"></f:variable>
    <f:variable name="className" value="{className}"></f:variable>
    <f:variable name="sliderContent" value="{sliderContent}"></f:variable>
    <f:if condition="{data.vnc_content_columns} && {carouselType} == 'grid'">
        <f:variable
            name="columnsClass"
            value="carousel--grid--cols-{data.vnc_content_columns}"
        ></f:variable>
    </f:if>

    <div
        class="{f:if(condition: '{carouselType}', then: 'carousel--{carouselType}')} {columnsClass}"
        aria-roledescription="carousel"
    >
        <div
            class="splide"
            aria-label="{data.header}"
            data-vnc-carousel="{carouselOptions -> f:format.raw()}"
            tabindex="0"
        >
            <div class="splide__track">
                <ul class="splide__list">
                    {sliderContent -> f:format.raw()}
                </ul>
            </div>

            <f:render section="carouselControls" />
            <f:if
                condition="{data.vnc_content_columns} < {records -> f:count()}"
            >
            </f:if>
        </div>
    </div>

    <f:section name="carouselControls">
        <div class="flex items-center justify-center gap-4 splide__arrows">
            <button
                type="button"
                role="button"
                class="splide__arrow--prev "
                data-table-scroll="Zurück"
                aria-label="Zurück"
            >
                <svg class="w-full h-full fill-current" preserveAspectRatio="xMaxYMin"  viewBox="0 0 33 32">
                  <path d="M23.3 15.399c-0.060-0.080-0.12-0.14-0.2-0.2l-12-9c-0.44-0.33-1.070-0.24-1.4 0.2-0.13 0.17-0.2 0.38-0.2 0.6v18c0 0.55 0.45 1 1 1 0.22 0 0.43-0.070 0.6-0.2l12-9c0.44-0.33 0.53-0.96 0.2-1.4z" ></path>
                </svg>
            </button>
            <ul class="splide__pagination"></ul>
            
            <button
                type="button"
                role="button"
                class="splide__arrow--next"
                data-table-scroll="end"
            >
                <svg class="w-full h-full fill-current"  preserveAspectRatio="xMaxYMin"  viewBox="0 0 33 32">
                  <path class="w-full h-full" d="M23.3 15.399c-0.060-0.080-0.12-0.14-0.2-0.2l-12-9c-0.44-0.33-1.070-0.24-1.4 0.2-0.13 0.17-0.2 0.38-0.2 0.6v18c0 0.55 0.45 1 1 1 0.22 0 0.43-0.070 0.6-0.2l12-9c0.44-0.33 0.53-0.96 0.2-1.4z" ></path>
                </svg>
            </button>
        </div>
    </f:section>
</html>
