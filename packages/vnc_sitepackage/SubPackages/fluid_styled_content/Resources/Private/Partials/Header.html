<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:variable name="header">{data.header}</f:variable>
<f:variable name="headerLayout">{data.header_layout}</f:variable>
<f:variable name="subHeader">{data.subheader}</f:variable>
<f:variable name="introText">{data.vnc_content_introtext}{data.bodytext}</f:variable>
<f:variable name="icon">{data.icon}</f:variable>

<f:if condition="{data.overline}">
  <div class="flex items-center gap-2 text-xxs">
    <f:if condition="{data.icon}">
      <i class="icon {data.icon}"></i>
    </f:if>
    <p class="">{data.overline -> f:format.raw()}</p>
  </div>
</f:if>

<f:if condition="{header}">
    <f:if condition="{data.header_layout}">
      <f:then>
        <f:variable name="headerTag">h{data.header_layout}</f:variable>
      </f:then>
      <f:else>
        <f:variable name="headerTag">p</f:variable>
      </f:else>
    </f:if>

    <f:if condition="!{headerSize}">
      <f:then>
        <f:if condition="{data.vnc_content_headlinesize}">
          <f:then>
            <f:variable name="headerSize">{data.vnc_content_headlinesize}</f:variable>
          </f:then>
          <f:else>
            <f:variable name="headerSize">l</f:variable>
          </f:else>
        </f:if>
      </f:then>
    </f:if>
    <{headerTag} class="headline-{headerSize} text-primary ">{header -> f:format.raw()}</{headerTag}>

</f:if>

<f:comment><!-- Add a subheader if any --></f:comment>
<f:if condition="{subHeader}">
        <p class="headline-s text-primary">{subHeader -> f:format.raw()}</p>
</f:if>


<f:comment><!-- Add introtext if any -->
<f:if condition="{introText}">
    <div class="text-s">
        <p>{introText -> f:format.raw()}</p>
    </div>
</f:if>

</f:comment>