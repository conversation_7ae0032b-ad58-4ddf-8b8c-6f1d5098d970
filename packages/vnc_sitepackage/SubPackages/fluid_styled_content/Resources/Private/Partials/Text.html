<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <article class="flex flex-col gap-6 justify-between h-full">
        <div class="flex flex-col gap-4 lg:gap-2">
            <f:if condition="{data.header}">
                <div class="{f:if(condition: '{embeddedTextPartial}', then: '', else: 'lg:container-grid - {embeddedTextPartial}')}">
                    <div class="flex flex-col gap-2 {f:if(condition: '{embeddedTextPartial}', then: '', else: 'col-span-12 md:col-span-10 md:col-start-2')}">
                        <f:render partial="Header" arguments="{_all}" />
                    </div>
                </div>
            </f:if>

            <f:if condition="{data.bodytext}">
                <f:if
                    condition="{data.vnc_content_layout}  == 2 && {data.vnc_content_bodytextsecondcol}"
                >
                    <f:then>
                        <div
                            class="grid grid-cols-1 md:grid-cols-2 gap-content"
                        >
                            <div class="">
                                <f:format.html>{data.bodytext}</f:format.html>
                            </div>
                            <div class="">
                                <f:format.html
                                    >{data.vnc_content_bodytextsecondcol}</f:format.html
                                >
                            </div>
                        </div>
                    </f:then>
                    <f:else>
                        <div class="{f:if(condition: '{embeddedTextPartial}', then: '', else: 'lg:container-grid')}">
                            <div
                                class="{f:if(condition: '{embeddedTextPartial}', then: '', else: 'col-span-12 md:col-span-10 md:col-start-2')}"
                            >
                                <div
                                    class="text-s bodytext [&>p:last-child]:mb-0"
                                >
                                    <f:format.html
                                        >{data.bodytext}</f:format.html
                                    >
                                </div>
                            </div>
                        </div>
                    </f:else>
                </f:if>
            </f:if>
        </div>
        <f:if condition="{data.link}">
            <p
                class="{f:if(condition: '{data.vnc_content_layout} == 2 && {data.vnc_content_bodytextsecondcol}', then: 'text-center', else: '')}"
            >
                <f:link.typolink
                    class="button button--primary"
                    parameter="{data.link}"
                    additionalAttributes="{ tabIndex: -1}"
                >
                    <f:if condition="{data.linktext}"
                        >{data.linktext -> f:format.raw()}</f:if
                    >
                </f:link.typolink>
            </p>
        </f:if>
        <f:if condition="{data.vnc_content_link}">
            <p
                class="{f:if(condition: '{data.vnc_content_layout} == 2 && {data.vnc_content_bodytextsecondcol}', then: 'text-center', else: '')}"
            >
                <f:link.typolink
                    class="button button--primary"
                    parameter="{data.vnc_content_link}"
                    additionalAttributes="{ tabIndex: -1}"
                >
                    <f:if condition="{data.vnc_content_linktext}"
                        >{data.vnc_content_linktext -> f:format.raw()}</f:if
                    >
                </f:link.typolink>
            </p>
        </f:if>
    </article>
</html>
