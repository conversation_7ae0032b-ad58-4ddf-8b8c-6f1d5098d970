<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:variable name="uid">{uid}</f:variable>
<f:variable name="size">{size}</f:variable>
<f:variable name="openAttr">
    <f:if condition="{open}">
        <f:then>
            data-modal-open
        </f:then>
    </f:if>
</f:variable>
<f:variable name="dismissAttr">
    <f:if condition="{dismiss}">
        <f:then>
            data-modal-dismiss
        </f:then>
    </f:if>
</f:variable>
<f:variable name="header">{header}</f:variable>
<f:variable name="footer">{footer}</f:variable>

<dialog
    id="modal-dialog-{uid}"
    data-modal-dialog="modal-dialog-{uid}"
    class="top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 transition-opacity duration-300 absolute {f:if(condition: '{! size}', then: '-{size}')} {className} open:opacity-100 open:w-full open:pointer-events-inherit backdrop:backdrop-blur-sm backdrop:bg-black/50"
    {openAttr}
    {dismissAttr}

    {f:if(condition: header, then: ' aria-labelledby=modal-dialog-{uid}__title', else: '')}
    >
    <div class="px-4 md:px-12 py-2 md:py-6">
        <div class="flex items-center gap-3">
            <div class="headline-m" id="modal-dialog-{uid}__title">{header -> f:format.raw()}</div>
            <button
                type="button"
                class="absolute top-6 right-6 cursor-pointer"
                aria-label="Schließen"
                data-modal-close
            ><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M24.8 9.4L23.4 8L16.4 15L9.4 8L8 9.4L15 16.4L8 23.4L9.4 24.8L16.4 17.8L23.4 24.8L24.8 23.4L17.8 16.4L24.8 9.4Z" fill="#101010"/>
              </svg></button>
        </div>
        <div class="">
            {content -> f:format.raw()}
        </div>
    </div>
</dialog>