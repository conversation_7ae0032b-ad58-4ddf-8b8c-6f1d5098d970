<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:variable name="offset">{offset}</f:variable>
<div class="text-media">
    <div class="flex flex-col lg:flex-row {f:if(condition: '{data.vnc_content_layout} == \'right\'', then: 'lg:flex-row-reverse')} gap-8">
        <div class="flex items-center {f:if(condition: '{offset}', then: '', else: '')}">
            <f:render partial="Media/Media" arguments="{file: file, data: data, settings: settings}"/>
        </div>
        <div class="flex items-center {f:if(condition: '{offset}', then: '', else: '')}">
            <h1 class="debug-marker">TEXT MEDIA</h1>
            <f:render partial="Text" arguments="{data: data, embeddedTextPartial: true}"/>
        </div>
    </div>
</div>
