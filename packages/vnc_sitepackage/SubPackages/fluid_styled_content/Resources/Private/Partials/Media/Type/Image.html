<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">

<f:if condition="{height}">
    <f:then><f:variable name="height" value="{height}"></f:variable></f:then>
    <f:else><f:variable name="height" value="auto"></f:variable></f:else>
</f:if>

<f:variable name="decorative">{decorative}</f:variable>
<f:if condition="{decorative}">
    <f:then>
        <f:variable name="roleAttr">
            role='presentation'
        </f:variable>
        <f:variable name="image.alternative" value=""></f:variable>
    </f:then>
</f:if>

<f:comment><!-- this does not make sense, doesn't it?
<f:if condition="!{className}">
    <f:variable name="className">
        <f:if condition="{className}">
            <f:then>media--cover</f:then>
        </f:if>
    </f:variable>
</f:if>-->
</f:comment>


<f:if condition="!{cropVariant}">
    <f:then>
        <f:if condition="{data.vnc_content_img_layout}">
            <f:then>
                <f:variable name="cropVariant">{data.vnc_content_img_layout}</f:variable>
            </f:then>
            <f:else>
                <f:variable name="cropVariant" value="default"></f:variable>
            </f:else>
        </f:if>
    </f:then>
</f:if>


<f:if condition="!{cropVariants}">
    <f:variable name="cropVariants" value="{
        default:cropVariant,
        tablet:cropVariant,
        mobile:cropVariant
        }"/>
</f:if>

<f:if condition="{sizes}">
    <f:then><f:variable name="sizes" value="{sizes}" />
  </f:then>
    <f:else><f:variable name="sizes" value="{0:'lg', 1:'md', 2:'sm'}" /></f:else>
</f:if>

<f:if condition="!{breakpoints}">
    <f:then>
        <f:variable name="breakpoints" value="{
            xxl:{media:'min-width', size:1400, maxWidth:2000, cropVariant: cropVariants.default },
            xl:{media:'min-width', size:1200, maxWidth:1200, cropVariant: cropVariants.default },
            lg:{media:'min-width', size:992, maxWidth:992, cropVariant: cropVariants.tablet },
            md:{media:'min-width', size:768, maxWidth:768, cropVariant: cropVariants.tablet },
            sm:{media:'min-width', size:576, maxWidth:576, cropVariant: cropVariants.mobile },
            xs:{media:'min-width', size:375, maxWidth:375, cropVariant: cropVariants.mobile }
        }"/>
    </f:then>
</f:if>

<f:for each="{breakpoints}" as="breakpoint" key="breakpointKey" iteration="breakpointIteration">
        <f:if condition="{sizes.0} == {breakpointKey}">
            <f:variable name="defaultSize" value="{breakpoint}" />
        </f:if>
</f:for>


<picture>
    <f:for each="{breakpoints}" as="breakpoint" key="breakpointKey" iteration="breakpointIteration">
        <f:for each="{sizes}" as="size" iteration="sizeIteration">
            <f:if condition="{breakpointKey} == {size}">
                <f:then>
                    <source
                        media="({breakpoint.media}: {breakpoint.size}px)"
                        srcset="{f:uri.image(image: image, maxWidth: '{breakpoint.maxWidth}', height: height, cropVariant: '{breakpoint.cropVariant}', treatIdAsReference: '1')}, {f:uri.image(image: image, maxWidth: '{breakpoint.maxWidth * 2}', cropVariant: '{breakpoint.cropVariant}', treatIdAsReference: '1')} 2x"
                    />
                </f:then>
            </f:if>
        </f:for>
    </f:for>
    <img
        src="{f:uri.image(image: image, maxWidth: '{defaultSize.maxWidth}', height: height, cropVariant: '{defaultSize.cropVariant}', treatIdAsReference: '1')}"
        class="{imageClassName}"
        title="{image.title}"
        alt="{f:if(condition: '{decorative}', then: '', else: image.alternative)}"
        {roleAttr}
    />
</picture>

<f:comment>
    <!--
    <f:if condition="{data.vnc_content_download_icon} || {data.vnc_content_enlarge_icon}">
        <div class="teaser__actions">
            <f:if condition="{data.vnc_content_enlarge_icon}">
                <a data-action-zoom href="#">
                    <i class="icon nc-zoom"></i>
                </a>
            </f:if>
            <f:if condition="{data.vnc_content_download_icon}">
                <a href="{f:uri.image(image: image, treatIdAsReference: '1')}" download="{f:if(condition: '{image.title}', then: '{image.title}', else: 'download-{imageCount}')}">
                    <i class="icon nc-download"></i>
                </a>
            </f:if>
        </div>
    </f:if>
    -->
</f:comment>
</html>
