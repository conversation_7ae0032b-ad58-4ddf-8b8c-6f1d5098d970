<div
    class="flex w-[330px] flex-col items-center bg-(--styling-background-light) px-6"
    data-teaser=""
>
    <article>
        <p class="flex flex-col h-[72px] items-center gap-[-24px] flex-[1_0_0]">
            <f:if condition="{event.categoryIcon}">
                <i class="{event.categoryIcon}"></i>
            </f:if>
            {event.category}
        </p>
        <h3 class="text-black font-roboto text-[18px] font-bold pb-4 leading-[24px] not-italic [font-feature-settings:'liga'_off,'clig'_off]">{event.title}</h3>
        <div class="flex flex-col w-[282px] items-start gap-(--global-spacer-components-l,24px)">
            <div>
                <i class="icon nc-calendar-date"></i>
                <f:format.date format="d.m.Y"
                    >{event.eventStartdate}</f:format.date
                >
                <f:if condition="{event.eventEnddate}">
                    -
                    <f:format.date format="d.m.Y"
                        >{event.eventEnddate}</f:format.date
                    >
                </f:if>
            </div>

            <f:if condition="{event.eventStarttime} || {event.eventEndtime}">
                <div>
                    <i class="icon nc-clock"></i>
                    <f:format.date format="H:i"
                        >{event.eventStarttime}</f:format.date
                    >
                    <f:if condition="{event.eventEndtime}">
                        -
                        <f:format.date format="H:i"
                            >{event.eventEndtime}</f:format.date
                        >
                    </f:if>
                </div>
            </f:if>
            <f:if condition="{event.location}">
                <div>
                    <i class="icon nc-pin-3"></i>
                    {event.location}
                </div>
            </f:if>
            <f:if condition="{event.price}">
                <div>
                    <i class="icon nc-round-euro"></i>
                    {event.price}
                </div>
            </f:if>
        </div>
        <br />
        <p>
            <f:if condition="{event.link}">
                <f:then>
                    <f:link.typolink
                        parameter="{event.link}"
                        class="flex items-center justify-center h-[48px] py-[20px] px-[32px] gap-[10px] mb-4 border-(--styling-cta-button-border-radius,0px) bg-(--styling-cta-button-color-primary-default-fill) text-(--styling-cta-button-color-primary-default-text)"
                        additionalAttributes="{
                                        'data-teaser-cta': 'true',
                                    }"
                    >
                        <f:if condition="{event.linkText}">
                            <f:then> {event.linkText} </f:then>
                            <f:else>
                                <f:translate
                                    key="LLL:EXT:vnc_events/ContentBlocks/ContentElements/events/Source/Language/Frontend.xlf:show_details"
                                />
                            </f:else>
                        </f:if>
                    </f:link.typolink>
                </f:then>
                <f:else>
                    <f:if condition="{data.singlePid.0}">
                        <f:link.typolink
                            parameter="{data.singlePid.0.uid}"
                            class="button button--secondary"
                            additionalAttributes="{'data-teaser-cta': 'true'}"
                            additionalParams="event={event.uid}"
                        >
                            <f:translate
                                key="LLL:EXT:vnc_events/ContentBlocks/ContentElements/events/Source/Language/Frontend.xlf:show_details"
                            />
                        </f:link.typolink>
                    </f:if>
                </f:else>
            </f:if>
        </p>
    </article>
</div>
