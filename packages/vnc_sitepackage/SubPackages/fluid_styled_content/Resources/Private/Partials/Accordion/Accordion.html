<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">

<div class="accordion" data-accordion>
    <f:for each="{records}" as="item" iteration="itemIterator">
        <f:if condition="{item.data.activated}">
            <f:then>
                <f:variable name="collapse" value="false"/>
            </f:then>
            <f:else>
                <f:variable name="collapse" value="true"/>
            </f:else>
        </f:if>
        <f:render
            partial="Accordion/AccordionItem"
            arguments="{
                item: item,
                uid: data.uid,
                count: itemIterator.cycle,
                collapse: collapse
            }"
        />
    </f:for>
</div>
