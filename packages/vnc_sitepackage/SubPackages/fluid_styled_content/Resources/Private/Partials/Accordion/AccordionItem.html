<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>

<div
    class="accordion__item"
    data-accordion-item
    >
    <h3 class="accordion_heading h5">
        <button
            type="button"
            class="accordion__toggle d-flex gap-2 w-100 text-start"
            id="accordion-toggle-{uid}-{count}"
            aria-expanded="{f:if(condition: '{collapse}', then: 'false', else: 'true')}"
            aria-controls="accordion-body-{uid}-{count}"
            data-accordion-toggle
            >
            {item.data.header}
            <span class="svg-icon">
                <svg class="icon" preserveAspectRatio="xMaxYMin">
                    <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-chevron-down"></use>
                  </svg>
              </span>
        </button>
    </h3>
    <f:if condition="{collapse}">
        <f:then>
            <f:variable name="hiddenAttr" value="hidden"/>
        </f:then>
        <f:else>
            <f:variable name="hiddenAttr" value="true"/>
        </f:else>
    </f:if>
    <div
        class="accordion__panel{f:if(condition: '{collapse}', then: ' is-collapsed')}"
        role="region"
        data-accordion-panel
        id="accordion-panel-{uid}-{count}"
        aria-labelledby="accordion-toggle-{uid}-{count}"
        {hiddenAttr}
        >
        <f:render partial="Accordion/AccordionItemContent" arguments="{_all}"></f:render>
    </div>
</div>

</html>


