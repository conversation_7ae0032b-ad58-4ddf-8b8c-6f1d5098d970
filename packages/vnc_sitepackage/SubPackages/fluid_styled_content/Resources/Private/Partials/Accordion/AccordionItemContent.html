<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>

<div class="accordion__content">
    <div class="ce_text py-3">
        <article>
            <f:format.raw>{item.data.bodytext}</f:format.raw>
            <f:if condition="{item.files}">
                <f:then>
                    <f:for each="{item.files}" as="file" iteration="fileIteration">
                        <p>
                            <f:render partial="Media/Media" arguments="{file: file, data: record.data, settings: settings}" />
                        </p>
                    </f:for>
                </f:then>
                <f:else>
                    <f:comment><!-- no files in this record --></f:comment>
                </f:else>
            </f:if>
        </article>
    </div>
</div>

</html>
