<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:variable name="wrapperClass"></f:variable>
    <f:variable name="mediaClass"></f:variable>
    <f:variable name="contentClass"></f:variable>
    <f:variable name="contentInnerClass"></f:variable>
    <f:variable name="teaserContentBackgroundClass"></f:variable>
    <f:variable name="imageClassName">w-full</f:variable>

    <f:if condition="{teaserContentBackground}">
        <f:then>
            <f:if condition="{teaserContentBackground} == 'default'">
                <f:then>
                    <f:variable
                        name="teaserContentBackgroundClass"
                        value="bg-(--styling-background-light)"
                    ></f:variable>
                </f:then>
                <f:else>
                    <f:variable
                        name="teaserContentBackgroundClass"
                        value="bg-(--styling-background-{teaserContentBackground})"
                    ></f:variable>
                </f:else>
            </f:if>
        </f:then>
        <f:else></f:else>
    </f:if>

    <f:switch expression="{teaserContentLayout}">
        <f:case value="overlay">
            <f:variable name="wrapperClass"></f:variable>
            <f:variable name="mediaClass">[&>div>picture>img]:w-full</f:variable>
            <f:variable name="contentClass">lg:absolute z-10 w-full lg:h-full flex items-center px-0 lg:px-24 {f:if(condition: '{teaserContentPosition} == \'right\'', then: 'justify-end', else: 'justify-start')}</f:variable>
            <f:variable name="contentInnerClass">w-full lg:w-1/2 {teaserContentBackgroundClass} px-4 py-6 md:py-12 md:px-6</f:variable>
        </f:case>

        <f:case value="half-video">
            <f:variable name="wrapperClass">flex flex-col-reverse md:flex-row {f:if(condition: '{teaserContentPosition} == \'right\'', then: 'md:flex-row-reverse')}</f:variable>
            <f:variable name="mediaClass">md:w-3/5 flex items-center justify-center {f:if(condition: '!{record.data.header} && !{teaserContent}', then: 'md:w-full')}</f:variable>
            <f:variable name="contentClass">flex items-center justify-center md:px-4 lg:px-6 py-6 md:py-12 md:w-2/5 {teaserContentBackgroundClass} {f:if(condition: '!{record.data.header} && !{teaserContent}', then: 'hidden')}</f:variable>
        </f:case>

        <f:case value="half-boxed">
            <f:variable name="wrapperClass">flex flex-col-reverse md:flex-row {f:if(condition: '{teaserContentPosition} == \'right\'', then: 'md:flex-row-reverse')}</f:variable>
            <f:variable name="mediaClass">md:w-1/2 flex items-center justify-center {f:if(condition: '!{record.data.header} && !{teaserContent}', then: 'md:w-full')}</f:variable>
            <f:variable name="contentClass">flex items-center justify-center px-4 md:px-16 lg:px-24 py-6 md:py-12 md:w-1/2 {teaserContentBackgroundClass} {f:if(condition: '!{record.data.header} && !{teaserContent}', then: 'hidden')}</f:variable>
        </f:case>

        <f:case value="half-default">
            <f:variable name="wrapperClass">flex flex-col-reverse md:flex-row gap-4 {f:if(condition: '{teaserContentPosition} == \'right\'', then: 'md:flex-row-reverse')}</f:variable>
            <f:variable name="mediaClass">md:w-1/2 flex items-center justify-center {f:if(condition: '!{record.data.header} && !{teaserContent}', then: 'md:w-full')}</f:variable>
            <f:variable name="contentClass">flex items-center justify-center md:w-1/2 {teaserContentBackgroundClass} {f:if(condition: '!{record.data.header} && !{teaserContent}', then: 'hidden')}</f:variable>
        </f:case>

        <f:case value="tile">
            <f:variable name="wrapperClass">h-full</f:variable>
            <f:variable name="mediaClass"></f:variable>
            <f:variable name="contentClass">h-full</f:variable>
            <f:variable name="contentInnerClass">h-full p-6 lg:px-6 {teaserContentBackgroundClass}</f:variable>
        </f:case>

        <f:case value="contact">
            <f:variable name="wrapperClass">h-full</f:variable>
            <f:variable name="mediaClass"></f:variable>
            <f:variable name="contentClass">h-full</f:variable>
            <f:variable name="contentInnerClass">flex flex-col gap-2 py-4 lg:py-6</f:variable>
        </f:case>

        <f:case value="contact-split">
            <f:variable name="wrapperClass">flex-col sm:flex-row-reverse</f:variable>
            <f:variable name="mediaClass"> w-full sm:w-3/5</f:variable>
            <f:variable name="contentClass">w-full sm:w-2/5 flex items-center </f:variable>
            <f:variable name="contentInnerClass">flex flex-col gap-2 py-4 lg:py-6 sm:px-4 lg:px-6</f:variable>
        </f:case>

        <f:defaultCase>
            <!-- Default case for any other layout -->
            <f:variable name="wrapperClass"></f:variable>
            <f:variable name="mediaClass"></f:variable>
            <f:variable name="contentClass"></f:variable>
        </f:defaultCase>
    </f:switch>

    <div class="flex flex-col-reverse relative {wrapperClass}">
        <div class="{contentClass}">
            <div class="{contentInnerClass}">
                <f:if condition="{teaserContent}">
                    <f:then>
                        <f:format.raw>{teaserContent}</f:format.raw>
                    </f:then>
                    <f:else>
                        
                        <f:render
                            partial="Text"
                            arguments="{data: record.data, headerSize: headerSize, embeddedTextPartial: true }"
                        ></f:render>
                    </f:else>
                </f:if>
            </div>
        </div>
        <div class="{mediaClass}">
            <f:for each="{record.files}" as="file" iteration="imageIteration">
                <f:render partial="Media/Media" arguments="{_all}"></f:render>
            </f:for>
        </div>
    </div>
</html>
