<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:layout name="Default" />

    <f:section name="Main">
        <f:if condition="{data.vnc_content_teaser_type} == 'video'">
            <f:then>
                <f:variable
                    name="record"
                    value="{ data: data, files: { 0: videos.0 } }"
                ></f:variable>
            </f:then>
            <f:else>
                <f:variable
                    name="record"
                    value="{ data: data, files: { 0: images.0 } }"
                ></f:variable>
            </f:else>
        </f:if>

        <div class="container-viewport">
            <h1 class="debug-marker">STAGE</h1>

            <f:variable
                name="cropVariants"
                value="{
          default:'ultrawide',
          tablet:'ultrawide',
          mobile:'widescreen'
      }"
            />

            <f:variable name="teaserContentLayout" value="half"></f:variable>
            <f:variable name="teaserContentPosition" value="right"></f:variable>
            <f:variable name="headerSize" value="xl"></f:variable>
            <f:variable
                name="teaserContentBackground"
                value="default"
            ></f:variable>
            <f:render partial="Teaser" arguments="{_all}"></f:render>
        </div>
    </f:section>
</html>
