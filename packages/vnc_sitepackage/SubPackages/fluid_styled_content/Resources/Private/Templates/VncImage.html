<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:layout name="Default" />

    <f:comment
        ><!--
    Gallery
    Caption tbd
--></f:comment
    >

    <f:section name="Main">
        <div class="container-viewport flex flex-col gap-2">
            <f:variable name="file" value="{images.0}" />
            
            <f:variable name="lightbox" value="true" />
            <f:variable name="download" value="true" />
            <f:variable name="cropVariant" value="ultrawide" />
            <f:variable name="sizes" value="{0:'xxl', 1:'xl', 2:'lg'}" />
            
            <h1 class="debug-marker">Image</h1>
            <f:variable name="imageClassName">w-full</f:variable>
            <f:render partial="Media/Media" arguments="{_all}"></f:render>
            <f:if condition="{data.bodytext}">
                <f:then>
                  <div class="px-4 2xl:px-2">
                    <p class="text-xs"><f:format.html>{data.bodytext}</f:format.html></p>
                  </div>
                </f:then>
            </f:if>
        </div>
    </f:section>
</html>
