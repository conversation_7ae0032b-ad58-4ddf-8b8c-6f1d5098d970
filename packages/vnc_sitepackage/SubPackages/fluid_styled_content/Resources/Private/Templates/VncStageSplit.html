<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:layout name="Default" />

    <f:section name="Main">
        <div class="container-viewport">
            <f:variable
                name="teaserContentLayout"
                value="half-boxed"
            ></f:variable>
            <f:variable
                name="teaserContentPosition"
                value="{data.vnc_content_layout}"
            ></f:variable>
            <f:variable name="headerSize" value="xl"></f:variable>
            <f:variable
                name="teaserContentBackground"
                value="default"
            ></f:variable>

            <f:variable
                name="record"
                value="{ data: data , files: { 0: files.0 } }"
            ></f:variable>
            <f:variable
                name="cropVariants"
                value="{
              default:'square',
              tablet:'square',
              mobile:'square'
          }"
            />
            <h1 class="debug-marker">STAGE SPLIT</h1>

            <f:render partial="Teaser" arguments="{_all}" />
        </div>
    </f:section>
</html>
