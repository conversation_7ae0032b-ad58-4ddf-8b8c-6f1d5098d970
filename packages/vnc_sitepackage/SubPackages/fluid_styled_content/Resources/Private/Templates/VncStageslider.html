<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:layout name="Default"/>

<f:section name="Main">
    <div class="container-viewport">

        <f:variable name="carouselType" value="stage"></f:variable>
        <f:variable name="carouselOptions" value="{
            &quot;arrows&quot;: true
        }"></f:variable>
        <f:variable name="sliderContent">
            <f:for each="{records}" as="slide" key="label">
                <div class="carousel__item splide__slide">
                    <f:variable name="record" value="{ data: slide.data, files: slide.images }"></f:variable>
                    
                    <h1 class="debug-marker">STAGE SLIDER</h1>

                    <f:variable name="teaserContentLayout" value="overlay"></f:variable>
                    <f:variable name="teaserContentPosition" value="left"></f:variable>
                    <f:variable name="headerSize" value="xl"></f:variable>
                    <f:variable name="teaserContentBackground" value="default"></f:variable>

                    <f:variable name="sizes" value="{0:'xxl', 1:'xl', 2:'lg', 3:'xs'}"/>
                    <f:variable name="cropVariants" value="{
                        default:'television',
                        tablet:'television',
                        mobile:'classicscreen'
                    }"/>

                    <f:render partial="Teaser" arguments="{_all}"></f:render>

                    
                </div>
            </f:for>
        </f:variable>

        <f:render partial="Carousel" arguments="{_all}"></f:render>
    </div>
</f:section>


</html>
