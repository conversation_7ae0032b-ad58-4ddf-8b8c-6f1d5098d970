<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:layout name="Default" />

    <f:section name="Main">
      <f:variable name="carouselOptions" value="{&quot;gap&quot;: &quot;1.5rem&quot, &quot;perPage&quot;:&quot;{data.vnc_content_columns}&quot;,&quot;breakpoints&quot;:{&quot;1024&quot;:{&quot;perPage&quot;:2, &quot;gap&quot;: &quot;1rem&quot;},&quot;768&quot;:{&quot;perPage&quot;:1.25, &quot;gap&quot;: &quot;0.75rem&quot;},&quot;360&quot;:{&quot;perPage&quot;:1.25, &quot;gap&quot;: &quot;0.75rem&quot;}}}"></f:variable>


        <div class="flex flex-col gap-content">
            <f:if condition="{data.header}">
                <div class="container-small flex flex-col gap-content">
                    <f:render partial="Header" arguments="{_all}" />
                </div>
            </f:if>
            <f:if condition="{images}">
              <f:variable name="carouselType" value="tiles"></f:variable>
              <f:variable name="imageCount">{images -> f:count()}</f:variable>
                <div class="container">
                    <f:variable name="sliderContent">
                        <f:for
                            each="{images}"
                            as="file"
                        >
               
                            <div class="splide__slide bg-red-500 w-12 h-12">
                                <p>....</p>
                            </div>
                        </f:for>
                    </f:variable>

                    <f:render partial="Carousel" arguments="{_all}"></f:render>
                </div>
            </f:if>
        </div>
    </f:section>
</html>
