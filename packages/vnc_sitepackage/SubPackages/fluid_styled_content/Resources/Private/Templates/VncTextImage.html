<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
<f:layout name="Default"/>

<f:section name="Main">
    <div
        class="container{f:if(condition: '{data.vnc_content_fullwidth}', then: '')}"
        >
        <f:for
            each="{files}"
            as="file"
            iteration="imgIterator"
            >

            <f:variable name="record" value="{ data: data, files: { 0: file } }"/>
            <f:variable name="teaserContentLayout" value="half-default"></f:variable>
            <f:variable name="teaserContentPosition" value="{data.vnc_content_layout}"></f:variable>
            <f:variable name="lightbox" value="true" />
            <f:variable name="download" value="true" />
            <h1 class="debug-marker">Text Image</h1>
            <f:render partial="Teaser" arguments="{_all}"/>
        </f:for>
    </div>
</f:section>

</html>
