<f:layout name="Default"/>

<f:section name="Main">
    <f:if condition="{event}">
        <f:then>
            <div class="container">
                <div id="c{data.uid}" class="row {f:if(condition:'{data.space_after_class}', then: '{data.space_after_class}')}">
                    <div class=">col-12 col-md-10 offset-md-1 col-lg-8 offset-lg-2 mt-5">
                        <article>
                            <div class="headline h2">
                                {event.title}
                            </div>
                            <p class="overline">
                                <f:if condition="{event.categoryIcon}">
                                    <i class="svg-icon {event.categoryIcon}"></i>
                                </f:if>
                                {event.category}
                            </p>
                            <div>
                                <i class="svg-icon icon nc-calendar-date"></i>
                                <f:format.date format="d.m.Y">{event.eventStartdate}</f:format.date>
                                <f:if condition="{event.eventEnddate}">
                                    - <f:format.date format="d.m.Y">{event.eventEnddate}</f:format.date>
                                </f:if>
                            </div>
                            <f:if condition="{event.eventStarttime} || {event.eventEndtime}">
                                <div>
                                    <i class="svg-icon icon nc-clock"></i>
                                    <f:format.date format="H:i">{event.eventStarttime}</f:format.date>
                                    <f:if condition="{event.eventEndtime}">
                                        - <f:format.date format="H:i">{event.eventEndtime}</f:format.date>
                                    </f:if>
                                </div>
                            </f:if>
                            <f:if condition="{event.location}">
                                <div>
                                    <i class="svg-icon icon nc-pin-3"></i>
                                    {event.location}
                                </div>
                            </f:if>
                            <f:if condition="{event.price}">
                                <div>
                                    <i class="svg-icon icon nc-round-euro"></i>
                                    {event.price}
                                </div>
                            </f:if>
                            <f:if condition="{event.bodytext}">
                                <div class="mt-3">
                                    <f:format.html>{event.bodytext}</f:format.html>
                                </div>
                            </f:if>
                            <br>
                            <p>
                                <f:if condition="{event.link}">
                                    <f:link.typolink parameter="{event.link}"
                                                     class="button button--secondary"
                                                     additionalAttributes="{'data-teaser-cta': 'true'}"
                                    >
                                        <f:if condition="{event.linkText}">
                                            <f:then>
                                                {event.linkText}
                                            </f:then>
                                            <f:else>
                                                <f:translate key="LLL:EXT:vnc_events/ContentBlocks/ContentElements/events/language/frontend.xlf:show_details" />
                                            </f:else>
                                        </f:if>
                                    </f:link.typolink>
                                </f:if>
                            </p>
                            <button
                                type="button"
                                class="button button--secondary mb-5"
                                onclick="history.back()"
                                tabindex="0"
                            >
                                <i class="svg-icon icon nc-arrow-left"></i>
                                <span class="caption">Zurück</span>
                            </button>
                        </article>
                    </div>
                </div>
            </div>
        </f:then>
        <f:else>
            <div class="container">
                <div id="c{data.uid}" class="row {f:if(condition:'{data.space_after_class}', then: '{data.space_after_class}')}">
                    <div class=">col-12 col-md-10 offset-md-1 col-lg-8 offset-lg-2 mt-5">
                        <h3>Leider konnte kein Event gefunden werden.</h3>
                    </div>
                </div>
            </div>
        </f:else>
    </f:if>
</f:section>
