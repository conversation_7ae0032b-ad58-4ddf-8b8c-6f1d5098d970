<f:layout name="Default"/>

<f:section name="Main">

    <div class="flex flex-col 2xl:w-[1920px] 2xl:h-[1500px] px-[264px] py-[0px] items-start gap-[8px]">
        <div id="c{data.uid}" class="row {f:if(condition:'{data.space_after_class}', then: '{data.space_after_class}')}">
            <div class="flex w-full 2xl:w-[920px] lg:w-[824px] md:w-[568px] sm:w-[336px] flex-col items-start gap-(--global-spacer-components-l,24px) p-3">
                <f:if condition="{data.header || data.bodytext}">
                    <article class="vnc_accordion__intro">
                        <f:if condition="{data.header}">
                            <f:render partial="Header" arguments="{_all}"/>
                        </f:if>
                        <f:if condition="{data.subheader}">
                            <p><strong>{data.subheader}</strong></p>
                        </f:if>
                        <f:if condition="{data.bodytext}">
                            <f:format.html>{data.bodytext}</f:format.html>
                        </f:if>
                    </article>
                </f:if>
            </div>
            <div class="col-12">
                <f:if condition="{events}">
                    <f:render section="Accordion" arguments="{_all}" />
                </f:if>
            </div>
        </div>
    </div>

</f:section>

<f:section name="Accordion">
    <div class="accordion" data-accordion data-vnc-events-accordion>
        <f:render section="AccordionFilter" arguments="{categories: categories}" />
        <f:for each="{events}" as="event" iteration="eventIterator">
            <f:render section="AccordionItem" arguments="{event: event}" />
        </f:for>
        <div class="my-4 text-center" data-vnc-events-pagination></div>
        <div data-vnc-events-list-button-more>
            <button class="button button--secondary d-block mx-auto">Weitere Events laden</button>
        </div>
    </div>
</f:section>

<f:section name="AccordionItem">
    <div class="accordion__item py-2 py-lg-0"
         data-accordion-item
         data-vnc-events-accordion-item
         data-vnc-events-category="{event.pid}"
         aria-hidden="false"
    >
        <h3 class="accordion_heading heading h5">
            <button
                type="button"
                class="accordion__toggle d-flex gap-2 w-100 text-start flex-column flex-lg-row justify-content-center justify-content-lg-between"
                id="accordion-toggle-{event.uid}"
                aria-expanded="false"
                aria-controls="accordion-body-{event.uid}"
                data-accordion-toggle
            >
                <span class="button__infoline_sm d-flex d-lg-none w-100 justify-content-between">
                    <span class="d-flex gap-1 align-items-center">
                        <i class="icon {event.categoryIcon}"></i>
                        <span class="d-block">{event.category}</span>
                    </span>
                    <span class="align-self-center">
                        <f:format.date format="d.m.Y">{event.eventStartdate}</f:format.date>
                        <f:if condition="{event.eventEnddate}">
                            - <f:format.date format="d.m.Y">{event.eventEnddate}</f:format.date>
                        </f:if>
                    </span>
                </span>
                <span class="d-flex gap-3 w-100">
                    <span class="text-center icon__info d-none d-lg-block">
                        <i class="icon {event.categoryIcon}"></i>
                        <span class="d-block">{event.category}</span>
                    </span>
                    <span class="d-flex flex-column flex-wrap justify-content-center justify-content-lg-between w-100">
                        <span>{event.title}</span>
                        <span class="button__infoline d-none d-lg-flex gap-3">
                            <span>
                                <i class="svg-icon icon nc-calendar-date"></i>
                                <f:format.date format="d.m.Y">{event.eventStartdate}</f:format.date>
                                <f:if condition="{event.eventEnddate}">
                                    - <f:format.date format="d.m.Y">{event.eventEnddate}</f:format.date>
                                </f:if>
                            </span>
                            <f:if condition="{event.eventStarttime} || {event.eventEndtime}">
                                <span>
                                    <i class="svg-icon icon nc-clock"></i>
                                    <f:format.date format="H:i">{event.eventStarttime}</f:format.date>
                                    <f:if condition="{event.eventEndtime}">
                                        - <f:format.date format="H:i">{event.eventEndtime}</f:format.date>
                                    </f:if>
                                </span>
                            </f:if>
                            <f:if condition="{event.location}">
                                <span>
                                    <i class="svg-icon icon nc-pin-3"></i>
                                    {event.location}
                                </span>
                            </f:if>
                            <f:if condition="{event.price}">
                                <span>
                                    <i class="svg-icon icon nc-round-euro"></i>
                                    {event.price}
                                </span>
                            </f:if>
                        </span>
                    </span>
                    <i class="svg-icon icon nc-circle-ctrl-down align-self-center"></i>
                </span>
            </button>
        </h3>
        <div
            class="accordion__panel is-collapsed"
            role="region"
            data-accordion-panel
            id="accordion-panel-{event.uid}"
            aria-labelledby="accordion-toggle-{event.uid}"
            hidden
        >
            <f:render section="AccordionItemContent" arguments="{_all}"></f:render>
        </div>
    </div>
</f:section>

<f:section name="AccordionItemContent">
    <div class="accordion__content">
        <div class="ce_text py-3">
            <article>
                <div class="d-lg-none">
                    <div>
                        <i class="svg-icon icon nc-calendar-date"></i>
                        <f:format.date format="d.m.Y">{event.eventStartdate}</f:format.date>
                        <f:if condition="{event.eventEnddate}">
                            - <f:format.date format="d.m.Y">{event.eventEnddate}</f:format.date>
                        </f:if>
                    </div>
                    <f:if condition="{event.eventStarttime} || {event.eventEndtime}">
                        <div>
                            <i class="svg-icon icon nc-clock"></i>
                            <f:format.date format="H:i">{event.eventStarttime}</f:format.date>
                            <f:if condition="{event.eventEndtime}">
                                - <f:format.date format="H:i">{event.eventEndtime}</f:format.date>
                            </f:if>
                        </div>
                    </f:if>
                    <f:if condition="{event.location}">
                        <div>
                            <i class="svg-icon icon nc-pin-3"></i>
                            {event.location}
                        </div>
                    </f:if>
                    <f:if condition="{event.price}">
                        <div>
                            <i class="svg-icon icon nc-round-euro"></i>
                            {event.price}
                        </div>
                    </f:if>
                    <f:if condition="{event.bodytext}">
                        <div class="mt-3">
                            <f:format.html>{event.bodytext}</f:format.html>
                        </div>
                    </f:if>
                </div>

                <f:format.html>{event.bodytext}</f:format.html>
            </article>
        </div>
    </div>
</f:section>

<f:section name="AccordionFilter">
    <div data-vnc-events-list-filter>
        <button data-filter-navigation class="flex items-center justify-center h-[48px] py-[20px] px-[32px] gap-[10px] border-(--styling-cta-button-border-radius,0px) bg-(--styling-cta-button-color-primary-default-fill) text-(--styling-cta-button-color-primary-default-text)">Filter</button>
        <div data-filter-content aria-hidden="true">
            <fieldset>
                <legend>Kategorie</legend>
                <f:for each="{categories}" as="category">
                    <div>
                        <label for="vnc-filter-{category.uid}">
                            <input type="checkbox"
                                   id="vnc-filter-{category.uid}"
                                   name="vnc-filter[]"
                                   value="{category.uid}"
                                   checked="checked"
                            />
                            {category.title}
                        </label>
                    </div>
                </f:for>
            </fieldset>
        </div>
    </div>
</f:section>
