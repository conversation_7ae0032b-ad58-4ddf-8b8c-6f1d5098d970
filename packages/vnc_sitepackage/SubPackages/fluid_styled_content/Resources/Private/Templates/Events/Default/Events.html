{namespace vnc=Vancado\VncEvents\ViewHelpers}
{namespace cb=TYPO3\CMS\ContentBlocks\ViewHelper}

<f:layout name="Default"/>

<f:section name="Main">
    <f:variable name="countEvents"><vnc:countEvents categories="{data.categories}" /></f:variable>
    {countEvents}
    <f:if condition="{data.layout} == 0">
        <f:then>
            <f:variable name="classContainer">flex w-full 2xl:w-[920px] lg:w-[824px] md:w-[568px] sm:w-[336px] flex-col items-start gap-(--global-spacer-components-l,24px) p-3</f:variable>
            <f:variable name="classCarousel">col-12</f:variable>
            <f:variable name="itemsPerSlide">{data.items_per_slide}</f:variable>
        </f:then>
        <f:else>
            <f:variable name="classContainer">col-12 col-md-7 col-lg-6 offset-lg-1</f:variable>
            <f:variable name="classCarousel">col-12 col-md-5 col-lg-4</f:variable>
            <f:variable name="itemsPerSlide">1</f:variable>
        </f:else>
    </f:if>
    <div class="flex flex-col items-center gap-[24px] gap-section-m">
        <div id="c{data.uid}" class="row {f:if(condition:'{data.space_after_class}', then: '{data.space_after_class}')}">
            <div class="{classContainer}">
                <f:if condition="{data.header || data.bodytext}">
                    <article class="flex flex-col items-start gap-(--global-spacer-components-m,16px)">
                        <f:if condition="{data.header}">
                            <f:render partial="Header" arguments="{_all}"/>
                        </f:if>
                        <f:if condition="{data.subheader}">
                            <p><strong>{data.subheader}</strong></p>
                        </f:if>
                        <f:if condition="{data.bodytext}">
                            <f:format.html>{data.bodytext}</f:format.html>
                        </f:if>
                        <f:if condition="{data.listPid.0}">
                            <f:link.typolink parameter="{data.listPid.0.uid}"
                                             class="flex items-center justify-center h-[48px] py-[20px] px-[32px] gap-[10px] border-(--styling-cta-button-border-radius,0px) bg-(--styling-cta-button-color-primary-default-fill) text-(--styling-cta-button-color-primary-default-text)"
                            >
                                <f:translate key="LLL:EXT:vnc_events/ContentBlocks/ContentElements/events/language/frontend.xlf:show_all_events" />
                            </f:link.typolink>
                        </f:if>
                    </article>
                </f:if>
            </div>
            <div class="{classCarousel}">
                <f:if condition="{countEvents} > 0">
                    <f:then>
                        <section data-vnc-events-carousel
                                 data-items-per-slide="{itemsPerSlide}"
                                 class="splide"
                                 aria-label="Basic Structure Example"
                        >
                            <div class="splide__track">
                                <div class="splide__list">
                                    <vnc:getEvents each="{data.categories}" as="event" iteration="iterator">
                                        <div class="splide__slide">
                                            <f:render partial="Events/Event" arguments="{event: event, iterator: iterator, data: data}" />
                                        </div>
                                    </vnc:getEvents>
                                </div>
                            </div>

                            <div class="splide__arrows">
                                <button class="splide__arrow splide__arrow--first">
                                    <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M26.9335 24.8996C26.7637 24.9813 26.5743 25.0134 26.3872 24.9921C26.2 24.9709 26.0226 24.8972 25.8755 24.7796L16.5005 17.2806V23.9996C16.5005 24.1881 16.4474 24.3727 16.3471 24.5322C16.2468 24.6917 16.1035 24.8197 15.9337 24.9013C15.7638 24.9829 15.5744 25.0149 15.3872 24.9936C15.1999 24.9722 15.0226 24.8984 14.8755 24.7806L4.87547 16.7806C4.75831 16.6869 4.66374 16.5681 4.59874 16.4329C4.53375 16.2977 4.5 16.1496 4.5 15.9996C4.5 15.8496 4.53375 15.7015 4.59874 15.5663C4.66374 15.4311 4.75831 15.3123 4.87547 15.2186L14.8755 7.21862C15.0226 7.10085 15.1999 7.02704 15.3872 7.00569C15.5744 6.98434 15.7638 7.01632 15.9337 7.09794C16.1035 7.17957 16.2468 7.30752 16.3471 7.46705C16.4474 7.62658 16.5005 7.81119 16.5005 7.99962V14.7186L25.8755 7.21862C26.0226 7.10085 26.1999 7.02704 26.3872 7.00569C26.5744 6.98434 26.7638 7.01632 26.9337 7.09794C27.1035 7.17957 27.2468 7.30752 27.3471 7.46705C27.4474 7.62658 27.5005 7.81119 27.5005 7.99962L27.5005 23.9996C27.5002 24.1878 27.4469 24.3721 27.3466 24.5313C27.2463 24.6905 27.1031 24.8181 26.9335 24.8996Z" fill="#333333"/>
                                    </svg>
                                </button>
                                <button class="splide__arrow splide__arrow--prev">
                                    <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M22.9489 25.8954C22.6089 26.0654 22.2089 26.0254 21.8989 25.7954L9.89893 16.7954C9.45893 16.4654 9.36893 15.8354 9.69893 15.3954C9.75893 15.3154 9.81893 15.2554 9.89893 15.1954L21.8989 6.19542C22.3389 5.86542 22.9689 5.95542 23.2989 6.39542C23.4289 6.57542 23.4989 6.78542 23.4989 7.00542V25.0054C23.4989 25.3854 23.2889 25.7354 22.9489 25.8954Z" fill="#333333"/>
                                    </svg>
                                </button>
                                <div class="splide__pagination"></div>
                                <button class="splide__arrow splide__arrow--next">
                                    <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M23.3 15.3989C23.24 15.3189 23.18 15.2589 23.1 15.1989L11.1 6.19893C10.66 5.86893 10.03 5.95893 9.7 6.39893C9.57 6.56893 9.5 6.77893 9.5 6.99893V24.9989C9.5 25.5489 9.95 25.9989 10.5 25.9989C10.72 25.9989 10.93 25.9289 11.1 25.7989L23.1 16.7989C23.54 16.4689 23.63 15.8389 23.3 15.3989Z" fill="#333333"/>
                                    </svg>
                                </button>
                                <button class="splide__arrow splide__arrow--last">
                                    <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M18.125 7.21938C17.9779 7.10161 17.8005 7.02779 17.6133 7.00644C17.4261 6.98509 17.2367 7.01707 17.0668 7.09869C16.897 7.18032 16.7537 7.30827 16.6534 7.4678C16.5531 7.62733 16.4999 7.81194 16.5 8.00038V14.7194L7.125 7.21938C6.97791 7.10161 6.80053 7.02779 6.61331 7.00644C6.42609 6.98509 6.23665 7.01707 6.06682 7.09869C5.89698 7.18032 5.75367 7.30827 5.65338 7.4678C5.5531 7.62733 5.49993 7.81194 5.5 8.00038V24.0004C5.49993 24.1888 5.5531 24.3734 5.65338 24.533C5.75367 24.6925 5.89698 24.8204 6.06682 24.9021C6.23665 24.9837 6.42609 25.0157 6.61331 24.9943C6.80053 24.973 6.97791 24.8991 7.125 24.7814L16.5 17.2814V24.0004C16.4999 24.1888 16.5531 24.3734 16.6534 24.533C16.7537 24.6925 16.897 24.8204 17.0668 24.9021C17.2367 24.9837 17.4261 25.0157 17.6133 24.9943C17.8005 24.973 17.9779 24.8991 18.125 24.7814L28.125 16.7814C28.2422 16.6877 28.3367 16.5689 28.4017 16.4337C28.4667 16.2985 28.5005 16.1504 28.5005 16.0004C28.5005 15.8504 28.4667 15.7023 28.4017 15.5671C28.3367 15.4319 28.2422 15.3131 28.125 15.2194L18.125 7.21938Z" fill="#333333"/>
                                    </svg>
                                </button>
                            </div>
                        </section>
                    </f:then>
                    <f:else>
                        <p class="mt-3">Keine Events vorhanden</p>
                    </f:else>
                </f:if>
            </div>
        </div>
    </div>
</f:section>
