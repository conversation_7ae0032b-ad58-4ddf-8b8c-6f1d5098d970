<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>

<f:layout name="Default"/>

<f:section name="Main">

    <f:variable name="carouselOptions" value="{&quot;gap&quot;: &quot;1.5rem&quot, &quot;perPage&quot;:&quot;{data.vnc_content_columns}&quot;,&quot;breakpoints&quot;:{&quot;1024&quot;:{&quot;perPage&quot;:{data.vnc_content_columns}, &quot;gap&quot;: &quot;1rem&quot;},&quot;768&quot;:{&quot;perPage&quot;:1, &quot;gap&quot;: &quot;0.75rem&quot;, &quot;padding&quot;: {&quot;right&quot;: &quot;15%&quot;}},&quot;360&quot;:{&quot;perPage&quot;:1, &quot;gap&quot;: &quot;0.75rem&quot;, &quot;padding&quot;: {&quot;right&quot;: &quot;15%&quot;}}}}"></f:variable>

    <div class=" flex flex-col gap-content">
        
        <f:if condition="{data.header}">
          <div class="container-small flex flex-col gap-content">
            <f:render partial="Header" arguments="{_all}"/>
          </div>
        </f:if>
        <div class="container">
        <f:variable name="sliderContent">
            <f:for each="{records}" as="record" iteration="recordIterator">
                <div class="splide__slide">
                        <h1 class="debug-marker">TEXT IMAGE TILES</h1>
                        <f:variable name="headerSize" value="xs"></f:variable>
                        <f:variable name="teaserContentLayout" value="tile"></f:variable>
                        <f:variable name="teaserContentBackground" value="default"></f:variable>
                        <f:variable name="embeddedTextPartial" value="true" />
                        <f:render partial="Teaser" arguments="{_all}"/>
                </div>
            </f:for>
        </f:variable>

        <f:render partial="Carousel" arguments="{_all}"></f:render>
      </div>
    </div>

</f:section>
