<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:layout name="Default"/>

<f:section name="Main">
    <div class="container-medium">
        <f:for each="{files}" as="file">
    
            <f:variable name="record" value="{ data: data, files: { 0: file } }"/>
            <f:variable name="teaserContentLayout" value="half-video"></f:variable>
            <f:variable name="teaserContentBackground" value="none"></f:variable>
            <f:variable name="teaserContentPosition" value="{data.vnc_content_layout}"></f:variable>
            <h1 class="debug-marker">VNC Text Media</h1>
       
            <f:render partial="Teaser" arguments="{_all}"/>

        </f:for>
    </div>
</f:section>

</html>




