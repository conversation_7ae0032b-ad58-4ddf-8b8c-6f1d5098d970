<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:layout name="Default" />
    <f:section name="Main">
        <div class="container">
            <div class="flex flex-col gap-6">
                <f:if condition="{data.header}">
                    <div class="container-small flex flex-col gap-content">
                        <f:render partial="Header" arguments="{_all}" />
                    </div>
                </f:if>
                <h1 class="debug-marker">TEXT TILES</h1>
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-{data.vnc_content_columns} gap-6"
                >
                    <f:for
                        each="{records}"
                        as="record"
                        iteration="recordIterator"
                    >
                        <f:if condition="{recordIterator.index} < 4">
                            
                            <f:variable name="headerSize" value="xs"></f:variable>
                            <f:variable name="teaserContentLayout" value="tile"></f:variable>
                            <f:variable name="teaserContentBackground" value="default"></f:variable>
                            <f:render partial="Teaser" arguments="{_all}"/>
                        </f:if>
                    </f:for>
                </div>
            </div>
        </div>
    </f:section>
</html>
