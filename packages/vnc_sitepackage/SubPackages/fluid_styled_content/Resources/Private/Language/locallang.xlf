<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" original="messages" datatype="plaintext" date="2025-06-13T15:09:48+02:00">
		<header>
			<authorName>Vancado</authorName>
		</header>
		<body>
			<trans-unit id="content.element.accordion.layout.1">
				<source>Without image</source>
			</trans-unit>
			<trans-unit id="content.element.accordion.layout.2">
				<source>With image square left</source>
			</trans-unit>
			<trans-unit id="content.element.accordion.layout.3">
				<source>With image portrait left</source>
			</trans-unit>
			<trans-unit id="content.element.bodytext">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="content.element.bodytext.description">
				<source></source>
			</trans-unit>
			<trans-unit id="content.element.bodytextsecondcol">
				<source>Text right column</source>
			</trans-unit>
			<trans-unit id="content.element.bodytextsecondcol.description">
				<source>Only visible in two-columns view</source>
			</trans-unit>
			<trans-unit id="content.element.columns">
				<source>Columns</source>
			</trans-unit>
			<trans-unit id="content.element.image">
				<source>Image</source>
			</trans-unit>
			<trans-unit id="content.element.imagevideo">
				<source>Image/Video</source>
			</trans-unit>
			<trans-unit id="content.element.images">
				<source>Images</source>
			</trans-unit>
			<trans-unit id="content.element.imagesubline">
				<source>Image Subline</source>
			</trans-unit>
			<trans-unit id="content.element.overline">
				<source>Overline</source>
			</trans-unit>
			<trans-unit id="content.element.overline.description">
				<source>Text above the headline</source>
			</trans-unit>
			<trans-unit id="content.element.introtext">
				<source>Intro Text</source>
			</trans-unit>
			<trans-unit id="content.element.introtext.description">
				<source>Text shown above the element</source>
			</trans-unit>
			<trans-unit id="content.element.layout">
				<source>View</source>
			</trans-unit>
			<trans-unit id="content.element.layout.imageLeft">
				<source>Image left</source>
			</trans-unit>
			<trans-unit id="content.element.layout.imageRight">
				<source>Image right</source>
			</trans-unit>
			<trans-unit id="content.element.link">
				<source>Link</source>
			</trans-unit>
			<trans-unit id="content.element.linktext">
				<source>Link text</source>
			</trans-unit>
			<trans-unit id="content.element.linktext.description">
				<source>Optional - when empty the target name or URI will be displayed</source>
			</trans-unit>
			<trans-unit id="content.element.type">
				<source>Content type</source>
			</trans-unit>
			<trans-unit id="content.element.type.big">
				<source>big</source>
			</trans-unit>
			<trans-unit id="content.element.type.image">
				<source>Image</source>
			</trans-unit>
			<trans-unit id="content.element.type.small">
				<source>small</source>
			</trans-unit>
			<trans-unit id="content.element.type.video">
				<source>Video</source>
			</trans-unit>
			<trans-unit id="content.element.video">
				<source>Video</source>
			</trans-unit>
			<trans-unit id="content.element.video.description">
				<source>Select a video</source>
			</trans-unit>
			<trans-unit id="content.element.vncfullsizeteaser">
				<source>Fullsize Teaser</source>
			</trans-unit>
			<trans-unit id="content.element.vncfullsizeteaser.description">
				<source>A Teaser spanning the full content width</source>
			</trans-unit>
			<trans-unit id="content.element.vncfullsizeteasersplit">
				<source>Fullsize Split Teaser</source>
			</trans-unit>
			<trans-unit id="content.element.vncfullsizeteasersplit.description">
				<source>A teaser in two halfs: Text and image spanning the full content width</source>
			</trans-unit>
			<trans-unit id="content.element.vncimage">
				<source>Image</source>
			</trans-unit>
			<trans-unit id="content.element.vncimage.description">
				<source>1 image with caption.</source>
			</trans-unit>
			<trans-unit id="content.element.vncgallery">
				<source> 2 images with captions (further content as carousel).</source>
			</trans-unit>
			<trans-unit id="content.element.vncgallery.description">
				<source>Shows multiple images in a gallery with optional enlarge and download Icons.</source>
			</trans-unit>
			<trans-unit id="content.element.image.description">
				<source>Please select image</source>
			</trans-unit>
			<trans-unit id="content.element.vncstage">
				<source>Full-width image without text, e.g. for content pages.</source>
			</trans-unit>
			<trans-unit id="content.element.vncstage.bodytext.description">
				<source>Simple Text, line breaks keep preserved</source>
			</trans-unit>
			<trans-unit id="content.element.vncstage.description">
				<source>Fullsize image or video with text and subline</source>
			</trans-unit>
			<trans-unit id="content.element.vncstageslider">
				<source>Image + Text Box Stage</source>
			</trans-unit>
			<trans-unit id="content.element.vncstageslider.description">
				<source>Full-width image with text box (further content as carousel) e.g. for the home page.</source>
			</trans-unit>
			<trans-unit id="content.element.vncstageslider.header">
				<source>Label</source>
			</trans-unit>
			<trans-unit id="content.element.vncstageslider.item">
				<source>Stage Slider</source>
			</trans-unit>
			<trans-unit id="content.element.vncstagesplit">
				<source>Image + Text Stage</source>
			</trans-unit>
			<trans-unit id="content.element.vncstagesplit.description">
				<source>Full-width module with separate areas for text and images, e.g. for the home page or content pages.</source>
			</trans-unit>
			<trans-unit id="content.element.vnctext">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="content.element.vnctext.description">
				<source>1 or 2-column text with intro including tables, lists and links, e.g. for body text.</source>
			</trans-unit>
			<trans-unit id="content.element.vnctext.layout.1">
				<source>1 column text</source>
			</trans-unit>
			<trans-unit id="content.element.vnctext.layout.2">
				<source>2 column text</source>
			</trans-unit>
			<trans-unit id="content.element.vnctextimage">
				<source>Image + Text</source>
			</trans-unit>
			<trans-unit id="content.element.vnctextimage.description">
				<source> Image and text (further content as carousel).</source>
			</trans-unit>
			<trans-unit id="content.element.vnctextimagetiles">
				<source>Image + Text Cards</source>
			</trans-unit>
			<trans-unit id="content.element.vnctextimagetiles.description">
				<source>2, 3 or 4-column content consisting of text and optional image (further as carousel) and intro, e.g. for teasers.</source>
			</trans-unit>
			<trans-unit id="content.element.vnctextimagetiles.item">
				<source>Text/Image Tiles</source>
			</trans-unit>
			<trans-unit id="content.element.vnctextmedia">
				<source>Video/Image + Text 16:9</source>
			</trans-unit>
			<trans-unit id="content.element.vnctextmedia.description">
				<source>Video or image in 16:9 format with text, e.g. for YouTube.</source>
			</trans-unit>
			<trans-unit id="content.element.vnctexttiles">
				<source>Quick Link Cards</source>
			</trans-unit>
			<trans-unit id="content.element.vnctexttiles.description">
				<source>2, 3 or 4-column texts and quick links (further content as carousel) and intro, e.g. for teasers.</source>
			</trans-unit>
			<trans-unit id="content.element.vnctexttiles.item">
				<source>Text Tiles</source>
			</trans-unit>
			<trans-unit id="content.item.field.bodytext">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="content.item.field.header">
				<source>Header</source>
			</trans-unit>
			<trans-unit id="content.item.field.subheader">
				<source>Subheader</source>
			</trans-unit>
			<trans-unit id="content.item.field.image">
				<source>Image</source>
			</trans-unit>
			<trans-unit id="content.item.field.link">
				<source>Link</source>
			</trans-unit>
			<trans-unit id="content.item.field.linktext">
				<source>Link text</source>
			</trans-unit>
			<trans-unit id="content.element.vncimageslider">
				<source>Banderole Gallery</source>
			</trans-unit>
			<trans-unit id="content.element.vncimageslider.description">
				<source>Narrow image gallery with uniform image height (flexible image width) with headline and text, e.g. for logos.</source>
			</trans-unit>
			<trans-unit id="content.element.vncintrotext">
				<source>Text Stage</source>
			</trans-unit>
			<trans-unit id="content.element.vncintrotext.description">
				<source>Vollflächiger Text ohne Bild z.B. für Pflichtseiten.</source>
			</trans-unit>
			<trans-unit id="content.element.field.downloadicon">
				<source>Download icon</source>
			</trans-unit>
			<trans-unit id="content.element.field.downloadicon.description">
				<source>Shows an icon to download the image</source>
			</trans-unit>
			<trans-unit id="content.element.field.enlargeicon">
				<source>Enlarge icon</source>
			</trans-unit>
			<trans-unit id="content.element.field.enlargeicon.description">
				<source>Shows an icon to enlarge the image</source>
			</trans-unit>
			<trans-unit id="backendlayout.subpage.stage">
				<source>Subpage Stage Area</source>
			</trans-unit>
			<trans-unit id="backendlayout.subpage">
				<source>Subpage</source>
			</trans-unit>
			<trans-unit id="backendlayout.subpage.content">
				<source>Content Area</source>
			</trans-unit>
			<trans-unit id="backendlayout.startpage">
				<source>Startpage</source>
			</trans-unit>
			<trans-unit id="backendlayout.startpage.stage">
				<source>Startpage Stage Area</source>
			</trans-unit>
			<trans-unit id="backendlayout.startpage.content">
				<source>Content Area</source>
			</trans-unit>
			<trans-unit id="backendlayout.footer">
				<source>Footer</source>
			</trans-unit>
			<trans-unit id="backendlayout.footer.content">
				<source>Footer Column</source>
			</trans-unit>
			<trans-unit id="newcontentelement.wizard.header">
				<source>Vancado Content Elements</source>
			</trans-unit>
			<trans-unit id="pagination.prev">
				<source>Previous</source>
			</trans-unit>
			<trans-unit id="pagination.next">
				<source>Next</source>
			</trans-unit>
			<trans-unit id="content.element.overlay">
				<source>Background color</source>
			</trans-unit>
			<trans-unit id="content.element.overlay.without">
				<source>without</source>
			</trans-unit>
			<trans-unit id="content.element.overlay.light">
				<source>hell</source>
			</trans-unit>
			<trans-unit id="content.element.overlay.dark">
				<source>dunkel</source>
			</trans-unit>
			<trans-unit id="accept_youtube">
				<source>Accept Youtube Services</source>
			</trans-unit>
			<trans-unit id="form.upload_select">
				<source>Select File</source>
			</trans-unit>
			<trans-unit id="form.delete_all_files">
				<source>Delete all files</source>
			</trans-unit>
			<trans-unit id="content.element.vnc_content_linktext.quicksearch">
				<source>Quicksearch</source>
			</trans-unit>
			<trans-unit id="content.element.vnc_content_linktext.quicksearch.description">
				<source>Comma seperated list of quicksearch queries below the search box</source>
			</trans-unit>
			<trans-unit id="content.element.space_after_class">
				<source>Space after class</source>
			</trans-unit>
		</body>
	</file>
</xliff>
