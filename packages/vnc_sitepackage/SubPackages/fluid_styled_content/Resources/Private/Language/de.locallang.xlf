<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" target-language="de" original="" datatype="plaintext" date="2025-06-13T15:09:48+02:00">
		<body>
			<trans-unit id="content.element.accordion.layout.1">
				<source>Without image</source>
				<target>Ohne Bild</target>
			</trans-unit>
			<trans-unit id="content.element.accordion.layout.2">
				<source>With image square left</source>
				<target>Mit Bild quadratisch links</target>
			</trans-unit>
			<trans-unit id="content.element.accordion.layout.3">
				<source>With image portrait left</source>
				<target>Mit Bild hochformat links</target>
			</trans-unit>
			<trans-unit id="content.element.bodytext">
				<source>Text</source>
				<target>Text</target>
			</trans-unit>
			<trans-unit id="content.element.bodytextsecondcol">
				<source>Text right column</source>
				<target>Text rechte Spalte</target>
			</trans-unit>
			<trans-unit id="content.element.bodytextsecondcol.description">
				<source>Only visible in two-columns view</source>
				<target>Wird nur in der Ansicht &#34;2-spaltiger Text mit Überschrift&#34; angezeigt</target>
			</trans-unit>
			<trans-unit id="content.element.columns">
				<source>Columns</source>
				<target>Spalten</target>
			</trans-unit>
			<trans-unit id="content.element.image">
				<source>Image</source>
				<target>Bild</target>
			</trans-unit>
			<trans-unit id="content.element.imagevideo">
				<source>Image/Video</source>
				<target>Bild/Video</target>
			</trans-unit>
			<trans-unit id="content.element.images">
				<source>Images</source>
				<target>Bilder</target>
			</trans-unit>
			<trans-unit id="content.element.imagesubline">
				<source>Image Subline</source>
				<target>Bildunterschrift</target>
			</trans-unit>
			<trans-unit id="content.element.overline">
				<source>Overline</source>
				<target>Overline</target>
			</trans-unit>
			<trans-unit id="content.element.overline.description">
				<source>Text above the headline</source>
				<target>Text oberhalb der Überschrift</target>
			</trans-unit>
			<trans-unit id="content.element.introtext">
				<source>Intro Text</source>
				<target>Introtext</target>
			</trans-unit>
			<trans-unit id="content.element.introtext.description">
				<source>Text shown above the element</source>
				<target>Text, der über dem Element angezeigt wird</target>
			</trans-unit>
			<trans-unit id="content.element.layout">
				<source>View</source>
				<target>Ansicht</target>
			</trans-unit>
			<trans-unit id="content.element.layout.imageLeft">
				<source>Image left</source>
				<target>Bild links</target>
			</trans-unit>
			<trans-unit id="content.element.layout.imageRight">
				<source>Image right</source>
				<target>Bild rechts</target>
			</trans-unit>
			<trans-unit id="content.element.link">
				<source>Link</source>
				<target>Link</target>
			</trans-unit>
			<trans-unit id="content.element.linktext">
				<source>Link text</source>
				<target>Link-Bezeichnung</target>
			</trans-unit>
			<trans-unit id="content.element.linktext.description">
				<source>Optional - when empty the target name or URI will be displayed</source>
				<target>Optional - Wenn leer, wird der Seitenname bzw. die URI angezeigt</target>
			</trans-unit>
			<trans-unit id="content.element.type">
				<source>Content type</source>
				<target>Inhaltsart</target>
			</trans-unit>
			<trans-unit id="content.element.type.big">
				<source>big</source>
				<target>groß</target>
			</trans-unit>
			<trans-unit id="content.element.type.image">
				<source>Image</source>
				<target>Bild</target>
			</trans-unit>
			<trans-unit id="content.element.type.small">
				<source>small</source>
				<target>klein</target>
			</trans-unit>
			<trans-unit id="content.element.type.video">
				<source>Video</source>
				<target>Video</target>
			</trans-unit>
			<trans-unit id="content.element.video">
				<source>Video</source>
				<target>Video</target>
			</trans-unit>
			<trans-unit id="content.element.video.description">
				<source>Select a video</source>
				<target>Bitte wählen Sie ein Video</target>
			</trans-unit>
			<trans-unit id="content.element.vncfullsizeteaser">
				<source>Fullsize Teaser</source>
				<target>Fullsize-Teaser</target>
			</trans-unit>
			<trans-unit id="content.element.vncfullsizeteasersplit">
				<source>Fullsize Split Teaser</source>
				<target>Fullsize-Split-Teaser</target>
			</trans-unit>
			<trans-unit id="content.element.vncfullsizeteasersplit.description">
				<source>A teaser in two halfs: Text and image spanning the full content width</source>
				<target>Ein Teaser in zwei Hälften: Text und Bild über die gesamte Contentbreite</target>
			</trans-unit>
			<trans-unit id="content.element.vncimage">
				<source>Image</source>
				<target>Image</target>
			</trans-unit>
			<trans-unit id="content.element.vncimage.description">
				<source>1 image with caption.</source>
				<target>1 Bild mit Bildunterschrift.</target>
			</trans-unit>
			<trans-unit id="content.element.vncgallery">
				<source> 2 images with captions (further content as carousel).</source>
				<target>2 Bilder mit Bildunterschriften (weitere Inhalte als Carousel).</target>
			</trans-unit>
			<trans-unit id="content.element.vncgallery.description">
				<source>Shows multiple images in a gallery with optional enlarge and download Icons.</source>
				<target>Zeigt mehrere Bilder in einer Galerie mit optionalen Vergrößern- und Download-Icons</target>
			</trans-unit>
			<trans-unit id="content.element.image.description">
				<source>Please select image</source>
				<target>Bitte wählen Sie ein Bild aus</target>
			</trans-unit>
			<trans-unit id="content.element.vncstage">
				<source>Full-width image without text, e.g. for content pages.</source>
				<target>Vollflächiges Bild ohne Text z.B. für Contentseiten.</target>
			</trans-unit>
			<trans-unit id="content.element.vncstage.bodytext.description">
				<source>Simple Text, line breaks keep preserved</source>
				<target>Einfacher Text, Zeilenumbrüche bleiben erhalten.</target>
			</trans-unit>
			<trans-unit id="content.element.vncstage.description">
				<source>Fullsize image or video with text and subline</source>
				<target>Fullsize Bild oder Video mit Text und Subline</target>
			</trans-unit>
			<trans-unit id="content.element.vncstageslider">
				<source>Image + Text Box Stage</source>
				<target>Image + Text Box Stage</target>
			</trans-unit>
			<trans-unit id="content.element.vncstageslider.description">
				<source>Full-width image with text box (further content as carousel) e.g. for the home page.</source>
				<target>Vollflächiges Bild mit Textbox (weitere Inhalte als Carousel) z.B. für die Startseite.</target>
			</trans-unit>
			<trans-unit id="content.element.vncstageslider.header">
				<source>Label</source>
				<target>Bezeichnung</target>
			</trans-unit>
			<trans-unit id="content.element.vncstageslider.item">
				<source>Stage Slider</source>
				<target>Stage-Slider</target>
			</trans-unit>
			<trans-unit id="content.element.vncstagesplit">
				<source>Image + Text Stage</source>
				<target>Stage-Split</target>
			</trans-unit>
			<trans-unit id="content.element.vncstagesplit.description">
				<source>Full-width module with separate areas for text and images, e.g. for the home page or content pages.</source>
				<target>Vollflächiges Modul mit separaten Flächen für Text und Bild z.B. für die Startseite und Contentseiten.</target>
			</trans-unit>
			<trans-unit id="content.element.vnctext">
				<source>Text</source>
				<target>Text</target>
			</trans-unit>
			<trans-unit id="content.element.vnctext.description">
				<source>1 or 2-column text with intro including tables, lists and links, e.g. for body text.</source>
				<target>1- oder 2-spaltiger Text mit Intro inkl. Tabellen, Listen und Links, z.B. für Fließtexte.</target>
			</trans-unit>
			<trans-unit id="content.element.vnctext.layout.1">
				<source>1 column text</source>
				<target>1-spaltiger Text</target>
			</trans-unit>
			<trans-unit id="content.element.vnctext.layout.2">
				<source>2 column text</source>
				<target>2-spaltiger Text</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextimage">
				<source>Image + Text</source>
				<target>Image + Text</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextimage.description">
				<source> Image and text (further content as carousel).</source>
				<target>Bild und Text (mehr Inhalte als Carousel).</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextimagetiles">
				<source>Image + Text Cards</source>
				<target>Image + Text Cards</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextimagetiles.description">
				<source>2, 3 or 4-column content consisting of text and optional image (further as carousel) and intro, e.g. for teasers.</source>
				<target>2-, 3- oder 4-spaltige Inhalte aus Text und optionalem Bild (weitere Inhalte als Carousel) und Intro z.B. für Teaser.</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextimagetiles.item">
				<source>Text/Image Tiles</source>
				<target>Text/Bild-Kacheln</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextmedia">
				<source>Video/Image + Text 16:9</source>
				<target>Video/Image + Text 16:9</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextmedia.description">
				<source>Video or image in 16:9 format with text, e.g. for YouTube.</source>
				<target>Video oder Bild im 16:9-Format mit Text z.B. für YouTube. </target>
			</trans-unit>
			<trans-unit id="content.element.vnctexttiles">
				<source>Quick Link Cards</source>
				<target>Quick Link Cards</target>
			</trans-unit>
			<trans-unit id="content.element.vnctexttiles.description">
				<source>2, 3 or 4-column texts and quick links (further content as carousel) and intro, e.g. for teasers.</source>
				<target>2-, 3- oder 4-spaltige Texte und Quick Links (weitere Inhalte als Carousel) und Intro z.B. für Teaser.</target>
			</trans-unit>
			<trans-unit id="content.element.vnctexttiles.item">
				<source>Text Tiles</source>
				<target>Text-Kacheln</target>
			</trans-unit>
			<trans-unit id="content.item.field.bodytext">
				<source>Text</source>
				<target>Text</target>
			</trans-unit>
			<trans-unit id="content.item.field.header">
				<source>Header</source>
				<target>Überschrift</target>
			</trans-unit>
			<trans-unit id="content.item.field.subheader">
				<source>Subheader</source>
				<target>Subheader</target>
			</trans-unit>
			<trans-unit id="content.item.field.image">
				<source>Image</source>
				<target>Bild</target>
			</trans-unit>
			<trans-unit id="content.item.field.link">
				<source>Link</source>
				<target>Link</target>
			</trans-unit>
			<trans-unit id="content.item.field.linktext">
				<source>Link text</source>
				<target>Linktext</target>
			</trans-unit>
			<trans-unit id="content.element.vncimageslider">
				<source>Banderole Gallery</source>
				<target>Banderole Gallery</target>
			</trans-unit>
			<trans-unit id="content.element.vncimageslider.description">
				<source>Narrow image gallery with uniform image height (flexible image width) with headline and text, e.g. for logos.</source>
				<target>Schmale Bildergalerie mit einheitlicher Bildhöhe (flexible Bildbreite) mit Headline und Text, z. B. für Logos.</target>
			</trans-unit>
			<trans-unit id="content.element.vncintrotext">
				<source>Text Stage</source>
				<target>Text Stage</target>
			</trans-unit>
			<trans-unit id="content.element.vncintrotext.description">
				<source>Vollflächiger Text ohne Bild z.B. für Pflichtseiten.</source>
				<target> Full-width text without image, e.g. for mandatory pages.</target>
			</trans-unit>
			<trans-unit id="content.element.field.downloadicon">
				<source>Download icon</source>
				<target>Download-Icon</target>
			</trans-unit>
			<trans-unit id="content.element.field.downloadicon.description">
				<source>Shows an icon to download the image</source>
				<target>Zeigt ein Icon um das Bild herunterzuladen</target>
			</trans-unit>
			<trans-unit id="content.element.field.enlargeicon">
				<source>Enlarge icon</source>
				<target>Vergrößern-Icon</target>
			</trans-unit>
			<trans-unit id="content.element.field.enlargeicon.description">
				<source>Shows an icon to enlarge the image</source>
				<target>Zeigt ein Icon um das Bild zu vergrößern</target>
			</trans-unit>
			<trans-unit id="backendlayout.subpage.stage">
				<source>Subpage Stage Area</source>
				<target>Unterseite Stage-Bereich</target>
			</trans-unit>
			<trans-unit id="backendlayout.subpage">
				<source>Subpage</source>
				<target>Unterseite</target>
			</trans-unit>
			<trans-unit id="backendlayout.subpage.content">
				<source>Content Area</source>
				<target>Inhalts-Bereich</target>
			</trans-unit>
			<trans-unit id="backendlayout.startpage">
				<source>Startpage</source>
				<target>Startseite</target>
			</trans-unit>
			<trans-unit id="backendlayout.startpage.stage">
				<source>Startpage Stage Area</source>
				<target>Startseite Stage-Bereich</target>
			</trans-unit>
			<trans-unit id="backendlayout.startpage.content">
				<source>Content Area</source>
				<target>Inhalts-Bereich</target>
			</trans-unit>
			<trans-unit id="backendlayout.footer">
				<source>Footer</source>
				<target>Footer</target>
			</trans-unit>
			<trans-unit id="backendlayout.footer.content">
				<source>Footer Column</source>
				<target>Footer Spalte</target>
			</trans-unit>
			<trans-unit id="newcontentelement.wizard.header">
				<source>Vancado Content Elements</source>
				<target>Vancado Content-Elemente</target>
			</trans-unit>
			<trans-unit id="pagination.prev">
				<source>Previous</source>
				<target>Zurück</target>
			</trans-unit>
			<trans-unit id="pagination.next">
				<source>Next</source>
				<target>Vor</target>
			</trans-unit>
			<trans-unit id="content.element.overlay">
				<source>Background color</source>
				<target>Hintergrund-Farbe</target>
			</trans-unit>
			<trans-unit id="content.element.overlay.without">
				<source>without</source>
				<target>ohne</target>
			</trans-unit>
			<trans-unit id="content.element.overlay.light">
				<source>hell</source>
				<target>hell</target>
			</trans-unit>
			<trans-unit id="content.element.overlay.dark">
				<source>dunkel</source>
				<target>dunkel</target>
			</trans-unit>
			<trans-unit id="accept_youtube">
				<source>Accept Youtube Services</source>
				<target>Zugriff auf Youtube gestatten</target>
			</trans-unit>
			<trans-unit id="form.upload_select">
				<source>Select File</source>
				<target>Datei auswählen</target>
			</trans-unit>
			<trans-unit id="form.delete_all_files">
				<source>Delete all files</source>
				<target>Alle Dateien löschen</target>
			</trans-unit>
			<trans-unit id="content.element.vnc_content_linktext.quicksearch">
				<source>Quicksearch</source>
				<target>Schnellsuche</target>
			</trans-unit>
			<trans-unit id="content.element.vnc_content_linktext.quicksearch.description">
				<source>Comma seperated list of quicksearch queries below the search box</source>
				<target>Komma separierte Liste von Suchbegriffen die unter dem Suchslot angezeigt werden</target>
			</trans-unit>
			<trans-unit id="content.element.space_after_class">
				<source>Space after class</source>
				<target>Abstand nach unten</target>
			</trans-unit>
		</body>
	</file>
</xliff>
