{namespace vnc=Vancado\VncWarnings\ViewHelpers}
<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    xmlns:vnc="http://typo3.org/ns/Vancado/VncSitepackage/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:layout name="Default" />
    <f:section name="content">

        <div class="container">
            <div class="row">
                <div class="col d-flex justify-content-center">
                    <f:if condition="{warnings}">
                        <f:for each="{warnings}" as="warning" iteration="warningIterator">
                            <f:if condition="{warning.uid}">
                                <f:then>
                                    <f:render partial="ModalDialogToggle" arguments="{
                                        uid: 'warning-modal-{warning.uid}',
                                        text: 'Toggle Warning {warning.uid}'
                                    }"/>
                                </f:then>
                            </f:if>
                        </f:for>
                    </f:if>
                </div>
            </div>
        </div>

        <f:if condition="{warnings}">
            <f:for each="{warnings}" as="warning" iteration="warningIterator">
                <f:if condition="{warning.uid}">
                    <f:then>
                        <f:variable name="modalHeader">
                            <h3 class="headline-m flex flex-col gap-3">
                                <i class="svg-icon svg-icon--l w-6 h-6">
                                    <svg class="icon" preserveAspectRatio="xMaxYMin">
                                        <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-warning" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
                                    </svg>
                                </i>
                                {warning.header -> f:format.raw()}
                            </h3>
                        </f:variable>
                        <f:variable name="modalContent">
                            <article class="mt-4 text-s">
                                <f:format.html>{warning.text}</f:format.html>
                                <f:link.typolink
                                    class="mt-4 button button--primary"
                                    parameter="{warning.link}"
                                >{warning.linkText}</f:link.typolink>
                            </article>
                        </f:variable>
                        <f:render partial="ModalDialog" arguments="{
                            uid: 'warning-modal-{warning.uid}',
                            size: 'lg',
                            open: 'true',
                            header: modalHeader,
                            content: modalContent
                        }"/>
                    </f:then>
                    <f:else>
                        current page is not in pages listed by item
                        {warning.uid} - {warningIterator.cycle}
                    </f:else>
                </f:if>
            </f:for>
        </f:if>
    </f:section>
</html>
