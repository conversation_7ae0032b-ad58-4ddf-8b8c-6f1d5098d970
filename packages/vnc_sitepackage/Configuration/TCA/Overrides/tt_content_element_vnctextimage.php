<?php

defined('TYPO3') || die();

$CTYPE = 'vnctextimage';
$EXTKEY = 'vnc_sitepackage';
$LOCALLANG = 'vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf';

/***************
 * Add Content Element
 */
if (!isset($GLOBALS['TCA']['tt_content']['types'][$CTYPE])) {
    $GLOBALS['TCA']['tt_content']['types'][$CTYPE] = [];
}

/***************
 * Add content element to seletor list
 */
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTcaSelectItem(
    'tt_content',
    'CType',
    [
        'LLL:EXT:' . $LOCALLANG . ':content.element.' . $CTYPE,
        $CTYPE,
        $CTYPE . '-icon'
    ],
    'text',
    'after'
);

/***************
 * Assign Icon
 */
$GLOBALS['TCA']['tt_content']['ctrl']['typeicon_classes'][$CTYPE] = $CTYPE . '-icon';

/***************
 * Configure element type
 */
$GLOBALS['TCA']['tt_content']['types'][$CTYPE] = array_replace_recursive(
    $GLOBALS['TCA']['tt_content']['types'][$CTYPE],
    [
        'showitem' => '--div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.general;general,
                header,
                header_layout,
                subheader,
                bodytext,
                vnc_content_link,
                vnc_content_linktext,
                assets,
                vnc_content_enlarge_icon,
                vnc_content_download_icon,
                vnc_content_layout,
                space_after_class,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:language,
                --palette--;;language,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access,
                --palette--;;hidden,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.access;access,',
        'columnsOverrides' => [

            'bodytext' => [
                'description' => 'LLL:EXT:' . $LOCALLANG . ':content.element.bodytext.description',
                'config' => [
                    'type' => 'text',
                    'cols' => '80',
                    'rows' => '5',
                    'enableRichtext' => true,
                    'richtextConfiguration' => 'VncContentBasic'
                ]
            ],
            'assets' => [
                'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.image',
                'config' => [
                    'maxitems' => 1,
                    'minitems' => 1,
                    'exclude' => 0,
                    'allowed' => 'jpg,jpeg,png',
                    'overrideChildTca' => [
                        'columns' => [
                            'uid_local' => [
                                'config' => [
                                    'appearance' => [
                                        'elementBrowserAllowed' => 'jpg,jpeg,png'
                                    ],
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'vnc_content_layout' => [
                'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.layout',
                'config' => [
                    'type' => 'select',
                    'renderType' => 'selectSingle',
                    'default' => 'left',
                    'items' => [
                        ['label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.layout.imageLeft', 'value' => 'left'],
                        ['label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.layout.imageRight', 'value' => 'right'],
                    ]
                ],
            ],
        ]
    ]
);

$GLOBALS['TCA']['tt_content']['types'][$CTYPE]['columnsOverrides']['assets']['config']['overrideChildTca']['columns']['crop']['config'] = [
    'cropVariants' => [
        'default' => [
            'title' => 'Default',
            'disabled' => false,
        ],
        'widescreen' => [
            'disabled' => true,
        ],
        'ultrawide' => [
            'disabled' => true,
        ],
        'univisium' => [
            'disabled' => true,
        ],
        'wide' => [
            'disabled' => true,
        ],

        'upright' => [
            'disabled' => true,
        ],
        'television' => [
            'disabled' => true,
        ],
        'square' => [
            'disabled' => true,
        ],
        'wideupright' => [
            'disabled' => true,
        ],
        'tvupright' => [
            'disabled' => true,
        ],
        'classicscreen' => [
            'disabled' => true,
        ],
        'screenupright' => [
            'disabled' => true,
        ],

    ],
];
