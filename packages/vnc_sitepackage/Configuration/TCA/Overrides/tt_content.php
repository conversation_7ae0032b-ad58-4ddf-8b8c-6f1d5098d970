<?php

// register all specific gw tt_content columns in one go with default settings
// may overwritten in tt_content_element_xy.php

$LOCALLANG = 'vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf';

$temporaryColumns = [
    'vnc_content_layout' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.layout',
        'config' => [
            'type' => 'select',
            'renderType' => 'selectSingle',
            'items' => [
                ['label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.vnctext.layout.1', 'value' => 1],
                ['label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.vnctext.layout.2', 'value' => 2],
            ]
        ],
    ],
    'vnc_content_download_icon' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.field.downloadicon',
        'description' => 'LLL:EXT:' . $LOCALLANG . ':content.element.field.downloadicon.description',
        'config' => [
            'type' => 'check',
            'items' => [
                ['label' => 'aktiv', 'value' => '1'],
            ],
        ]
    ],
    'vnc_content_enlarge_icon' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.field.enlargeicon',
        'description' => 'LLL:EXT:' . $LOCALLANG . ':content.element.field.enlargeicon.description',
        'config' => [
            'type' => 'check',
            'items' => [
                ['label' => 'aktiv', 'value' => '1'],
            ],
        ]
    ],
    'vnc_content_columns' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.columns',
        'config' => [
            'type' => 'select',
            'renderType' => 'selectSingle',
            'items' => [
                ['label' => 1,'value' => 1],
                ['label' => 2,'value' => 2],
                ['label' => 3,'value' => 3],
                ['label' => 4,'value' => 4],
            ],
            'default' => 4,
        ],
    ],
    'vnc_content_overline' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.overline',
        'description' => 'LLL:EXT:' . $LOCALLANG . ':content.element.overline.description',
        'config' => [
            'type' => 'input',
            'eval' => 'trim'
        ],
    ],
    'vnc_content_introtext' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.introtext',
        'description' => 'LLL:EXT:' . $LOCALLANG . ':content.element.introtext.description',
        'config' => [
            'type' => 'text',
            'cols' => '80',
            'rows' => '5',
            'enableRichtext' => false
        ],
    ],
    'bodytext' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.bodytext',
        'description' => 'LLL:EXT:' . $LOCALLANG . ':content.element.bodytext.description',
        'config' => [
            'type' => 'text',
            'cols' => '80',
            'rows' => '5',
            'enableRichtext' => true,
            'richtextConfiguration' => 'VncDefault'
        ],
    ],
    'vnc_content_bodytextsecondcol' => [
        'displayCond' => 'FIELD:vnc_content_layout:=:2',
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.bodytextsecondcol',
        'description' => 'LLL:EXT:' . $LOCALLANG . ':content.element.bodytextsecondcol.description',
        'config' => [
            'type' => 'text',
            'cols' => '80',
            'rows' => '5',
            'enableRichtext' => true,
            'richtextConfiguration' => 'VncDefault'
        ],
    ],
    'vnc_content_linktext' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.linktext',
        'description' => 'LLL:EXT:' . $LOCALLANG . ':content.element.linktext.description',
        'config' => [
            'type' => 'input',
            'eval' => 'trim'
        ],
    ],
    'vnc_content_link' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.link',
        'config' => [
            'type' => 'input',
            'renderType' => 'link',
            'size' => 1020,
            'eval' => 'trim'
        ],
    ],
    'vnc_content_type' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.type',
        'config' => [
            'type' => 'select',
            'renderType' => 'selectSingle',
            'default' => 'big',
            'items' => [
                ['label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.type.big', 'value' => 'big'],
                ['label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.type.small', 'value' => 'small']
            ]
        ],
    ],
    'vnc_content_overlay' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.overlay',
        'config' => [
            'type' => 'select',
            'renderType' => 'selectSingle',
            'default' => '',
            'items' => [
                ['label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.overlay.without', 'value' => ''],
                ['label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.overlay.light', 'value' => 'light'],
                ['label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.overlay.dark', 'value' => 'dark']
            ]
        ],
    ],
    'vnc_content_video' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.video.description',
        'description' => 'LLL:EXT:' . $LOCALLANG . ':content.element.vncstage.video.description',
        'config' => [
            'type' => 'file',
            'maxitems' => 1,
            'minitems' => 1,
            'allowed' => 'mp4,youtube,vimeo',
                'appearance' => [
                'fileUploadAllowed' => false,
                'fileByUrlAllowed' => false,
                'elementBrowserAllowed' => 'mp4,youtube,vimeo'
            ],
            'overrideChildTca' => [
                'columns' => [
                    'title' => [
                        'label' => 'Title',
                        'eval' => '',
                    ]
                ],
                'types' => [
                    \TYPO3\CMS\Core\Resource\FileType::IMAGE->value => [
                        # mit Komma anhängen wenn standard Palette (link usw.) wieder angezeigt werden soll: --palette--;;filePalette
                        'showitem' => 'title,alternative,crop, --palette--;;filePalette'
                    ],
                ],
            ],
        ],
    ],
    'vnc_content_stageslider_item' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.vncstageslider.item',
        'config' => [
            'type' => 'inline',
            'foreign_table' => 'vnc_content_stageslider_item',
            'foreign_field' => 'tt_content',
            'appearance' => [
                'useSortable' => true,
                'showSynchronizationLink' => true,
                'showAllLocalizationLink' => true,
                'showPossibleLocalizationRecords' => true,
                'expandSingle' => true,
                'enabledControls' => [
                    'localize' => true,
                ]
            ],
            'minitems' => 1,
            'maxitems' => 10,
            'behaviour' => [
                'mode' => 'select',
                'localizeChildrenAtParentLocalization' => true,
            ]
        ]
    ],
    'space_after_class' => [
        'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.space_after_class',
        'config' => [
            'type' => 'select',
            'renderType' => 'selectSingle',
            'default' => 'M',
            'items' => [
                ['label' => 'M', 'value' => 'gap-section-m'],
                ['label' => 'S', 'value' => 'gap-section-s'],
                ['label' => 'none', 'value' => 'gap-section-none']
            ]
        ],
    ],
];

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTCAcolumns(
    'tt_content',
    $temporaryColumns
);
