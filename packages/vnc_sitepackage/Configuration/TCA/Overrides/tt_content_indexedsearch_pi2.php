<?php

defined('TYPO3') || die();


$CTYPE = 'indexedsearch_pi2';

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addToAllTCAtypes(
    'tt_content',
    'vnc_content_linktext',
    $CTYPE,
    'replace:subheader'
);

/*\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addToAllTCAtypes(
    'tt_content',
    'space_after_class',
    $CTYPE,
    'after:vnc_content_linktext'
);
*/

$GLOBALS['TCA']['tt_content']['types'][$CTYPE] = array_replace_recursive(
    $GLOBALS['TCA']['tt_content']['types'][$CTYPE],
    [
        'columnsOverrides' => [
            'vnc_content_linktext' => [
                'label' =>
                    'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:content.element.vnc_content_linktext.quicksearch',
                'description' =>
                    'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:content.element.vnc_content_linktext.quicksearch.description',
                'config' => [
                    'size' => 128
                    ]
            ],
             'header_layout' => [
                 'config' => [
                     'width' => 32,
                     'cols' => '80',
                 ]
             ],
             'space_after_class' => [
                  'label' =>
                        'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:content.element.space_after_class',
             ],
        ],
    ]
);
