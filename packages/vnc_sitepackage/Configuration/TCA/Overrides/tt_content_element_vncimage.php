<?php

defined('TYPO3') || die();


$CTYPE = 'vncimage';
$EXTKEY = 'vnc_sitepackage';
$LOCALLANG = 'vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf';

/***************
 * Add Content Element
 */
if (!isset($GLOBALS['TCA']['tt_content']['types'][$CTYPE])) {
    $GLOBALS['TCA']['tt_content']['types'][$CTYPE] = [];
}

/***************
 * Add content element to selector list
 */
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTcaSelectItem(
    'tt_content',
    'CType',
    [
        'LLL:EXT:' . $LOCALLANG . ':content.element.' . $CTYPE,
        $CTYPE,
        $CTYPE . '-icon'
    ],
    'text',
    'after'
);

/***************
 * Assign Icon
 */
$GLOBALS['TCA']['tt_content']['ctrl']['typeicon_classes'][$CTYPE] = $CTYPE . '-icon';

/***************
 * Configure element type
 */
$GLOBALS['TCA']['tt_content']['types'][$CTYPE] = array_replace_recursive(
    $GLOBALS['TCA']['tt_content']['types'][$CTYPE],
    [
        'showitem' => '--div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.general;general,

                image,
                vnc_content_enlarge_icon,
                vnc_content_download_icon,
                bodytext,
                space_after_class,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:language,
                --palette--;;language,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access,
                --palette--;;hidden,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.access;access,',
        'columnsOverrides' => [
            'bodytext' => [
                'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.imagesubline'
                ],
            'image' => [
                'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.image',
                'config' => [
                    'type' => 'file',
                    'maxitems' => 1,
                    'minitems' => 1,
                    'allowed' => 'jpg,jpeg,png',
                    'appearance' => [
                        'fileUploadAllowed' => false,
                        'fileByUrlAllowed' => false,
                        'elementBrowserAllowed' => 'jpg,jpeg,png'
                    ],
                    'overrideChildTca' => [
                        'columns' => [
                            'title' => [
                                'label' => 'Title',
                                'eval' => '',
                            ]
                        ],
                        'types' => [
                            \TYPO3\CMS\Core\Resource\FileType::IMAGE->value => [
                                # mit Komma anhängen wenn standard Palette (link usw.) wieder angezeigt werden soll: --palette--;;filePalette
                                'showitem' => 'title,alternative,crop, --palette--;;filePalette'
                            ],
                        ],
                    ],
                ],
            ],

        ]
    ]
);

// configure here all element specific access restrictions to the std crop variants defined in
// packages/vnc_sitepackage/Configuration/TsConfig/Page/TCEFORM/CropVariants.tsconfig
$GLOBALS['TCA']['tt_content']['types'][$CTYPE]['columnsOverrides']['image']['config']['overrideChildTca']['columns']['crop']['config'] = [
    'cropVariants' => [
        'default' => [
            'disabled' => true,
        ],
        'ultrawide' => [
            'disabled' => true,
        ],
        'univisium' => [
            'disabled' => true,
        ],
        'wide' => [
            'disabled' => true,
        ],
        'widescreen' => [
            'disabled' => false,
        ],
        'upright' => [
            'disabled' => true,
        ],
        'television' => [
            'disabled' => true,
        ],
        'square' => [
            'disabled' => true,
        ],
        'wideupright' => [
            'disabled' => true,
        ],
        'tvupright' => [
            'disabled' => true,
        ],
        'classicscreen' => [
            'disabled' => true,
        ],
        'screenupright' => [
            'disabled' => true,
        ],
    ],
];
