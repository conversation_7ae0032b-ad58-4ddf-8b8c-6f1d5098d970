<?php

$LOCALLANG = 'vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf';
$GLOBALS['TCA']['vnc_content_stageslider_item']['ctrl']['security']['ignorePageTypeRestriction'] = true;
return [
    'ctrl' => [
        'label' => 'header',
        'sortby' => 'sorting',
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'title' => 'Stageslider Item',
        'delete' => 'deleted',
        'versioningWS' => true,
        'origUid' => 't3_origuid',
        'hideAtCopy' => true,
        'prependAtCopy' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.prependAtCopy',
        'transOrigPointerField' => 'l10n_parent',
        'transOrigDiffSourceField' => 'l10n_diffsource',
        'languageField' => 'sys_language_uid',
        'enablecolumns' => [
            'disabled' => 'hidden',
            'starttime' => 'starttime',
            'endtime' => 'endtime',
        ],
        'typeicon_classes' => [
            'default' => 'vncstageslider-icon'
        ],
        'security' => [
            'ignorePageTypeRestriction' => true,
        ],
    ],
    'types' => [
        '1' => [
            'showitem' => '
                --palette--;LLL:EXT:' . $LOCALLANG . ':content.element.vncstageslider;,
                header,
                subheader,
                bodytext,
                link,
                linktext,
                image,
				--div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:tabs.access,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.visibility;visibility,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.access;access,
                --palette--;;hiddenLanguagePalette,
            '
        ],
    ],
    'palettes' => [
        '1' => [
            'showitem' => ''
        ],
        'access' => [
            'showitem' => '
                starttime;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:starttime_formlabel,
                endtime;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:endtime_formlabel
            '
        ],
        'general' => [
            'showitem' => '
                tt_content
            '
        ],
        'visibility' => [
            'showitem' => '
                hidden;LLL:EXT:' . $LOCALLANG . ':content.element.vncstageslider.item
            '
        ],
        // hidden but needs to be included all the time, so sys_language_uid is set correctly
        'hiddenLanguagePalette' => [
            'showitem' => 'sys_language_uid, l10n_parent',
            'isHiddenPalette' => true,
        ],
    ],
    'columns' => [
        'tt_content' => [
            'exclude' => true,
            'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.vncstageslider',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'foreign_table' => 'tt_content',
                'foreign_table_where' => 'AND tt_content.pid=###CURRENT_PID### AND tt_content.CType="vncstageslider"',
                'maxitems' => 1,
                'default' => 0,
            ],
        ],
        'hidden' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.hidden',
            'config' => [
                'type' => 'check'
            ]
        ],
        'starttime' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.starttime',
            'config' => [
                'type' => 'datetime',
                'default' => 0
            ],
            'l10n_mode' => 'exclude',
            'l10n_display' => 'defaultAsReadonly'
        ],
        'endtime' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.endtime',
            'config' => [
                'type' => 'datetime',
                'default' => 0,
                'range' => [
                    'upper' => mktime(0, 0, 0, 1, 1, 2038)
                ]
            ],
            'l10n_mode' => 'exclude',
            'l10n_display' => 'defaultAsReadonly'
        ],
        'sys_language_uid' => [
            'exclude' => 1,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.language',
            'config' => ['type' => 'language']
        ],
        'l10n_parent' => [
            'displayCond' => 'FIELD:sys_language_uid:>:0',
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.l18n_parent',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'items' => [
                    [
                        'label' => '',
                        'value' => 0
                    ]
                ],
                'foreign_table' => 'vnc_content_stageslider_item',
                'foreign_table_where' =>
                    'AND vnc_content_stageslider_item.pid=###CURRENT_PID### AND
                    vnc_content_stageslider_item.sys_language_uid IN (-1,0)',
                'default' => 0
            ]
        ],
        'l10n_diffsource' => [
            'config' => [
                'type' => 'passthrough'
            ]
        ],

        'header' => [
            'exclude' => true,
            'label' => 'LLL:EXT:' . $LOCALLANG . ':content.item.field.header',
            'config' => [
                'type' => 'input',
                'size' => 50,
                'eval' => 'trim',
                'required' => true
            ],
        ],
        'subheader' => [
            'exclude' => true,
            'label' => 'LLL:EXT:' . $LOCALLANG . ':content.item.field.subheader',
            'config' => [
                'type' => 'input',
                'size' => 50,
                'eval' => 'trim',
                'required' => false
            ],
        ],
        'bodytext' => [
            'label' => 'LLL:EXT:' . $LOCALLANG . ':content.item.field.bodytext',
            'l10n_mode' => 'prefixLangTitle',
            'l10n_cat' => 'text',
            'config' => [
                'type' => 'text',
                'cols' => '80',
                'rows' => '5',
                'enableRichtext' => false,
                'eval' => 'trim'
            ],
        ],
        'image' => [
            'label' => 'Bild',
            'config' => [
                'type' => 'file',
                'maxitems' => 1,
                'minitems' => 0,
                'allowed' => 'jpg,jpeg,png',
                'appearance' => [
                    'fileUploadAllowed' => false,
                    'fileByUrlAllowed' => false,
                    'elementBrowserAllowed' => 'jpg,jpeg,png'
                ],
                'overrideChildTca' => [

                    'types' => [
                        '0' => [
                            'showitem' => '
                                --palette--;;imageoverlayPalette,
                                --palette--;;filePalette'
                        ],
                        \TYPO3\CMS\Core\Resource\FileType::IMAGE->value => [
                            'showitem' => 'crop,--palette--;;filePalette'
                        ],
                    ],
                    'columns' => [
                        'crop' => [
                            'config' =>
                                [
                                    'cropVariants' => [
                                        'default' => [
                                            'disabled' => true,
                                        ],
                                        'ultrawide' => [
                                            'disabled' => true,
                                        ],
                                        'univisium' => [
                                            'disabled' => false,
                                        ],
                                        'wide' => [
                                            'disabled' => true,
                                        ],
                                        'widescreen' => [
                                            'disabled' => true,
                                        ],
                                        'upright' => [
                                            'disabled' => true,
                                        ],
                                        'television' => [
                                            'disabled' => true,
                                        ],
                                        'square' => [
                                            'disabled' => true,
                                        ],
                                        'wideupright' => [
                                            'disabled' => true,
                                        ],
                                        'tvupright' => [
                                            'disabled' => true,
                                        ],
                                        'classicscreen' => [
                                            'disabled' => false,
                                        ],
                                        'screenupright' => [
                                            'disabled' => true,
                                        ],
                                    ],
                                ],
                        ],
                    ],
                ],
            ],
        ],

        'link' => [
            'exclude' => true,
            'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.link',
            'config' => [
                'type' => 'input',
                'renderType' => 'link',
                'size' => 1024,
                'eval' => 'trim'
            ],
        ],
        'linktext' => [
            'exclude' => true,
            'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.linktext',
            'description' => 'LLL:EXT:' . $LOCALLANG . ':content.element.linktext.description',
            'config' => [
                'type' => 'input',
                'size' => 25,
                'eval' => 'trim'
            ],
        ],
    ],
];
