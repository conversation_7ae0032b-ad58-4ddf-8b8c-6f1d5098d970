@import 'EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Configuration/TypoScript/*constants.typoscript'
@import 'EXT:vnc_sitepackage/SubPackages/indexed_search/Configuration/TypoScript/*constants.typoscript'
@import 'EXT:vnc_sitepackage/SubPackages/news/Configuration/TypoScript/*constants.typoscript'
@import 'EXT:vnc_sitepackage/SubPackages/seo/Configuration/TypoScript/*constants.typoscript'
@import 'EXT:vnc_sitepackage/SubPackages/tt_address/Configuration/TypoScript/*constants.typoscript'
@import 'EXT:vnc_sitepackage/SubPackages/vnc_events/Configuration/TypoScript/*constants.typoscript'
@import 'EXT:vnc_sitepackage/SubPackages/vnc_icon_formelement/Configuration/TypoScript/*constants.typoscript'
@import 'EXT:vnc_sitepackage/SubPackages/vnc_interactive_image/Configuration/TypoScript/*constants.typoscript'
@import 'EXT:vnc_sitepackage/SubPackages/vnc_powermail/Configuration/TypoScript/*constants.typoscript'
@import 'EXT:vnc_sitepackage/SubPackages/vnc_warnings/Configuration/TypoScript/*constants.typoscript'


plugin.tx_vncsitepackage {
	view {
		# cat=plugin.tx_vncsitepackage/file; type=string; label=Path to template root (FE)
		templateRootPath = EXT:vnc_sitepackage/Resources/Private/Templates/Page/
		# cat=plugin.tx_vncsitepackage/file; type=string; label=Path to template partials (FE)
		partialRootPath = EXT:vnc_sitepackage/Resources/Private/Partials/Page
		# cat=plugin.tx_vncsitepackage/file; type=string; label=Path to template layouts (FE)
		layoutRootPath = EXT:vnc_sitepackage/Resources/Private/Layouts/
	}
	settings {
    # cat=plugin.tx_vncsitepackage/file; type=string; label=Pid of site Root
    startPagePid = 1
    # cat=plugin.tx_vncsitepackage/file; type=string; label=Path static files
    siteLogo =EXT:vnc_sitepackage/Resources/Public/Images/logo.svg
		# cat=plugin.tx_vncsitepackage/file; type=string; label=Path layout images
    imagePath = EXT:vnc_sitepackage/Resources/Public/Images
    # cat=plugin.tx_vncsitepackage/file; type=string; label=Path static files
    svgSpriteFile = EXT:vnc_sitepackage/Resources/Public/dist/icons.svg
    # cat=plugin.tx_vncsitepackage/file; type=string; label=Path favicon set
    faviconPath = EXT:vnc_sitepackage/Resources/Public/Favicons/Default
    # cat=plugin.tx_vncsitepackage/Navigations; type=integer; label = Pid for meta navigation
    metaNavigationPid = 15
    # cat=plugin.tx_vncsitepackage/Navigations; type=integer; label = Pid for footer navigation
    footerNavigationPid = 16
    # cat=plugin.tx_vncsitepackage/Warnings; type=integer; label = Pid for warnings storage
    vncWarningsPid = 54
    # cat=plugin.tx_vncsitepackage/Navigations; type=string; label = Social Media link to instagram
    linkInstagram = #
    # cat=plugin.tx_vncsitepackage/Navigations; type=string; label = Social Media link to facebook
    linkFacebook = #
    # cat=plugin.tx_vncsitepackage/Navigations; type=string; label = Social Media link to linkedin
    linkLinkedin = #
    # cat=plugin.tx_vncsitepackage/Navigations; type=string; label = Social Media link to xing
    linkXing = #
    # cat=plugin.tx_vncsitepackage/Navigations; type=string; label = Social Media link to youtube
    linkYoutube = #
    # cat=plugin.tx_vncsitepackage/Navigations; type=string; label = Social Media link to X (formerly Twitter)
    linkX =
    }
}
