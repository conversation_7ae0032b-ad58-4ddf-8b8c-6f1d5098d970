<teaser>
    <ROOT>
        <TCEforms>
            <sheetTitle>Teaser Content</sheetTitle>
        </TCEforms>
        <type>array</type>
        <el>
            <settings.flexform.teaser.variant>
                <TCEforms>
                    <exclude>1</exclude>
                    <label>Ausgabeformat der Teaser</label>
                    <config>
                        <type>select</type>
                        <renderType>selectSingle</renderType>
                        <default>oneColumn</default>
                        <items type="array">
                            <numIndex index="0" type="array">
                                <numIndex index="0">Gesamte Breite</numIndex>
                                <numIndex index="1">oneColumn</numIndex>
                            </numIndex>
                        </items>
                    </config>
                </TCEforms>
            </settings.flexform.teaser.variant>
            <settings.flexform.teaser.header>
                <TCEforms>
                    <exclude>1</exclude>
                    <label>Überschrift</label>
                    <config>
                        <type>input</type>
                        <eval>trim</eval>
                    </config>
                </TCEforms>
            </settings.flexform.teaser.header>
            <settings.flexform.teaser.body>
                <TCEforms>
                    <exclude>1</exclude>
                    <label>Teaser text</label>
                    <config>
                        <type>text</type>
                        <enableRichtext>1</enableRichtext>
                        <richtextConfiguration>default</richtextConfiguration>
                    </config>
                    <defaultExtras>richtext[]:rte_transform[mode=ts_css]</defaultExtras>
                </TCEforms>
            </settings.flexform.teaser.body>
            <settings.flexform.teaser.buttonText>
                <TCEforms>
                    <exclude>1</exclude>
                    <label>Button Text</label>
                    <description>Standard: Formular öffnen</description>
                    <config>
                        <type>input</type>
                        <eval>trim</eval>
                    </config>
                </TCEforms>
            </settings.flexform.teaser.buttonText>
        </el>
    </ROOT>
</teaser>
