#buttons:
# Multi Klassenzuweisung im RTE
# Button config and defaults
#
buttons:
  link:
    options:
      removeItems: 'folder'
    relAttribute:
      enabled: false
    properties:
      class:
        # hierunter müssen alle css class pro classesAnchor block gelistet werden
        # elemente müssen unten definiert sein, sonst für alle Link-Typen gültig
        allowedClasses:
          - 'link -text'
          - 'btn -icon -text'
          - 'link -text -isExternal'
          - 'btn -icon -isExternal'
          - 'link -text -isDownload'
          - 'btn -icon -isDownload'
          - 'link -text -isEmail'
          - 'btn -icon -isEmail'
          - 'link -text -isPhone'
          - 'btn -icon -isPhone'
    page:
      properties:
        class:
          default: 'link -text'
    file:
      properties:
        class:
          default: 'link -text -isDownload'
    url:
      properties:
        class:
          default: 'link -text -isExternal'
    email:
      properties:
        class:
          default: 'link -text -isEmail'
    telephone:
      properties:
        class:
          default: 'link -text -isPhone'

# Classes Aliasnames
# Zuweisung der Class zum Linktyp
classes:
  # Internal
  'link -text':
    name: 'Interner Link'
  'btn -icon -text':
    name: 'Interner Link & Icon'
  # External
  'link -text -isExternal':
    name: 'Externer Link'
  'btn -icon -isExternal':
    name: 'Externer Link & Icon'
  # Downloads
  'link -text -isDownload':
    name: 'Datei Link'
  'btn -icon -isDownload':
    name: 'Datei Link & Icon'
  # E-Mails
  'link -text -isEmail':
    name: 'Email Link'
  'btn -icon -isEmail':
    name: 'Email Link & Icon'
  # Phone
  'link -text -isPhone':
    name: 'Telefonnummer Link'
  'btn -icon -isPhone':
    name: 'Telefonnummer Link & Icon'

#Classes config
# Zuordnung Linkstyle zu linktyp.
# wenn hier nicht definiert aber oben allowed, dann taucht oben def.css in allen typen auf
classesAnchor:
  # Internal
  linkInternal:
    class: 'link -text'
    type: 'page'
  linkInternalIcon:
    class: 'btn -icon -text'
    type: 'page'
  # External
  linkExternal:
    class: 'link -text -isExternal'
    type: 'url'
  linkExternalIcon:
    class: 'btn -icon -isExternal'
    type: 'url'
  # Downloads
  linkDownload:
    class: 'link -text -isDownload'
    type: 'file'
  linkDownloadIcon:
    class: 'btn -icon -isDownload'
    type: 'file'
  # E-Mail
  linkMail:
    class: 'link -text -isEmail'
    type: 'email'
  linkMailIcon:
    class: 'btn -icon -isEmail'
    type: 'email'
  # Phone
  linkTelephone:
    class: 'link -text -isPhone'
    type: 'telephone'
  linkTelephoneIcon:
    class: 'btn -icon -isPhone'
    type: 'telephone'
