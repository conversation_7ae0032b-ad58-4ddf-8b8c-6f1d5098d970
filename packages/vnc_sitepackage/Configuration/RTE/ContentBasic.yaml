imports:
  - { resource: "EXT:rte_ckeditor/Configuration/RTE/Processing.yaml" }
  - { resource: "EXT:rte_ckeditor/Configuration/RTE/Editor/Base.yaml" }
  - { resource: "EXT:rte_ckeditor/Configuration/RTE/Editor/Plugins.yaml" }
  - { resource: "EXT:vnc_sitepackage/Configuration/RTE/Links.yaml" }

editor:
  config:
    extraAllowedContent: "*(*)[data-*]; span(shy)"

    toolbarGroups:
      - { name: basicstyles, groups: [ basicstyles ] }
      - { name: paragraph, groups: [  list, indent, blocks, align ] }
      - { name: links, groups: [ links ] }

    extraPlugins:
      - linebreak

    removePlugins:
      - format
      - image
      - horizontalrule

    removeButtons:
      - Anchor
      - Italic
      - Underline
      - Strike
      - Blockquote
      - WbrTag

processing:
  allowTags:
    - wbr
