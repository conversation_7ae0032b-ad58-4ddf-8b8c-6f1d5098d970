mod {
  web_layout {
    BackendLayouts {
      subpage {
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:backendlayout.subpage
        config {
          backend_layout {
            colCount = 1
            rowCount = 2
            rows {
              1 {
                columns {
                  1 {
                    name = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:backendlayout.subpage.stage
                    colPos = 1
                    allowed {
                      CType = html, vncstage, vncstageslider, vncstagesplit, vncintrotext, vnc_infobox
                    }
                  }
                }
              }
              2 {
                columns {
                  1 {
                    name = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:backendlayout.subpage.content
                    colPos = 0
                    allowed {
                      CType = html, powermail_pi1, shortcut, list, vnctext, vnc_quotes, vnctextimage, vnctextmedia, vncimage, vncgallery, vnctextimagetiles, vnctexttiles, news_pi1, news_detailview, vncimageslider, vnc_accordion, vnc_counter, ttaddress_listview, vnc_teaser100split, vnc_teaser100textimage, indexedsearch_pi2, interactive-image-container, vnc_events, vnc_eventdetail, vnc_eventlist
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
