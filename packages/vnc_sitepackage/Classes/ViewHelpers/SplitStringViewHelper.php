<?php

namespace Vancado\VncSitepackage\ViewHelpers;

use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;

class SplitStringViewHelper extends AbstractViewHelper
{
    /**
     * Initialisiert die Argumente für den ViewHelper
     */
    public function initializeArguments(): void
    {
        $this->registerArgument('string', 'string', 'Der zu teilende String', true);
        $this->registerArgument('separator', 'string', 'Das Trennzeichen', true);
    }

    /**
     * @return array
     */
    public function render(): array
    {
        $string = $this->arguments['string'];
        $separator = $this->arguments['separator'];

        return explode($separator, $string);
    }
}
