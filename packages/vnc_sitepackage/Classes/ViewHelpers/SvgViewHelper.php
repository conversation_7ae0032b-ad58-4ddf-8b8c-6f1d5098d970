<?php

declare(strict_types=1);

namespace Vancado\VncSitepackage\ViewHelpers;

use TYPO3\CMS\Core\Resource\FileReference;
use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;

final class SvgViewHelper extends AbstractViewHelper
{
    protected $escapeOutput = false;

    /**
     * @return void
     */
    public function initializeArguments(): void
    {
        $this->registerArgument('file', 'object', 'The svg file which has to be rendered', true);
    }

    /**
     * @param array $arguments
     * @param \Closure $renderChildrenClosure
     * @param RenderingContextInterface $renderingContext
     * @return string
     */
    public static function renderStatic(array $arguments, \Closure $renderChildrenClosure, RenderingContextInterface $renderingContext): string
    {
        /** @var FileReference $file */
        $file = $arguments['file'];
        $content = $file->getOriginalFile()->getContents();

        if (!str_starts_with($content, '<?xml version="1.0" encoding="UTF-8"?>')) {
            return '';
        }

        return str_replace('<?xml version="1.0" encoding="UTF-8"?>', '', $content);
    }
}
