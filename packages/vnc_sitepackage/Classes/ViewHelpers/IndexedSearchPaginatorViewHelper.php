<?php

namespace Vancado\VncSitepackage\ViewHelpers;

use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Core\Utility\MathUtility;
use TYPO3\CMS\Core\Utility\PathUtility;
use TYPO3\CMS\Extbase\Utility\LocalizationUtility;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractTagBasedViewHelper;

class IndexedSearchPaginatorViewHelper extends AbstractTagBasedViewHelper
{
    protected static string $prefixId = 'tx_indexedsearch';
    protected $tagName = 'div';

    public function initializeArguments()
    {
        $this->registerArgument('maximumNumberOfResultPages', 'int', '', true);
        $this->registerArgument('numberOfResults', 'int', '', true);
        $this->registerArgument('resultsPerPage', 'int', '', true);
        $this->registerArgument('currentPage', 'int', '', false, 0);
        $this->registerArgument('freeIndexUid', 'int', '');
    }

    public function render()
    {
        $maximumNumberOfResultPages = $this->arguments['maximumNumberOfResultPages'];
        $numberOfResults = $this->arguments['numberOfResults'];
        $resultsPerPage = $this->arguments['resultsPerPage'];
        $currentPage = $this->arguments['currentPage'];
        $freeIndexUid = $this->arguments['freeIndexUid'];

        if ($resultsPerPage <= 0) {
            $resultsPerPage = 10;
        }
        $pageCount = (int)ceil($numberOfResults / $resultsPerPage);
        if ($pageCount === 1) {
            return '';
        }

        $currentPage = MathUtility::forceIntegerInRange($currentPage, 0, $pageCount - 1);

        $content = '';

        if ($currentPage === 0) {
            $label = LocalizationUtility::translate('LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:empty_prev');
            $content .= '<li class="nav__item">
                            <a aria-label="' . $label . '" class="pagination__link nav__link disabled">...</a>
                         </li>';
        }

        if ($currentPage > 0) {
            $label = LocalizationUtility::translate('displayResults.previous', 'IndexedSearch') ?? '';
            $content .= $this->makecurrentPageSelector_link($label, $currentPage - 1, $freeIndexUid, 'prev');
        }

        $maximumNumberOfResultPages = MathUtility::forceIntegerInRange($maximumNumberOfResultPages, 1, $pageCount, 10);
        $minPage = $currentPage - (int)floor($maximumNumberOfResultPages / 2);
        $maxPage = $minPage + $maximumNumberOfResultPages - 1;

        if ($minPage < 0) {
            $maxPage -= $minPage;
            $minPage = 0;
        } elseif ($maxPage >= $pageCount) {
            $minPage -= $maxPage - $pageCount + 1;
            $maxPage = $pageCount - 1;
        }

        $pageLabel = LocalizationUtility::translate('displayResults.page', 'IndexedSearch');
        for ($a = $minPage; $a <= $maxPage; $a++) {
            $label = trim($pageLabel . ' ' . ($a + 1));
            $attr = $a === $currentPage ? 'is-active' : '';
            $label = $this->makecurrentPageSelector_link($label, $a, $freeIndexUid, $attr);
            $content .= $label;
        }

        if ($currentPage < $pageCount - 1) {
            $label = LocalizationUtility::translate('displayResults.next', 'IndexedSearch') ?? '';
            $content .= $this->makecurrentPageSelector_link($label, $currentPage + 1, $freeIndexUid, 'next');
        }

        if ($currentPage === $pageCount - 1) {
            $label = LocalizationUtility::translate('LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:empty_next');
            $content .= '<li class="nav__item">
                            <a aria-label="' . $label . '" class="pagination__link nav__link disabled">...</a>
                         </li>';
        }

        if (!isset($this->additionalArguments['class'])) {
            $this->additionalArguments['class'] = 'pagination';
            $this->additionalArguments['aria-label'] = LocalizationUtility::translate('LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:search_pagination');
        }

        $this->tag->setContent('<ul class="pagination__list nav__list d-flex gap-4 justify-content-center align-items-center">' . $content . '</ul>');
        return $this->tag->render();
    }

    protected function makecurrentPageSelector_link($str, $p, $freeIndexUid, $attr = '')
    {
        $symboldefsPath = PathUtility::getAbsoluteWebPath(GeneralUtility::getFileAbsFileName('EXT:vnc_sitepackage/Resources/Public/dist/icons.svg'));
        $onclick = 'document.getElementById(' . GeneralUtility::quoteJSvalue(self::$prefixId . '_pointer') . ').value=' . GeneralUtility::quoteJSvalue((string)$p) . ';';

        if ($freeIndexUid !== null) {
            $onclick .= 'document.getElementById(' . GeneralUtility::quoteJSvalue(self::$prefixId . '_freeIndexUid') . ').value=' . GeneralUtility::quoteJSvalue($freeIndexUid) . ';';
        }

        $onclick .= 'document.getElementById(' . GeneralUtility::quoteJSvalue(self::$prefixId) . ').submit();return false;';

        if ($attr === 'next' || $attr === 'prev') {
            $directionClass = $attr === 'next' ? '' : ' button--mirrored';
            return '<li class="nav__item">
                <a class="pagination__link pagination__arrow' . $directionClass . '" role="button" title="" href="#" onclick="' . htmlspecialchars($onclick) . '">
                    <i class="svg-icon svg-icon--triangle-right">
                        <svg class="icon" preserveAspectRatio="xMaxYMin">
                            <use xlink:href="' . $symboldefsPath . '#icon-triangle-right" xmlns:xlink="http://www.w3.org/1999/xlink"></use>
                        </svg>
                    </i>
                </a></li>';
        }

        return '<li class="nav__item"><a href="#" class="pagination__link nav___link ' . $attr . '" onclick="' . htmlspecialchars($onclick) . '">' . htmlspecialchars($str) . '</a></li>';
    }
}
