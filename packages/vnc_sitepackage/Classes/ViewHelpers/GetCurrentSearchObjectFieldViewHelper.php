<?php

namespace Vancado\VncSitepackage\ViewHelpers;

use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Core\Information\Typo3Version;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;

class GetCurrentSearchObjectFieldViewHelper extends AbstractViewHelper
{
    public function initializeArguments()
    {
        $this->registerArgument('field', 'string', '', true);
    }

    /**
     * @inheritDoc
     */
    public function render(): mixed
    {
        $field = $this->arguments['field'];
        $request = $this->getRequest();
        $contentObject = $request->getAttribute('currentContentObject')->data;
        if ($field != 'vnc_content_linktext') { //used for quick-search-links
            return $contentObject[$field];
        }
        return explode(',', $contentObject[$field]);
    }

    private function getRequest(): ?ServerRequestInterface
    {

        if ($this->renderingContext->hasAttribute(ServerRequestInterface::class)) {
            return $this->renderingContext->getAttribute(ServerRequestInterface::class);
        }
        return null;
    }
}
