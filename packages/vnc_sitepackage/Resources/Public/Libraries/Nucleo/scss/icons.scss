/* --------------------------------

Nucleo Web Font
Generated using nucleoapp.com

-------------------------------- */
$icon-font-path: "../fonts" !default;

@font-face {
  font-family: 'Nucleo';
  src: url('#{$nc-font-path}/Nucleo.eot');
  src: url('#{$nc-font-path}/Nucleo.eot') format('embedded-opentype'),
    url('#{$nc-font-path}/Nucleo.woff2') format('woff2'),
    url('#{$nc-font-path}/Nucleo.woff') format('woff'),
    url('#{$nc-font-path}/Nucleo.ttf') format('truetype'),
    url('#{$nc-font-path}/Nucleo.svg') format('svg');
  font-weight: normal;
  font-style: normal;
}

/*------------------------
	base class definition
-------------------------*/

.icon {
  display: inline-block;
  font: normal normal normal 1em/1 'Nucleo';
  color: inherit;
  flex-shrink: 0;
  speak: none;
  text-transform: none;
  /* Better Font Rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/*------------------------
  change icon size
-------------------------*/

/* relative units */
.icon-sm {
  font-size: 0.8em;
}
.icon-lg {
  font-size: 1.2em;
}
/* absolute units */
.icon-16 {
  font-size: 16px;
}
.icon-32 {
  font-size: 32px;
}

/*------------------------
  spinning icons
-------------------------*/

.icon-is-spinning {
  animation: icon-spin 1s infinite linear;
}

@keyframes icon-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/*------------------------
  rotated/flipped icons
-------------------------*/

.icon-rotate-90  {
	transform: rotate(90deg);
}

.icon-rotate-180 {
	transform: rotate(180deg);
}

.icon-rotate-270 {
	transform: rotate(270deg);
}

.icon-flip-y {
	transform: scaleY(-1);
}
.icon-flip-x {
	transform: scaleX(-1);
}

/*------------------------
	icons
-------------------------*/
@function unicode($str) {
  @return unquote("\"\\#{$str}\"");
}

$nc-2x-drag-down: unicode(ea01);
$nc-2x-drag-up: unicode(ea03);
$nc-2x-swipe-down: unicode(ea05);
$nc-2x-swipe-left: unicode(ea02);
$nc-2x-swipe-right: unicode(ea04);
$nc-2x-swipe-up: unicode(ea06);
$nc-2x-tap: unicode(ea10);
$nc-3d-29: unicode(ea07);
$nc-3d-glasses: unicode(ea08);
$nc-3d-model: unicode(ea09);
$nc-3d-printing: unicode(ea0a);
$nc-3x-swipe-left: unicode(ea0b);
$nc-3x-swipe-right: unicode(ea0c);
$nc-3x-swipe-up: unicode(ea0d);
$nc-3x-tap: unicode(ea0e);
$nc-4x-swipe-left: unicode(ea0f);
$nc-4x-swipe-right: unicode(ea11);
$nc-4x-swipe-up: unicode(ea12);
$nc-a-add: unicode(ea13);
$nc-a-chart: unicode(ea14);
$nc-a-chat: unicode(ea15);
$nc-a-check: unicode(ea16);
$nc-a-delete: unicode(ea17);
$nc-a-edit: unicode(ea18);
$nc-a-heart: unicode(ea19);
$nc-a-location: unicode(ea1a);
$nc-a-remove: unicode(ea1b);
$nc-a-search: unicode(ea1c);
$nc-a-security: unicode(ea1d);
$nc-a-share: unicode(ea1e);
$nc-a-star: unicode(ea1f);
$nc-a-sync: unicode(ea20);
$nc-a-tag-add: unicode(ea21);
$nc-a-tag-remove: unicode(ea22);
$nc-a-tag: unicode(ea24);
$nc-a-time: unicode(ea23);
$nc-abc: unicode(ea25);
$nc-access-key: unicode(ea26);
$nc-accessibility-lift: unicode(ea27);
$nc-accessibility: unicode(ea28);
$nc-account: unicode(ea29);
$nc-acorn: unicode(ea2a);
$nc-active-38: unicode(ea2b);
$nc-active-40: unicode(ea2c);
$nc-adaptive-bike: unicode(ea2d);
$nc-add-27: unicode(ea2e);
$nc-add-29: unicode(ea2f);
$nc-add-fav: unicode(ea30);
$nc-add-favorite: unicode(ea31);
$nc-add-like: unicode(ea32);
$nc-add-notification: unicode(ea33);
$nc-add-to-cart-2: unicode(ea34);
$nc-add-to-cart: unicode(ea35);
$nc-add: unicode(ea36);
$nc-adult-content: unicode(ea37);
$nc-agenda-bookmark: unicode(ea38);
$nc-agenda: unicode(ea39);
$nc-ai: unicode(ea3a);
$nc-air-baloon: unicode(ea3b);
$nc-air-bomb: unicode(ea3c);
$nc-air-conditioner: unicode(ea3d);
$nc-airbag: unicode(ea3e);
$nc-airplane: unicode(ea3f);
$nc-airport-trolley: unicode(ea40);
$nc-airport: unicode(ea41);
$nc-alarm-add: unicode(ea42);
$nc-alarm-disable: unicode(ea43);
$nc-alarm: unicode(ea44);
$nc-album: unicode(ea45);
$nc-alcohol: unicode(ea46);
$nc-algorithm: unicode(ea47);
$nc-alien-29: unicode(ea48);
$nc-alien-33: unicode(ea49);
$nc-align-bottom: unicode(ea4a);
$nc-align-center-horizontal: unicode(ea4b);
$nc-align-center-vertical: unicode(ea4c);
$nc-align-center: unicode(ea4d);
$nc-align-justify: unicode(ea4e);
$nc-align-left-2: unicode(ea4f);
$nc-align-left: unicode(ea50);
$nc-align-right-2: unicode(ea51);
$nc-align-right: unicode(ea52);
$nc-align-top: unicode(ea53);
$nc-all-directions: unicode(ea54);
$nc-alpha-order: unicode(ea55);
$nc-ambulance: unicode(ea56);
$nc-ampersand: unicode(ea57);
$nc-analytics: unicode(ea58);
$nc-anchor: unicode(ea59);
$nc-android: unicode(ea5a);
$nc-angle: unicode(ea5b);
$nc-angry-10: unicode(ea5c);
$nc-angry-44: unicode(ea5d);
$nc-animation-14: unicode(ea5e);
$nc-animation-31: unicode(ea5f);
$nc-animation-32: unicode(ea60);
$nc-antenna: unicode(ea61);
$nc-anti-shake: unicode(ea62);
$nc-apartment: unicode(ea63);
$nc-aperture: unicode(ea64);
$nc-api: unicode(ea65);
$nc-app-services: unicode(ea66);
$nc-app-store: unicode(ea67);
$nc-app: unicode(ea68);
$nc-apple-2: unicode(ea69);
$nc-apple: unicode(ea6a);
$nc-appointment: unicode(ea6b);
$nc-apps: unicode(ea6c);
$nc-apron: unicode(ea6d);
$nc-arcade: unicode(ea6e);
$nc-archer: unicode(ea6f);
$nc-archery-target: unicode(ea70);
$nc-archery: unicode(ea71);
$nc-archive-check: unicode(ea72);
$nc-archive-content: unicode(ea73);
$nc-archive-doc-check: unicode(ea74);
$nc-archive-doc: unicode(ea75);
$nc-archive-drawer: unicode(ea76);
$nc-archive-file-check: unicode(ea77);
$nc-archive-file: unicode(ea78);
$nc-archive: unicode(ea79);
$nc-armchair: unicode(ea7a);
$nc-armor: unicode(ea7b);
$nc-army: unicode(ea7c);
$nc-arrow-bottom-left: unicode(ea7d);
$nc-arrow-bottom-right: unicode(ea7e);
$nc-arrow-down-2: unicode(ea7f);
$nc-arrow-down-3: unicode(ea80);
$nc-arrow-down: unicode(ea81);
$nc-arrow-e: unicode(ea82);
$nc-arrow-left-2: unicode(ea83);
$nc-arrow-left-3: unicode(ea84);
$nc-arrow-left: unicode(ea85);
$nc-arrow-n: unicode(ea86);
$nc-arrow-right-2: unicode(ea87);
$nc-arrow-right-3: unicode(ea88);
$nc-arrow-right: unicode(ea89);
$nc-arrow-s: unicode(ea8a);
$nc-arrow-sm-down: unicode(ea8b);
$nc-arrow-sm-left: unicode(ea8c);
$nc-arrow-sm-right: unicode(ea8d);
$nc-arrow-tool: unicode(ea8e);
$nc-arrow-top-left: unicode(ea8f);
$nc-arrow-top-right: unicode(ea90);
$nc-arrow-up-2: unicode(ea91);
$nc-arrow-up-3: unicode(ea92);
$nc-arrow-up: unicode(ea93);
$nc-arrow-w: unicode(ea94);
$nc-arrows-expand-2: unicode(ea95);
$nc-arrows-expand: unicode(ea96);
$nc-arrows-fullscreen-2: unicode(ea97);
$nc-arrows-fullscreen: unicode(ea98);
$nc-arrows-maximize-2: unicode(ea99);
$nc-arrows-maximize: unicode(ea9a);
$nc-arrows-opposite-directions: unicode(ea9b);
$nc-arrows-same-direction: unicode(ea9c);
$nc-artboard: unicode(ea9d);
$nc-artificial-brain: unicode(ea9e);
$nc-artificial-intelligence: unicode(ea9f);
$nc-assault-rifle: unicode(eaa0);
$nc-astronaut: unicode(eaa1);
$nc-astronomy: unicode(eaa2);
$nc-at-sign-2: unicode(eaa3);
$nc-at-sign: unicode(eaa4);
$nc-athletics: unicode(eaa5);
$nc-atm: unicode(eaa6);
$nc-atom: unicode(eaa7);
$nc-attach: unicode(eaa8);
$nc-attachment: unicode(eaa9);
$nc-aubergine: unicode(eaaa);
$nc-audio-description: unicode(eaab);
$nc-audio-jack: unicode(eaac);
$nc-audio-mixer: unicode(eaad);
$nc-augmented-reality: unicode(eaae);
$nc-auto-flash-2: unicode(eaaf);
$nc-auto-flash: unicode(eab0);
$nc-auto-focus: unicode(eab1);
$nc-automated-logistics: unicode(eab2);
$nc-avocado: unicode(eab3);
$nc-award-49: unicode(eab4);
$nc-award: unicode(eab5);
$nc-axe: unicode(eab6);
$nc-b-add: unicode(eab7);
$nc-b-chart: unicode(eab8);
$nc-b-check: unicode(eab9);
$nc-b-comment: unicode(eaba);
$nc-b-eye: unicode(eabb);
$nc-b-location: unicode(eabc);
$nc-b-love: unicode(eabd);
$nc-b-meeting: unicode(eabe);
$nc-b-remove: unicode(eabf);
$nc-b-security: unicode(eac0);
$nc-baby-bottle: unicode(eac1);
$nc-baby-car-seat: unicode(eac2);
$nc-baby-clothes: unicode(eac3);
$nc-baby-monitor: unicode(eac4);
$nc-baby-stroller: unicode(eac5);
$nc-baby: unicode(eac6);
$nc-back-arrow: unicode(eac7);
$nc-background: unicode(eac8);
$nc-backpack-2: unicode(eac9);
$nc-backpack-57: unicode(eaca);
$nc-backpack-58: unicode(eacb);
$nc-backpack: unicode(eacc);
$nc-backup: unicode(eacd);
$nc-backward: unicode(eace);
$nc-bacon: unicode(eacf);
$nc-badge-13: unicode(ead0);
$nc-badge-14: unicode(ead1);
$nc-badge-15: unicode(ead2);
$nc-badge: unicode(ead3);
$nc-bag-16: unicode(ead4);
$nc-bag-17: unicode(ead5);
$nc-bag-20: unicode(ead6);
$nc-bag-21: unicode(ead7);
$nc-bag-22: unicode(ead8);
$nc-bag-49: unicode(eadb);
$nc-bag-50: unicode(ead9);
$nc-bag-add-18: unicode(eada);
$nc-bag-add-21: unicode(eadc);
$nc-bag-delivery: unicode(eadd);
$nc-bag-edit: unicode(eade);
$nc-bag-remove-19: unicode(eadf);
$nc-bag-remove-22: unicode(eae0);
$nc-bag-time: unicode(eae1);
$nc-bag: unicode(eae2);
$nc-baggage-collection: unicode(eae3);
$nc-baggage-scale: unicode(eae4);
$nc-baguette: unicode(eae5);
$nc-bahai: unicode(eae6);
$nc-bakery: unicode(eae7);
$nc-balance: unicode(eae8);
$nc-baloon: unicode(eae9);
$nc-bamboo: unicode(eaea);
$nc-ban: unicode(eaeb);
$nc-banana: unicode(eaec);
$nc-bank-statement: unicode(eaed);
$nc-barbecue-15: unicode(eaee);
$nc-barbecue-tools: unicode(eaef);
$nc-barbecue: unicode(eaf0);
$nc-barbell: unicode(eaf1);
$nc-barbershop: unicode(eaf2);
$nc-barcode-qr: unicode(eaf3);
$nc-barcode-scan: unicode(eaf4);
$nc-barcode: unicode(eaf7);
$nc-bars-anim-3: unicode(eaf5);
$nc-bars-anim: unicode(eaf6);
$nc-baseball-bat: unicode(eaf8);
$nc-baseball-pitch: unicode(eaf9);
$nc-baseball-player: unicode(eafa);
$nc-baseball: unicode(eb03);
$nc-basket-add: unicode(eafb);
$nc-basket-edit: unicode(eafc);
$nc-basket-favorite: unicode(eafd);
$nc-basket-remove: unicode(eafe);
$nc-basket-search: unicode(eaff);
$nc-basket-share: unicode(eb00);
$nc-basket-simple-add: unicode(eb01);
$nc-basket-simple-remove: unicode(eb02);
$nc-basket-simple: unicode(eb04);
$nc-basket-update: unicode(eb05);
$nc-basket: unicode(eb06);
$nc-basketball-board: unicode(eb09);
$nc-basketball-player: unicode(eb07);
$nc-basketball-ring: unicode(eb08);
$nc-basketball: unicode(eb13);
$nc-bat: unicode(eb0a);
$nc-bath-faucet: unicode(eb0b);
$nc-bathroom-cabinet: unicode(eb0c);
$nc-bathtub: unicode(eb0d);
$nc-battery-charging: unicode(eb0e);
$nc-battery-level: unicode(eb0f);
$nc-battery-low: unicode(eb10);
$nc-battery-power: unicode(eb11);
$nc-battery-status: unicode(eb12);
$nc-battery: unicode(eb14);
$nc-beach-bat: unicode(eb15);
$nc-bear-2: unicode(eb16);
$nc-bear: unicode(eb17);
$nc-beard: unicode(eb18);
$nc-bed: unicode(eb19);
$nc-bedroom: unicode(eb1a);
$nc-bee: unicode(eb1b);
$nc-beer-95: unicode(eb1c);
$nc-beer-96: unicode(eb1d);
$nc-bell: unicode(eb1e);
$nc-belt: unicode(eb1f);
$nc-berlin: unicode(eb20);
$nc-beverage: unicode(eb21);
$nc-bicep: unicode(eb22);
$nc-big-eyes: unicode(eb23);
$nc-big-smile: unicode(eb24);
$nc-bigmouth: unicode(eb25);
$nc-bike-bmx: unicode(eb26);
$nc-bike: unicode(eb27);
$nc-bikini: unicode(eb29);
$nc-bill: unicode(eb28);
$nc-billboard: unicode(eb2a);
$nc-billiard-ball: unicode(eb2b);
$nc-bin: unicode(eb2c);
$nc-binoculars: unicode(eb2d);
$nc-biochemistry: unicode(eb2e);
$nc-biology: unicode(eb2f);
$nc-biscuit: unicode(eb30);
$nc-bitcoin: unicode(eb34);
$nc-bleah: unicode(eb31);
$nc-blend: unicode(eb32);
$nc-blender: unicode(eb33);
$nc-blindness: unicode(eb35);
$nc-block-down: unicode(eb36);
$nc-block-left: unicode(eb37);
$nc-block-right: unicode(eb38);
$nc-block-up: unicode(eb39);
$nc-block: unicode(eb3a);
$nc-blockchain: unicode(eb3b);
$nc-blog: unicode(eb3c);
$nc-blueberries: unicode(eb3d);
$nc-blueprint: unicode(eb3e);
$nc-bluetooth: unicode(eb3f);
$nc-board-2: unicode(eb40);
$nc-board-27: unicode(eb41);
$nc-board-28: unicode(eb42);
$nc-board-29: unicode(eb43);
$nc-board-30: unicode(eb44);
$nc-board-51: unicode(eb45);
$nc-board-game: unicode(eb46);
$nc-board: unicode(eb47);
$nc-boat-front: unicode(eb48);
$nc-boat-small-02: unicode(eb49);
$nc-boat-small-03: unicode(eb4a);
$nc-boat: unicode(eb4b);
$nc-body-back: unicode(eb4c);
$nc-body-butt: unicode(eb4d);
$nc-body-cream: unicode(eb50);
$nc-bodybuilder: unicode(eb4e);
$nc-boiling-water: unicode(eb4f);
$nc-bold: unicode(eb51);
$nc-bolt: unicode(eb52);
$nc-bomb: unicode(eb53);
$nc-bones: unicode(eb54);
$nc-book-39: unicode(eb55);
$nc-book-bookmark-2: unicode(eb56);
$nc-book-bookmark: unicode(eb57);
$nc-book-open-2: unicode(eb58);
$nc-book-open: unicode(eb59);
$nc-book: unicode(eb5a);
$nc-bookmark-add-2: unicode(eb5b);
$nc-bookmark-add: unicode(eb5c);
$nc-bookmark-delete-2: unicode(eb5d);
$nc-bookmark-delete: unicode(eb5e);
$nc-bookmark: unicode(eb5f);
$nc-bookmarks: unicode(eb60);
$nc-books-46: unicode(eb61);
$nc-books: unicode(eb62);
$nc-boot-2: unicode(eb63);
$nc-boot-woman: unicode(eb64);
$nc-boot: unicode(eb65);
$nc-border-radius: unicode(eb66);
$nc-border: unicode(eb6b);
$nc-bored: unicode(eb67);
$nc-botany: unicode(eb6d);
$nc-bottle-wine: unicode(eb68);
$nc-bottle: unicode(eb69);
$nc-bouquet: unicode(eb6a);
$nc-bow: unicode(eb6c);
$nc-bowl: unicode(eb6e);
$nc-bowling-ball: unicode(eb6f);
$nc-bowling-pins: unicode(eb70);
$nc-box-2: unicode(eb71);
$nc-box-3d-50: unicode(eb72);
$nc-box-arrow-bottom-left: unicode(eb73);
$nc-box-arrow-bottom-right: unicode(eb74);
$nc-box-arrow-down: unicode(eb75);
$nc-box-arrow-left: unicode(eb76);
$nc-box-arrow-right: unicode(eb77);
$nc-box-arrow-top-left: unicode(eb78);
$nc-box-arrow-top-right: unicode(eb79);
$nc-box-arrow-up: unicode(eb7a);
$nc-box-caret-down: unicode(eb7b);
$nc-box-caret-left: unicode(eb7c);
$nc-box-caret-right: unicode(eb7d);
$nc-box-caret-up: unicode(eb7e);
$nc-box-ctrl-down: unicode(eb7f);
$nc-box-ctrl-left: unicode(eb80);
$nc-box-ctrl-right: unicode(eb81);
$nc-box-ctrl-up: unicode(eb82);
$nc-box-ribbon: unicode(eb83);
$nc-box: unicode(eb84);
$nc-boxing-bag: unicode(eb85);
$nc-boxing-glove: unicode(eb86);
$nc-boxing: unicode(eb8b);
$nc-bra: unicode(eb87);
$nc-braille: unicode(eb88);
$nc-brain: unicode(eb89);
$nc-brakes: unicode(eb8a);
$nc-bread: unicode(eb91);
$nc-bride: unicode(eb8c);
$nc-briefcase-24: unicode(eb8d);
$nc-briefcase-25: unicode(eb8e);
$nc-briefcase-26: unicode(eb8f);
$nc-brightness: unicode(eb90);
$nc-brioche: unicode(eb92);
$nc-broccoli: unicode(eb93);
$nc-broken-heart: unicode(eb94);
$nc-broom: unicode(eb95);
$nc-browse: unicode(eb96);
$nc-browser-chrome: unicode(eb97);
$nc-browser-edge-legacy: unicode(eb98);
$nc-browser-edge: unicode(eb99);
$nc-browser-firefox: unicode(eb9a);
$nc-browser-ie: unicode(eb9b);
$nc-browser-opera: unicode(eb9c);
$nc-browser-safari: unicode(eb9d);
$nc-brush: unicode(eb9e);
$nc-btn-play-2: unicode(eb9f);
$nc-btn-play: unicode(eba4);
$nc-btn-stop: unicode(eba0);
$nc-bucket: unicode(eba1);
$nc-buddhism: unicode(eba2);
$nc-bug: unicode(eba3);
$nc-bulb-61: unicode(eba5);
$nc-bulb-62: unicode(eba6);
$nc-bulb-63: unicode(eba7);
$nc-bulb-saver: unicode(eba8);
$nc-bulb: unicode(eba9);
$nc-bullet-list-67: unicode(ebaa);
$nc-bullet-list-68: unicode(ebab);
$nc-bullet-list-69: unicode(ebac);
$nc-bullet-list-70: unicode(ebad);
$nc-bullet-list: unicode(ebae);
$nc-bullets: unicode(ebaf);
$nc-bureau-dresser: unicode(ebb5);
$nc-bus-front-10: unicode(ebb0);
$nc-bus-front-12: unicode(ebb1);
$nc-bus: unicode(ebb2);
$nc-business-agent: unicode(ebb4);
$nc-business-contact-85: unicode(ebb3);
$nc-businessman-03: unicode(ebb6);
$nc-businessman-04: unicode(ebb7);
$nc-butter: unicode(ebb8);
$nc-butterfly: unicode(ebb9);
$nc-button-2: unicode(ebba);
$nc-button-eject: unicode(ebbb);
$nc-button-next: unicode(ebbc);
$nc-button-pause: unicode(ebbd);
$nc-button-play: unicode(ebbe);
$nc-button-power: unicode(ebbf);
$nc-button-previous: unicode(ebc0);
$nc-button-record: unicode(ebc1);
$nc-button-rewind: unicode(ebc2);
$nc-button-skip: unicode(ebc3);
$nc-button-stop: unicode(ebc4);
$nc-button: unicode(ebc5);
$nc-buzz: unicode(ebc6);
$nc-c-add: unicode(ebc7);
$nc-c-check: unicode(ebc8);
$nc-c-delete: unicode(ebc9);
$nc-c-edit: unicode(ebca);
$nc-c-info: unicode(ebcb);
$nc-c-pulse: unicode(ebcc);
$nc-c-question: unicode(ebcd);
$nc-c-remove: unicode(ebce);
$nc-c-warning: unicode(ebcf);
$nc-cabinet: unicode(ebd0);
$nc-cable: unicode(ebd2);
$nc-cactus: unicode(ebd1);
$nc-cake-13: unicode(ebd3);
$nc-cake-2: unicode(ebd4);
$nc-cake-slice: unicode(ebd5);
$nc-cake: unicode(ebd6);
$nc-calculator: unicode(ebd7);
$nc-calendar-2: unicode(ebd8);
$nc-calendar-date-2: unicode(ebd9);
$nc-calendar-date: unicode(ebda);
$nc-calendar-day-view: unicode(ebdb);
$nc-calendar-event-2: unicode(ebdc);
$nc-calendar-event-create: unicode(ebdd);
$nc-calendar-event: unicode(ebde);
$nc-calendar: unicode(ebdf);
$nc-call-doctor: unicode(ebe0);
$nc-camcorder: unicode(ebe1);
$nc-camera-2: unicode(ebe2);
$nc-camera-3: unicode(ebe3);
$nc-camera-button: unicode(ebe4);
$nc-camera-flash: unicode(ebe5);
$nc-camera-flashlight: unicode(ebe6);
$nc-camera-focus-2: unicode(ebe7);
$nc-camera-focus: unicode(ebe8);
$nc-camera-lens: unicode(ebe9);
$nc-camera-roll: unicode(ebea);
$nc-camera-screen: unicode(ebeb);
$nc-camera-shooting: unicode(ebec);
$nc-camera-timer: unicode(ebed);
$nc-camera: unicode(ebee);
$nc-camper: unicode(ebef);
$nc-camping: unicode(ebf5);
$nc-can: unicode(ebf0);
$nc-candle: unicode(ebf1);
$nc-candlestick-chart: unicode(ebf2);
$nc-candy-2: unicode(ebf3);
$nc-candy: unicode(ebf4);
$nc-canvas: unicode(ebf6);
$nc-cap: unicode(ebf7);
$nc-capitalize: unicode(ebf8);
$nc-caps-all: unicode(ebf9);
$nc-caps-small: unicode(ebfa);
$nc-car-2: unicode(ebfb);
$nc-car-accident: unicode(ebfc);
$nc-car-connect: unicode(ebfd);
$nc-car-door: unicode(ebfe);
$nc-car-front: unicode(ebff);
$nc-car-lights: unicode(ec00);
$nc-car-parking: unicode(ec01);
$nc-car-simple: unicode(ec02);
$nc-car-sport: unicode(ec03);
$nc-car-ventilation: unicode(ec04);
$nc-car-wash: unicode(ec05);
$nc-car: unicode(ec06);
$nc-card-edit: unicode(ec07);
$nc-card-favorite: unicode(ec08);
$nc-card-remove: unicode(ec09);
$nc-card-update: unicode(ec0a);
$nc-cards: unicode(ec0b);
$nc-caret-sm-up: unicode(ec0c);
$nc-carrot: unicode(ec0d);
$nc-cart-add-9: unicode(ec0e);
$nc-cart-add: unicode(ec0f);
$nc-cart-favorite: unicode(ec10);
$nc-cart-full: unicode(ec11);
$nc-cart-refresh: unicode(ec12);
$nc-cart-remove-9: unicode(ec13);
$nc-cart-remove: unicode(ec14);
$nc-cart-return: unicode(ec15);
$nc-cart-simple-add: unicode(ec16);
$nc-cart-simple-remove: unicode(ec17);
$nc-cart-speed: unicode(ec18);
$nc-cart: unicode(ec19);
$nc-cash-register: unicode(ec1a);
$nc-casino-chip: unicode(ec1b);
$nc-casino: unicode(ec1c);
$nc-castle: unicode(ec1d);
$nc-cat: unicode(ec1e);
$nc-catalog: unicode(ec1f);
$nc-cauldron: unicode(ec20);
$nc-cctv: unicode(ec21);
$nc-cd-reader: unicode(ec22);
$nc-celsius: unicode(ec23);
$nc-centralize: unicode(ec24);
$nc-certificate: unicode(ec25);
$nc-chain: unicode(ec26);
$nc-chair: unicode(ec2b);
$nc-chalkboard: unicode(ec27);
$nc-champagne: unicode(ec28);
$nc-chandelier: unicode(ec29);
$nc-change-direction: unicode(ec2a);
$nc-charger-cable: unicode(ec2c);
$nc-chart-bar-32: unicode(ec2d);
$nc-chart-bar-33: unicode(ec2e);
$nc-chart-growth: unicode(ec2f);
$nc-chart-pie-35: unicode(ec30);
$nc-chart-pie-36: unicode(ec31);
$nc-chart: unicode(ec32);
$nc-chat: unicode(ec33);
$nc-check-all: unicode(ec34);
$nc-check-double: unicode(ec35);
$nc-check-in: unicode(ec36);
$nc-check-list: unicode(ec37);
$nc-check-out: unicode(ec38);
$nc-check-single: unicode(ec39);
$nc-check: unicode(ec3a);
$nc-checkbox-btn-checked: unicode(ec3b);
$nc-checkbox-btn: unicode(ec3c);
$nc-cheese-24: unicode(ec3d);
$nc-cheese-87: unicode(ec3e);
$nc-cheeseburger: unicode(ec3f);
$nc-chef-hat: unicode(ec40);
$nc-chef: unicode(ec41);
$nc-chemistry: unicode(ec42);
$nc-cheque-2: unicode(ec43);
$nc-cheque-3: unicode(ec44);
$nc-cheque: unicode(ec45);
$nc-chequered-flag: unicode(ec46);
$nc-cherry: unicode(ec47);
$nc-chess-bishop: unicode(ec48);
$nc-chess-king: unicode(ec49);
$nc-chess-knight: unicode(ec4a);
$nc-chess-pawn: unicode(ec4b);
$nc-chess-queen: unicode(ec4c);
$nc-chess-tower: unicode(ec4d);
$nc-chicken-2: unicode(ec4e);
$nc-chicken: unicode(ec4f);
$nc-child: unicode(ec50);
$nc-chili: unicode(ec51);
$nc-chimney: unicode(ec52);
$nc-china: unicode(ec53);
$nc-chips: unicode(ec54);
$nc-choco-cream: unicode(ec55);
$nc-chocolate-mousse: unicode(ec56);
$nc-chocolate: unicode(ec57);
$nc-christianity: unicode(ec58);
$nc-church: unicode(ec59);
$nc-churros: unicode(ec5a);
$nc-cinema: unicode(ec5b);
$nc-circle-08: unicode(ec5c);
$nc-circle-09: unicode(ec5e);
$nc-circle-10: unicode(ec5d);
$nc-circle-anim-2: unicode(ec5f);
$nc-circle-anim-3: unicode(ec60);
$nc-circle-anim: unicode(ec61);
$nc-circle-arrow-down: unicode(ec62);
$nc-circle-arrow-left: unicode(ec63);
$nc-circle-arrow-right: unicode(ec64);
$nc-circle-arrow-up: unicode(ec65);
$nc-circle-caret-down: unicode(ec66);
$nc-circle-caret-left: unicode(ec67);
$nc-circle-caret-right: unicode(ec68);
$nc-circle-caret-up: unicode(ec69);
$nc-circle-ctrl-down: unicode(ec6a);
$nc-circle-ctrl-left: unicode(ec6b);
$nc-circle-ctrl-right: unicode(ec6c);
$nc-circle-ctrl-up: unicode(ec6d);
$nc-circle-in: unicode(ec6e);
$nc-circle-out: unicode(ec6f);
$nc-circle: unicode(ec70);
$nc-circuit-round: unicode(ec71);
$nc-circuit: unicode(ec72);
$nc-clapperboard-2: unicode(ec73);
$nc-clapperboard: unicode(ec74);
$nc-clarinet: unicode(ec7c);
$nc-clear-data: unicode(ec75);
$nc-climbing: unicode(ec76);
$nc-clock-anim: unicode(ec77);
$nc-clock: unicode(ec78);
$nc-clone: unicode(ec79);
$nc-closed-captioning: unicode(ec7a);
$nc-clothes-hanger: unicode(ec7b);
$nc-clothing-hanger: unicode(ec7d);
$nc-cloud-data-download: unicode(ec7e);
$nc-cloud-download: unicode(ec7f);
$nc-cloud-drop: unicode(ec80);
$nc-cloud-fog-31: unicode(ec81);
$nc-cloud-fog-32: unicode(ec82);
$nc-cloud-forecast: unicode(ec83);
$nc-cloud-hail: unicode(ec84);
$nc-cloud-light: unicode(ec85);
$nc-cloud-mining: unicode(ec86);
$nc-cloud-moon: unicode(ec87);
$nc-cloud-rain: unicode(ec88);
$nc-cloud-rainbow: unicode(ec89);
$nc-cloud-snow-34: unicode(ec8a);
$nc-cloud-snow-42: unicode(ec8b);
$nc-cloud-sun-17: unicode(ec8c);
$nc-cloud-sun-19: unicode(ec8d);
$nc-cloud-upload: unicode(ec8e);
$nc-cloud: unicode(ec8f);
$nc-clover: unicode(ec90);
$nc-clubs-suit: unicode(ec91);
$nc-coat-hanger: unicode(ec92);
$nc-coat: unicode(ec93);
$nc-cockade: unicode(ec94);
$nc-cocktail: unicode(ec95);
$nc-code-editor: unicode(ec96);
$nc-code: unicode(ec97);
$nc-coffe-long: unicode(ec98);
$nc-coffee-bean: unicode(ec99);
$nc-coffee-long: unicode(ec9a);
$nc-coffee-maker: unicode(ec9b);
$nc-coffee: unicode(ec9c);
$nc-coffin: unicode(ec9d);
$nc-cogwheel: unicode(ec9e);
$nc-coins: unicode(ec9f);
$nc-collar: unicode(eca0);
$nc-collection: unicode(eca1);
$nc-color: unicode(eca2);
$nc-comb: unicode(eca3);
$nc-command: unicode(eca4);
$nc-comment-add: unicode(eca5);
$nc-comment: unicode(eca6);
$nc-comments: unicode(eca7);
$nc-compact-camera: unicode(eca8);
$nc-compare: unicode(eca9);
$nc-compass-04: unicode(ecaa);
$nc-compass-05: unicode(ecab);
$nc-compass-06: unicode(ecac);
$nc-compass-2: unicode(ecad);
$nc-compass-3: unicode(ecae);
$nc-compass: unicode(ecaf);
$nc-components: unicode(ecb0);
$nc-compressed-file: unicode(ecb1);
$nc-computer-monitor: unicode(ecb2);
$nc-computer-upload: unicode(ecb3);
$nc-computer: unicode(ecb4);
$nc-concierge: unicode(ecb5);
$nc-condom: unicode(ecb6);
$nc-cone: unicode(ecb7);
$nc-conference-room: unicode(ecb8);
$nc-configuration-tools: unicode(ecb9);
$nc-connect: unicode(ecba);
$nc-connection: unicode(ecbb);
$nc-construction-sign: unicode(ecbc);
$nc-contact-86: unicode(ecbf);
$nc-contact-87: unicode(ecbd);
$nc-contact-88: unicode(ecbe);
$nc-contact: unicode(ecc0);
$nc-contactless-card: unicode(ecc1);
$nc-contactless: unicode(ecc2);
$nc-contacts-2: unicode(ecc3);
$nc-contacts-44: unicode(ecc4);
$nc-contacts-45: unicode(ecc5);
$nc-contacts: unicode(ecc6);
$nc-content-360deg: unicode(ecc7);
$nc-content-delivery: unicode(ecc8);
$nc-contrast-2: unicode(ecc9);
$nc-contrast: unicode(ecca);
$nc-control-panel: unicode(eccb);
$nc-controller-2: unicode(eccc);
$nc-controller: unicode(eccd);
$nc-conversion: unicode(ecce);
$nc-cookies: unicode(ecd6);
$nc-copy-2: unicode(eccf);
$nc-copy: unicode(ecd0);
$nc-copyright: unicode(ecd1);
$nc-corn: unicode(ecd2);
$nc-corner-bottom-left: unicode(ecd3);
$nc-corner-bottom-right: unicode(ecd4);
$nc-corner-down-round: unicode(ecd5);
$nc-corner-left-down: unicode(ecd7);
$nc-corner-left-round: unicode(ecd8);
$nc-corner-right-down: unicode(ecd9);
$nc-corner-right-round: unicode(ecda);
$nc-corner-top-left: unicode(ecdb);
$nc-corner-top-right: unicode(ecdf);
$nc-corner-up-left: unicode(ecdc);
$nc-corner-up-right: unicode(ecdd);
$nc-corner-up-round: unicode(ecde);
$nc-cornucopia: unicode(ece0);
$nc-corset: unicode(ece1);
$nc-coughing: unicode(ece2);
$nc-countdown-2: unicode(ece3);
$nc-countdown: unicode(ece4);
$nc-couple-gay: unicode(ece5);
$nc-couple-lesbian: unicode(ece6);
$nc-coupon: unicode(ece7);
$nc-cow: unicode(ece8);
$nc-cpu: unicode(ece9);
$nc-crab: unicode(ecea);
$nc-cradle: unicode(eceb);
$nc-crane: unicode(ecec);
$nc-creative-commons: unicode(eced);
$nc-credit-card-in: unicode(ecee);
$nc-credit-card: unicode(ecef);
$nc-credit-locked: unicode(ecf0);
$nc-crepe: unicode(ecf1);
$nc-cricket-bat: unicode(ecf2);
$nc-croissant: unicode(ecf3);
$nc-crop: unicode(ecf4);
$nc-cross-down: unicode(ecf5);
$nc-cross-horizontal: unicode(ecf6);
$nc-cross-left: unicode(ecf7);
$nc-cross-right: unicode(ecf8);
$nc-cross-up: unicode(ecfb);
$nc-cross-vertical: unicode(ecf9);
$nc-cross: unicode(ecfa);
$nc-crosshair: unicode(ecfc);
$nc-crossing-directions: unicode(ecfd);
$nc-crossroad: unicode(ecfe);
$nc-croupier: unicode(ecff);
$nc-crown: unicode(ed00);
$nc-crumpet: unicode(ed01);
$nc-crunches: unicode(ed02);
$nc-cry-15: unicode(ed03);
$nc-cry-57: unicode(ed04);
$nc-crying-baby: unicode(ed05);
$nc-crypto-wallet: unicode(ed06);
$nc-cryptography: unicode(ed07);
$nc-css3: unicode(ed08);
$nc-ctrl-backward: unicode(ed09);
$nc-ctrl-down: unicode(ed0a);
$nc-ctrl-forward: unicode(ed0b);
$nc-ctrl-left: unicode(ed0c);
$nc-ctrl-right: unicode(ed0d);
$nc-ctrl-up: unicode(ed0e);
$nc-cubes-anim: unicode(ed0f);
$nc-cupcake: unicode(ed10);
$nc-cure: unicode(ed11);
$nc-curling-stone: unicode(ed12);
$nc-curling: unicode(ed13);
$nc-currency-dollar: unicode(ed14);
$nc-currency-euro: unicode(ed15);
$nc-currency-exchange-2: unicode(ed16);
$nc-currency-exchange: unicode(ed17);
$nc-currency-pound: unicode(ed18);
$nc-currency-yen: unicode(ed19);
$nc-cursor-48: unicode(ed1a);
$nc-cursor-49: unicode(ed1b);
$nc-cursor-add: unicode(ed24);
$nc-cursor-grab: unicode(ed1c);
$nc-cursor-load: unicode(ed1d);
$nc-cursor-menu: unicode(ed1e);
$nc-cursor-not-allowed: unicode(ed1f);
$nc-cursor-pointer: unicode(ed20);
$nc-cursor-text: unicode(ed21);
$nc-curtains: unicode(ed22);
$nc-curved-arrow-down: unicode(ed23);
$nc-curved-arrow-left: unicode(ed25);
$nc-curved-arrow-right: unicode(ed26);
$nc-curved-circuit: unicode(ed27);
$nc-customer-support: unicode(ed28);
$nc-cut: unicode(ed29);
$nc-cute: unicode(ed2a);
$nc-cutlery-75: unicode(ed2b);
$nc-cutlery-76: unicode(ed2c);
$nc-cutlery-77: unicode(ed2d);
$nc-cutlery: unicode(ed35);
$nc-cyborg: unicode(ed2e);
$nc-cycle: unicode(ed2f);
$nc-cycling-track: unicode(ed30);
$nc-cycling: unicode(ed31);
$nc-d-add: unicode(ed32);
$nc-d-chart: unicode(ed33);
$nc-d-check: unicode(ed34);
$nc-d-delete: unicode(ed36);
$nc-d-edit: unicode(ed37);
$nc-d-remove: unicode(ed38);
$nc-dancer: unicode(ed39);
$nc-dart: unicode(ed3a);
$nc-dashboard: unicode(ed3b);
$nc-data-download: unicode(ed3c);
$nc-data-settings: unicode(ed3d);
$nc-data-table: unicode(ed3e);
$nc-data-upload: unicode(ed3f);
$nc-database: unicode(ed40);
$nc-dead-hand: unicode(ed41);
$nc-deadlift: unicode(ed42);
$nc-deaf: unicode(ed43);
$nc-debt: unicode(ed44);
$nc-decentralize: unicode(ed45);
$nc-decision-process: unicode(ed46);
$nc-decoration: unicode(ed47);
$nc-decrease-font-size: unicode(ed48);
$nc-deer: unicode(ed49);
$nc-delete-28: unicode(ed4a);
$nc-delete-30: unicode(ed4b);
$nc-delete-forever: unicode(ed4c);
$nc-delete-key: unicode(ed4d);
$nc-delete-x: unicode(ed4e);
$nc-delete: unicode(ed4f);
$nc-delivery-2: unicode(ed54);
$nc-delivery-3: unicode(ed50);
$nc-delivery-fast: unicode(ed51);
$nc-delivery-time: unicode(ed52);
$nc-delivery-track: unicode(ed53);
$nc-delivery: unicode(ed55);
$nc-design-system: unicode(ed56);
$nc-design: unicode(ed57);
$nc-desk-drawer: unicode(ed58);
$nc-desk-lamp: unicode(ed59);
$nc-desk: unicode(ed5a);
$nc-detached-property: unicode(ed61);
$nc-detox: unicode(ed5b);
$nc-device-connection: unicode(ed5c);
$nc-devil: unicode(ed5d);
$nc-diamond: unicode(ed5e);
$nc-diamonds-suits: unicode(ed5f);
$nc-diaper-changing-area: unicode(ed60);
$nc-diaper: unicode(ed62);
$nc-dice-2: unicode(ed63);
$nc-dice: unicode(ed64);
$nc-diet-food: unicode(ed65);
$nc-diet-plan: unicode(ed66);
$nc-diet: unicode(ed67);
$nc-digital-key: unicode(ed68);
$nc-digital-piano: unicode(ed69);
$nc-direction-down: unicode(ed6a);
$nc-direction-left: unicode(ed6b);
$nc-direction-right: unicode(ed6c);
$nc-direction-up: unicode(ed6d);
$nc-direction: unicode(ed6e);
$nc-directions: unicode(ed6f);
$nc-discount-2: unicode(ed70);
$nc-disgusted: unicode(ed71);
$nc-dish: unicode(ed72);
$nc-dishwasher: unicode(ed73);
$nc-disinfectant: unicode(ed74);
$nc-disk-reader: unicode(ed75);
$nc-disk: unicode(ed76);
$nc-disperse: unicode(ed77);
$nc-distance: unicode(ed78);
$nc-distribute-horizontal: unicode(ed79);
$nc-distribute-vertical: unicode(ed7a);
$nc-divider: unicode(ed7b);
$nc-dizzy-face: unicode(ed7c);
$nc-dna-27: unicode(ed7d);
$nc-dna-38: unicode(ed7e);
$nc-doc-folder: unicode(ed7f);
$nc-dock-bottom: unicode(ed80);
$nc-dock-left: unicode(ed81);
$nc-dock-right: unicode(ed82);
$nc-dock-top: unicode(ed83);
$nc-doctor: unicode(ed84);
$nc-document-2: unicode(ed85);
$nc-document-copy: unicode(ed86);
$nc-document: unicode(ed87);
$nc-dog-house: unicode(ed88);
$nc-dog-leash: unicode(ed89);
$nc-dog: unicode(ed8a);
$nc-dont-touch-eyes: unicode(ed8b);
$nc-dont-touch-mouth: unicode(ed8c);
$nc-donut: unicode(ed8d);
$nc-door-2: unicode(ed8e);
$nc-door-3: unicode(ed92);
$nc-door-handle: unicode(ed8f);
$nc-door: unicode(ed90);
$nc-doorphone: unicode(ed91);
$nc-dots-anim-2: unicode(ed93);
$nc-dots-anim-3: unicode(ed94);
$nc-dots-anim-4: unicode(ed95);
$nc-dots-anim-5: unicode(ed96);
$nc-dots-anim-6: unicode(ed97);
$nc-dots-anim-7: unicode(ed98);
$nc-dots-anim: unicode(ed99);
$nc-dots: unicode(ed9a);
$nc-double-arrow-left: unicode(ed9b);
$nc-double-arrow-right: unicode(ed9c);
$nc-double-bed: unicode(ed9d);
$nc-double-tap: unicode(ed9e);
$nc-down-arrow: unicode(ed9f);
$nc-download-data: unicode(eda0);
$nc-download-file: unicode(eda1);
$nc-download: unicode(eda2);
$nc-drag-21: unicode(eda3);
$nc-drag-31: unicode(eda4);
$nc-drag-down: unicode(eda5);
$nc-drag-left: unicode(eda6);
$nc-drag-right: unicode(eda7);
$nc-drag-up: unicode(eda8);
$nc-drag: unicode(eda9);
$nc-drawer-2: unicode(edaa);
$nc-drawer: unicode(edab);
$nc-dress-man: unicode(edac);
$nc-dress-woman: unicode(edad);
$nc-dresser-2: unicode(edae);
$nc-dresser-3: unicode(edaf);
$nc-dresser: unicode(edb0);
$nc-drill: unicode(edb1);
$nc-drink-2: unicode(edb2);
$nc-drink-list: unicode(edb3);
$nc-drink: unicode(edb4);
$nc-drinking-bottle: unicode(edb5);
$nc-drone-2: unicode(edb6);
$nc-drone: unicode(edb7);
$nc-drop-15: unicode(edb8);
$nc-drop: unicode(edb9);
$nc-drops: unicode(edba);
$nc-druidism: unicode(edbb);
$nc-drums: unicode(edbc);
$nc-duck: unicode(edbd);
$nc-dumbbell: unicode(edbe);
$nc-duplicate: unicode(edbf);
$nc-e-add: unicode(edc0);
$nc-e-delete: unicode(edc1);
$nc-e-reader: unicode(edc2);
$nc-e-remove: unicode(edc3);
$nc-earbuds: unicode(edc4);
$nc-earth-science: unicode(edc5);
$nc-eclipse: unicode(edc6);
$nc-eco-home: unicode(edc7);
$nc-ecology: unicode(edc8);
$nc-edge-razor: unicode(edc9);
$nc-edit-2: unicode(edca);
$nc-edit-brightness: unicode(edcb);
$nc-edit-color: unicode(edcc);
$nc-edit-contrast: unicode(edcd);
$nc-edit-curves: unicode(edce);
$nc-edit-levels: unicode(edcf);
$nc-edit-note: unicode(edd0);
$nc-edit-saturation: unicode(edd1);
$nc-edit: unicode(edd2);
$nc-egg-38: unicode(edd3);
$nc-egg-39: unicode(edd4);
$nc-egg: unicode(edd5);
$nc-eggs: unicode(edd6);
$nc-eight: unicode(edd7);
$nc-eject: unicode(edd8);
$nc-electronic-circuit: unicode(edd9);
$nc-elephant: unicode(edda);
$nc-elliptical-cross-trainer: unicode(eddb);
$nc-email: unicode(eddc);
$nc-embryo: unicode(eddd);
$nc-empty: unicode(edde);
$nc-energy-drink: unicode(eddf);
$nc-energy-shaker: unicode(ede0);
$nc-energy-supplement: unicode(ede1);
$nc-energy: unicode(ede7);
$nc-engine-start: unicode(ede2);
$nc-engine: unicode(ede3);
$nc-enlarge-diagonal-2: unicode(ede4);
$nc-enlarge-diagonal: unicode(ede5);
$nc-enlarge-h: unicode(ede6);
$nc-enlarge-horizontal: unicode(ede8);
$nc-enlarge-vertical: unicode(ede9);
$nc-enlarge: unicode(edee);
$nc-enter: unicode(edea);
$nc-equestrian-helmet: unicode(edeb);
$nc-eraser-32: unicode(edec);
$nc-eraser-33: unicode(eded);
$nc-eraser-46: unicode(edef);
$nc-escalator: unicode(edf0);
$nc-event-confirm: unicode(edf1);
$nc-event-create: unicode(edf2);
$nc-event-ticket: unicode(edf3);
$nc-exchange: unicode(edf4);
$nc-exclamation-mark: unicode(edf5);
$nc-exercise-bike: unicode(edf6);
$nc-exhibition: unicode(edf7);
$nc-exit-right: unicode(edf8);
$nc-expand-h: unicode(edf9);
$nc-expand-window: unicode(edfa);
$nc-expand: unicode(edfb);
$nc-explore-2: unicode(edfc);
$nc-explore-user: unicode(edfd);
$nc-explore: unicode(edfe);
$nc-export: unicode(edff);
$nc-eye-recognition: unicode(ee00);
$nc-eye: unicode(ee01);
$nc-eyelash: unicode(ee02);
$nc-eyeliner: unicode(ee03);
$nc-eyeshadow: unicode(ee08);
$nc-ez-bar: unicode(ee04);
$nc-f-add: unicode(ee05);
$nc-f-chat: unicode(ee06);
$nc-f-check: unicode(ee07);
$nc-f-comment: unicode(ee09);
$nc-f-dashboard: unicode(ee0a);
$nc-f-delete: unicode(ee0b);
$nc-f-remove: unicode(ee0c);
$nc-face-man: unicode(ee0d);
$nc-face-powder: unicode(ee0e);
$nc-face-recognition: unicode(ee0f);
$nc-face-woman: unicode(ee10);
$nc-factory: unicode(ee16);
$nc-fahrenheit: unicode(ee11);
$nc-family-roof: unicode(ee13);
$nc-family: unicode(ee12);
$nc-fan: unicode(ee14);
$nc-fav-list: unicode(ee15);
$nc-fav-property: unicode(ee17);
$nc-fav-remove: unicode(ee18);
$nc-favorite: unicode(ee19);
$nc-feedback: unicode(ee1a);
$nc-feeding-bottle: unicode(ee1b);
$nc-female: unicode(ee1c);
$nc-fence: unicode(ee1d);
$nc-fencing-swords: unicode(ee1e);
$nc-fencing: unicode(ee1f);
$nc-file-2: unicode(ee20);
$nc-file-add: unicode(ee21);
$nc-file-alert: unicode(ee22);
$nc-file-archive: unicode(ee23);
$nc-file-article: unicode(ee24);
$nc-file-audio-2: unicode(ee25);
$nc-file-audio: unicode(ee2c);
$nc-file-bookmark: unicode(ee26);
$nc-file-chart-bar: unicode(ee27);
$nc-file-chart-pie: unicode(ee28);
$nc-file-check: unicode(ee29);
$nc-file-cloud: unicode(ee2a);
$nc-file-copies: unicode(ee2b);
$nc-file-copy: unicode(ee2d);
$nc-file-delete: unicode(ee2e);
$nc-file-dev: unicode(ee2f);
$nc-file-download-3: unicode(ee30);
$nc-file-download: unicode(ee31);
$nc-file-edit: unicode(ee32);
$nc-file-export: unicode(ee33);
$nc-file-favorite: unicode(ee34);
$nc-file-folder: unicode(ee35);
$nc-file-gallery: unicode(ee36);
$nc-file-history: unicode(ee37);
$nc-file-image: unicode(ee38);
$nc-file-import: unicode(ee39);
$nc-file-info: unicode(ee3a);
$nc-file-link: unicode(ee3b);
$nc-file-locked: unicode(ee3c);
$nc-file-money: unicode(ee3d);
$nc-file-new: unicode(ee3e);
$nc-file-no-access: unicode(ee3f);
$nc-file-play: unicode(ee40);
$nc-file-preferences: unicode(ee41);
$nc-file-question: unicode(ee42);
$nc-file-remove: unicode(ee43);
$nc-file-replace: unicode(ee44);
$nc-file-search: unicode(ee45);
$nc-file-settings: unicode(ee46);
$nc-file-shared: unicode(ee47);
$nc-file-starred: unicode(ee48);
$nc-file-sync: unicode(ee49);
$nc-file-text: unicode(ee4a);
$nc-file-upload-2: unicode(ee4b);
$nc-file-upload-3: unicode(ee4c);
$nc-file-upload: unicode(ee4d);
$nc-file-user: unicode(ee4e);
$nc-file-vector: unicode(ee4f);
$nc-file: unicode(ee50);
$nc-film: unicode(ee51);
$nc-filter-check: unicode(ee52);
$nc-filter-organization: unicode(ee53);
$nc-filter-remove: unicode(ee54);
$nc-filter-tool: unicode(ee55);
$nc-filter: unicode(ee56);
$nc-final-score: unicode(ee57);
$nc-find-baggage: unicode(ee58);
$nc-find-replace: unicode(ee59);
$nc-finger-snap: unicode(ee5a);
$nc-fire: unicode(ee60);
$nc-firearm: unicode(ee5b);
$nc-fireplace: unicode(ee5c);
$nc-firewall: unicode(ee5d);
$nc-fireworks: unicode(ee5e);
$nc-fish: unicode(ee5f);
$nc-fishbone: unicode(ee61);
$nc-fist: unicode(ee62);
$nc-fit-horizontal: unicode(ee63);
$nc-fit-vertical: unicode(ee68);
$nc-five: unicode(ee64);
$nc-flag-complex: unicode(ee65);
$nc-flag-diagonal-33: unicode(ee66);
$nc-flag-diagonal-34: unicode(ee67);
$nc-flag-points-31: unicode(ee69);
$nc-flag-points-32: unicode(ee6a);
$nc-flag-simple: unicode(ee70);
$nc-flag: unicode(ee6b);
$nc-flame: unicode(ee6c);
$nc-flash-off-2: unicode(ee6d);
$nc-flash-off: unicode(ee6e);
$nc-flashlight: unicode(ee6f);
$nc-flask-2: unicode(ee71);
$nc-flask: unicode(ee72);
$nc-flick-down: unicode(ee73);
$nc-flick-left: unicode(ee74);
$nc-flick-right: unicode(ee75);
$nc-flick-up: unicode(ee76);
$nc-flight-connection: unicode(ee77);
$nc-flight: unicode(ee78);
$nc-flip-horizontal: unicode(ee7d);
$nc-flip-up: unicode(ee79);
$nc-flip-vertical: unicode(ee7a);
$nc-flip: unicode(ee7b);
$nc-floor-lamp: unicode(ee7c);
$nc-floor: unicode(ee7e);
$nc-floors: unicode(ee7f);
$nc-floppy-disk: unicode(ee80);
$nc-flower-05: unicode(ee81);
$nc-flower-06: unicode(ee82);
$nc-flower-07: unicode(ee83);
$nc-flower-rose: unicode(ee84);
$nc-focus: unicode(ee85);
$nc-fog: unicode(ee86);
$nc-folder-2: unicode(ee87);
$nc-folder-3: unicode(ee88);
$nc-folder-add: unicode(ee89);
$nc-folder-alert: unicode(ee8a);
$nc-folder-audio: unicode(ee8b);
$nc-folder-bookmark: unicode(ee8c);
$nc-folder-chart-bar: unicode(ee8d);
$nc-folder-chart-pie: unicode(ee8e);
$nc-folder-check: unicode(ee8f);
$nc-folder-cloud: unicode(ee90);
$nc-folder-dev: unicode(ee91);
$nc-folder-download: unicode(ee92);
$nc-folder-edit: unicode(ee93);
$nc-folder-favorite: unicode(ee94);
$nc-folder-gallery: unicode(ee95);
$nc-folder-history: unicode(ee96);
$nc-folder-image: unicode(ee97);
$nc-folder-info: unicode(ee98);
$nc-folder-link: unicode(ee99);
$nc-folder-locked: unicode(ee9a);
$nc-folder-money: unicode(ee9b);
$nc-folder-music: unicode(ee9c);
$nc-folder-no-access: unicode(ee9d);
$nc-folder-play: unicode(ee9e);
$nc-folder-preferences: unicode(ee9f);
$nc-folder-question: unicode(eea0);
$nc-folder-remove: unicode(eea1);
$nc-folder-replace: unicode(eea2);
$nc-folder-search: unicode(eea3);
$nc-folder-settings: unicode(eea4);
$nc-folder-shared: unicode(eea5);
$nc-folder-starred: unicode(eea6);
$nc-folder-sync: unicode(eea7);
$nc-folder-upload: unicode(eea8);
$nc-folder-user: unicode(eea9);
$nc-folder-vector: unicode(eeaa);
$nc-folder: unicode(eeab);
$nc-food-course: unicode(eeac);
$nc-food-dog: unicode(eead);
$nc-food-scale: unicode(eeae);
$nc-food-supplement: unicode(eeaf);
$nc-football-headguard: unicode(eeb0);
$nc-forecast: unicode(eeb1);
$nc-forest: unicode(eeb2);
$nc-fork-2: unicode(eeb3);
$nc-fork: unicode(eeb4);
$nc-form: unicode(eeb5);
$nc-format-left: unicode(eeb6);
$nc-format-right: unicode(eeb7);
$nc-forward: unicode(eebe);
$nc-four: unicode(eeb8);
$nc-frame-effect: unicode(eeb9);
$nc-frame: unicode(eeba);
$nc-frankenstein: unicode(eebb);
$nc-fridge: unicode(eebc);
$nc-fuel-2: unicode(eebd);
$nc-fuel-electric: unicode(eebf);
$nc-fuel: unicode(eec0);
$nc-full-screen: unicode(eec1);
$nc-fullscreen-2: unicode(eec2);
$nc-fullscreen: unicode(eec3);
$nc-fullsize: unicode(eec4);
$nc-funnel: unicode(eec5);
$nc-furnished-property: unicode(eec6);
$nc-g-chart: unicode(eec7);
$nc-g-check: unicode(eec8);
$nc-gallery-layout: unicode(eec9);
$nc-gallery-view: unicode(eeca);
$nc-gaming-console: unicode(eecb);
$nc-gaming-controller: unicode(eecc);
$nc-gantt: unicode(eecd);
$nc-garlic: unicode(eece);
$nc-gas-mask: unicode(eecf);
$nc-gathering-restrictions: unicode(eed0);
$nc-gear: unicode(eed1);
$nc-geometry: unicode(eed2);
$nc-ghost-2: unicode(eed3);
$nc-ghost: unicode(eed4);
$nc-gift-exchange: unicode(eed5);
$nc-gift: unicode(eed6);
$nc-git-commit: unicode(eed7);
$nc-git-merge: unicode(eed8);
$nc-glass-water: unicode(eed9);
$nc-glass: unicode(eeda);
$nc-glasses-2: unicode(eedb);
$nc-glasses: unicode(eedc);
$nc-globe-2: unicode(eedd);
$nc-globe: unicode(eede);
$nc-glove: unicode(eedf);
$nc-gloves: unicode(eee0);
$nc-goal-65: unicode(eee1);
$nc-gold-coin: unicode(eee2);
$nc-gold: unicode(eee3);
$nc-golf-ball: unicode(eee8);
$nc-golf-club: unicode(eee4);
$nc-golf-course: unicode(eee5);
$nc-golf-player: unicode(eee6);
$nc-golf-strike: unicode(eee7);
$nc-gps: unicode(eee9);
$nc-grab: unicode(eeea);
$nc-gradient: unicode(eeeb);
$nc-grain-effect: unicode(eeec);
$nc-grain: unicode(eeed);
$nc-grammar-check: unicode(eef2);
$nc-grandparent: unicode(eeee);
$nc-grape: unicode(eeef);
$nc-graphics-tablet: unicode(eef0);
$nc-grave: unicode(eef1);
$nc-grenade: unicode(eef3);
$nc-grid-interface: unicode(eef4);
$nc-grid-layout: unicode(eef5);
$nc-grid-system: unicode(eef6);
$nc-grid-view: unicode(eef7);
$nc-grid: unicode(eef8);
$nc-groom: unicode(eef9);
$nc-group: unicode(eefa);
$nc-guitar: unicode(eefb);
$nc-gym-class: unicode(eefc);
$nc-gym-shoes: unicode(eefd);
$nc-gym: unicode(eefe);
$nc-gymnastics: unicode(eeff);
$nc-hacker: unicode(ef00);
$nc-hair-clipper: unicode(ef01);
$nc-hair-dryer: unicode(ef02);
$nc-hair-gel: unicode(ef03);
$nc-hair-man: unicode(ef04);
$nc-hair-straightener: unicode(ef05);
$nc-hair-towel: unicode(ef06);
$nc-hair-woman: unicode(ef07);
$nc-hairdresser: unicode(ef08);
$nc-halloween-pumpkin: unicode(ef0b);
$nc-hammer: unicode(ef09);
$nc-hand-card: unicode(ef0a);
$nc-hand-heart: unicode(ef0c);
$nc-hand-mixer: unicode(ef0d);
$nc-handball: unicode(ef0e);
$nc-handheld-console: unicode(ef0f);
$nc-handout: unicode(ef10);
$nc-hands-heart: unicode(ef11);
$nc-handshake: unicode(ef12);
$nc-hanging-toys: unicode(ef13);
$nc-happy-baby: unicode(ef14);
$nc-happy-sun: unicode(ef15);
$nc-hash-mark: unicode(ef16);
$nc-hat-2: unicode(ef17);
$nc-hat-3: unicode(ef18);
$nc-hat-top: unicode(ef19);
$nc-hat: unicode(ef1a);
$nc-hazelnut: unicode(ef1b);
$nc-hdmi: unicode(ef1c);
$nc-heading-1: unicode(ef1d);
$nc-heading-2: unicode(ef1e);
$nc-heading-3: unicode(ef1f);
$nc-heading-4: unicode(ef20);
$nc-heading-5: unicode(ef21);
$nc-heading-6: unicode(ef22);
$nc-headphones-2: unicode(ef23);
$nc-headphones-3: unicode(ef24);
$nc-headphones-mic: unicode(ef25);
$nc-headphones: unicode(ef26);
$nc-headset: unicode(ef27);
$nc-heart-anim: unicode(ef28);
$nc-heart-balloons: unicode(ef29);
$nc-heart-lock: unicode(ef2a);
$nc-heart: unicode(ef2b);
$nc-heartbeat: unicode(ef2c);
$nc-hearts-suit: unicode(ef2d);
$nc-heater: unicode(ef2e);
$nc-height: unicode(ef2f);
$nc-helicopter: unicode(ef30);
$nc-helmet: unicode(ef31);
$nc-hide: unicode(ef32);
$nc-hierarchy-53: unicode(ef33);
$nc-hierarchy-54: unicode(ef34);
$nc-hierarchy-55: unicode(ef35);
$nc-hierarchy-56: unicode(ef36);
$nc-high-priority: unicode(ef37);
$nc-hinduism: unicode(ef38);
$nc-hob: unicode(ef39);
$nc-hockey-stick: unicode(ef3a);
$nc-hockey: unicode(ef3b);
$nc-hold: unicode(ef3c);
$nc-home-2: unicode(ef3d);
$nc-home-3: unicode(ef3e);
$nc-home-search: unicode(ef3f);
$nc-home: unicode(ef40);
$nc-honey: unicode(ef41);
$nc-honeymoon: unicode(ef42);
$nc-hoodie: unicode(ef43);
$nc-hook: unicode(ef44);
$nc-horse-2: unicode(ef45);
$nc-horse-hopper: unicode(ef46);
$nc-horse: unicode(ef47);
$nc-horseshoe: unicode(ef48);
$nc-hospital-32: unicode(ef49);
$nc-hospital-33: unicode(ef4a);
$nc-hospital-34: unicode(ef4b);
$nc-hospital-bed: unicode(ef4c);
$nc-hot-dog: unicode(ef4d);
$nc-hot-key: unicode(ef4e);
$nc-hotel-bell: unicode(ef4f);
$nc-hotel-symbol: unicode(ef50);
$nc-hotel: unicode(ef51);
$nc-hotspot: unicode(ef52);
$nc-hourglass: unicode(ef53);
$nc-house-pricing: unicode(ef54);
$nc-house-property: unicode(ef55);
$nc-house-search-engine: unicode(ef56);
$nc-house: unicode(ef57);
$nc-html5: unicode(ef58);
$nc-humidity-26: unicode(ef59);
$nc-humidity-52: unicode(ef5a);
$nc-hurricane-44: unicode(ef5b);
$nc-hurricane-45: unicode(ef5c);
$nc-hut: unicode(ef5d);
$nc-hybrid-car: unicode(ef5e);
$nc-hyperlink: unicode(ef5f);
$nc-i-add: unicode(ef60);
$nc-i-check: unicode(ef61);
$nc-i-delete: unicode(ef62);
$nc-i-edit: unicode(ef63);
$nc-i-remove: unicode(ef64);
$nc-ice-cream-22: unicode(ef65);
$nc-ice-cream-72: unicode(ef66);
$nc-ice-cream: unicode(ef67);
$nc-ice-skates: unicode(ef68);
$nc-igloo: unicode(ef69);
$nc-image-2: unicode(ef6a);
$nc-image-add: unicode(ef6b);
$nc-image-delete: unicode(ef6c);
$nc-image-location: unicode(ef6d);
$nc-image: unicode(ef6e);
$nc-img-rotate-left: unicode(ef6f);
$nc-img-rotate-right: unicode(ef78);
$nc-img-stack: unicode(ef70);
$nc-img: unicode(ef71);
$nc-incense: unicode(ef72);
$nc-incognito: unicode(ef73);
$nc-increase-font-size: unicode(ef74);
$nc-increase: unicode(ef75);
$nc-infinite-loop: unicode(ef76);
$nc-infinite: unicode(ef77);
$nc-info-point: unicode(ef79);
$nc-info: unicode(ef7a);
$nc-infrared-thermometer: unicode(ef7b);
$nc-input: unicode(ef7c);
$nc-instant-camera-2: unicode(ef7d);
$nc-instant-camera: unicode(ef7e);
$nc-interview: unicode(ef88);
$nc-intestine: unicode(ef7f);
$nc-invert-direction: unicode(ef80);
$nc-invert-process: unicode(ef81);
$nc-iron-2: unicode(ef82);
$nc-iron-dont: unicode(ef83);
$nc-iron: unicode(ef84);
$nc-islam: unicode(ef85);
$nc-istanbul: unicode(ef86);
$nc-italic: unicode(ef87);
$nc-jacuzzi: unicode(ef89);
$nc-jam: unicode(ef8a);
$nc-jeans-41: unicode(ef8b);
$nc-jeans-43: unicode(ef8c);
$nc-jeans-pocket: unicode(ef8d);
$nc-jelly: unicode(ef8e);
$nc-jellyfish: unicode(ef8f);
$nc-jewel: unicode(ef90);
$nc-joint-account: unicode(ef91);
$nc-journey-06: unicode(ef92);
$nc-journey-07: unicode(ef93);
$nc-journey-08: unicode(ef94);
$nc-journey: unicode(ef95);
$nc-js-console: unicode(ef96);
$nc-json-logo: unicode(ef97);
$nc-judaism: unicode(ef98);
$nc-juice: unicode(ef99);
$nc-jump-rope: unicode(ef9a);
$nc-karate: unicode(ef9b);
$nc-ketchup: unicode(ef9c);
$nc-kettle: unicode(ef9d);
$nc-kettlebell: unicode(ef9e);
$nc-key: unicode(ef9f);
$nc-keyboard-hide: unicode(efa0);
$nc-keyboard-mouse: unicode(efa1);
$nc-keyboard-wired: unicode(efa2);
$nc-keyboard-wireless: unicode(efa3);
$nc-keyboard: unicode(efa4);
$nc-kid-2: unicode(efa5);
$nc-kid: unicode(efa6);
$nc-kiss: unicode(efa7);
$nc-kitchen-fan: unicode(efa8);
$nc-kiwi: unicode(efa9);
$nc-knife: unicode(efaa);
$nc-knob: unicode(efab);
$nc-l-add: unicode(efb2);
$nc-l-check: unicode(efac);
$nc-l-circle: unicode(efad);
$nc-l-circles: unicode(efae);
$nc-l-location: unicode(efb6);
$nc-l-remove: unicode(efaf);
$nc-l-search: unicode(efb0);
$nc-l-security: unicode(efb1);
$nc-l-settings: unicode(efb3);
$nc-l-sync: unicode(efb4);
$nc-l-system-update: unicode(efb5);
$nc-label: unicode(efb7);
$nc-ladybug: unicode(efb8);
$nc-lamp-3: unicode(efb9);
$nc-land: unicode(efba);
$nc-landing: unicode(efbb);
$nc-landscape-orientation: unicode(efbc);
$nc-language: unicode(efbd);
$nc-laptop-1: unicode(efbe);
$nc-laptop-2: unicode(efbf);
$nc-laptop-71: unicode(efc0);
$nc-laptop-72: unicode(efc1);
$nc-laptop: unicode(efc2);
$nc-lat-station: unicode(efc3);
$nc-laugh-17: unicode(efc4);
$nc-laugh-35: unicode(efc5);
$nc-launch-app: unicode(efc6);
$nc-launch: unicode(efc7);
$nc-laundry: unicode(efc8);
$nc-law: unicode(efc9);
$nc-layers-2: unicode(efca);
$nc-layers: unicode(efcb);
$nc-layout-11: unicode(efcc);
$nc-layout-25: unicode(efcd);
$nc-layout-grid: unicode(efce);
$nc-layout: unicode(efcf);
$nc-leaf-36: unicode(efd0);
$nc-leaf-38: unicode(efd3);
$nc-leaf: unicode(efd2);
$nc-leave: unicode(efd1);
$nc-left-arrow: unicode(efd4);
$nc-leggins: unicode(efd5);
$nc-lemon-slice: unicode(efd6);
$nc-lemon: unicode(efd7);
$nc-letter-a: unicode(efd8);
$nc-letter-b: unicode(efd9);
$nc-letter-c: unicode(efda);
$nc-letter-d: unicode(efdb);
$nc-letter-e: unicode(efdc);
$nc-letter-f: unicode(efdd);
$nc-letter-g: unicode(efde);
$nc-letter-h: unicode(efdf);
$nc-letter-i: unicode(efe0);
$nc-letter-j: unicode(efe1);
$nc-letter-k: unicode(efe2);
$nc-letter-l: unicode(efe3);
$nc-letter-m: unicode(efe4);
$nc-letter-n: unicode(efe5);
$nc-letter-o: unicode(efe6);
$nc-letter-p: unicode(efe7);
$nc-letter-q: unicode(efe8);
$nc-letter-r: unicode(efe9);
$nc-letter-s: unicode(efea);
$nc-letter-t: unicode(efeb);
$nc-letter-u: unicode(efec);
$nc-letter-v: unicode(efed);
$nc-letter-w: unicode(efee);
$nc-letter-x: unicode(efef);
$nc-letter-y: unicode(eff0);
$nc-letter-z: unicode(eff1);
$nc-letter: unicode(eff2);
$nc-library: unicode(eff3);
$nc-license-key: unicode(eff4);
$nc-lifering: unicode(eff6);
$nc-lift: unicode(eff5);
$nc-light-2: unicode(eff7);
$nc-light-control: unicode(eff8);
$nc-light-switch: unicode(eff9);
$nc-light-traffic: unicode(effa);
$nc-lighter: unicode(effb);
$nc-lighthouse: unicode(effc);
$nc-lightning: unicode(f003);
$nc-like: unicode(effd);
$nc-line-chart: unicode(effe);
$nc-line-height: unicode(efff);
$nc-link-broken: unicode(f000);
$nc-link: unicode(f001);
$nc-lip-gloss: unicode(f002);
$nc-lips: unicode(f006);
$nc-lipstick-2: unicode(f004);
$nc-lipstick: unicode(f005);
$nc-liquid-soap-container: unicode(f007);
$nc-list-bullet: unicode(f008);
$nc-list-numbers: unicode(f009);
$nc-list: unicode(f00a);
$nc-live-streaming: unicode(f00b);
$nc-loader-bars: unicode(f00c);
$nc-loan: unicode(f00d);
$nc-lobster: unicode(f00e);
$nc-lock-landscape: unicode(f00f);
$nc-lock-orientation: unicode(f010);
$nc-lock-portrait: unicode(f011);
$nc-lock: unicode(f012);
$nc-log-in: unicode(f013);
$nc-log-out: unicode(f014);
$nc-logic: unicode(f015);
$nc-logout: unicode(f016);
$nc-lollipop: unicode(f017);
$nc-london: unicode(f018);
$nc-long-sleeve: unicode(f019);
$nc-loop-2: unicode(f01a);
$nc-loop: unicode(f01b);
$nc-lotus-flower: unicode(f01c);
$nc-loudspeaker: unicode(f01d);
$nc-love-camera: unicode(f01e);
$nc-love-car: unicode(f01f);
$nc-love-card: unicode(f020);
$nc-love-heart-pin: unicode(f021);
$nc-love-letter: unicode(f022);
$nc-love-message: unicode(f023);
$nc-love-movie: unicode(f024);
$nc-love-song: unicode(f025);
$nc-love: unicode(f026);
$nc-low-priority: unicode(f027);
$nc-low-vision: unicode(f02b);
$nc-lucky-seven: unicode(f02d);
$nc-luggage: unicode(f028);
$nc-lungs-infection: unicode(f034);
$nc-lungs: unicode(f029);
$nc-m-add: unicode(f030);
$nc-m-check: unicode(f02a);
$nc-m-delete: unicode(f02c);
$nc-m-edit: unicode(f02e);
$nc-m-heart: unicode(f02f);
$nc-m-location: unicode(f031);
$nc-m-remove: unicode(f032);
$nc-m-search: unicode(f033);
$nc-m-security: unicode(f035);
$nc-m-settings: unicode(f036);
$nc-m-share: unicode(f037);
$nc-m-star: unicode(f038);
$nc-m-sync: unicode(f039);
$nc-m-time: unicode(f03a);
$nc-m-update: unicode(f03b);
$nc-machine-learning: unicode(f03c);
$nc-macro: unicode(f03d);
$nc-mad-12: unicode(f03e);
$nc-mad-58: unicode(f03f);
$nc-magnet: unicode(f040);
$nc-magnifier-zoom-in: unicode(f041);
$nc-magnifier-zoom-out: unicode(f042);
$nc-magnifier: unicode(f043);
$nc-mail: unicode(f044);
$nc-makeup-blush: unicode(f045);
$nc-makeup-brush: unicode(f046);
$nc-makeup-cream: unicode(f047);
$nc-makeup-foundation: unicode(f048);
$nc-makeup-mirror: unicode(f049);
$nc-makeup-palette: unicode(f04a);
$nc-makeup: unicode(f04b);
$nc-male: unicode(f04c);
$nc-malicious: unicode(f04d);
$nc-man-20: unicode(f04e);
$nc-man-23: unicode(f04f);
$nc-man-down: unicode(f050);
$nc-man-glasses: unicode(f051);
$nc-man-up-front: unicode(f053);
$nc-man-up: unicode(f052);
$nc-manga-62: unicode(f054);
$nc-manga-63: unicode(f055);
$nc-map-big: unicode(f056);
$nc-map-compass: unicode(f057);
$nc-map-gps: unicode(f058);
$nc-map-marker: unicode(f059);
$nc-map-pin: unicode(f05a);
$nc-map: unicode(f05b);
$nc-maple-leaf: unicode(f05c);
$nc-margin-left: unicode(f05d);
$nc-margin-right: unicode(f05e);
$nc-mario-mushroom: unicode(f05f);
$nc-markdown: unicode(f060);
$nc-marker-2: unicode(f061);
$nc-marker-3: unicode(f062);
$nc-marker: unicode(f063);
$nc-market-music: unicode(f064);
$nc-market-play: unicode(f065);
$nc-mascara: unicode(f066);
$nc-mask-face: unicode(f067);
$nc-mask-oval: unicode(f068);
$nc-mask-rect: unicode(f069);
$nc-massage: unicode(f06a);
$nc-mat: unicode(f06b);
$nc-matches: unicode(f06c);
$nc-math: unicode(f06d);
$nc-maximize-area: unicode(f06e);
$nc-maximize: unicode(f06f);
$nc-mayo: unicode(f070);
$nc-measure-02: unicode(f071);
$nc-measure-17: unicode(f072);
$nc-measure-big: unicode(f073);
$nc-measurement: unicode(f074);
$nc-measuring-cup: unicode(f075);
$nc-meat-spit: unicode(f076);
$nc-medal: unicode(f077);
$nc-media-player: unicode(f078);
$nc-media-stream: unicode(f079);
$nc-medical-clipboard: unicode(f07a);
$nc-medical-mask: unicode(f07b);
$nc-medication: unicode(f07c);
$nc-medicine-ball: unicode(f07d);
$nc-medicine: unicode(f07e);
$nc-meeting: unicode(f07f);
$nc-megaphone: unicode(f080);
$nc-menu-2: unicode(f081);
$nc-menu-3: unicode(f082);
$nc-menu-4: unicode(f083);
$nc-menu-5: unicode(f084);
$nc-menu-6: unicode(f085);
$nc-menu-7: unicode(f086);
$nc-menu-8: unicode(f087);
$nc-menu: unicode(f088);
$nc-merge-2: unicode(f089);
$nc-merge: unicode(f08a);
$nc-messaging: unicode(f08b);
$nc-metrics: unicode(f08c);
$nc-mic-2: unicode(f08d);
$nc-mic: unicode(f08e);
$nc-mickey-mouse: unicode(f08f);
$nc-microbiology: unicode(f090);
$nc-microphone-2: unicode(f091);
$nc-microphone-off: unicode(f092);
$nc-microphone: unicode(f093);
$nc-microscope: unicode(f094);
$nc-microsoft: unicode(f095);
$nc-microwave: unicode(f096);
$nc-migration: unicode(f097);
$nc-military-camp: unicode(f098);
$nc-military-knife: unicode(f099);
$nc-military-medal: unicode(f09a);
$nc-military-tag: unicode(f09b);
$nc-military-tank: unicode(f09c);
$nc-military-vest: unicode(f09d);
$nc-milk: unicode(f09e);
$nc-miner: unicode(f09f);
$nc-mirror-2: unicode(f0a0);
$nc-mirror-display: unicode(f0a1);
$nc-mirror-tablet-phone: unicode(f0a6);
$nc-mirror: unicode(f0a2);
$nc-missile: unicode(f0a3);
$nc-mistletoe: unicode(f0a4);
$nc-mobile-banking: unicode(f0a5);
$nc-mobile-card: unicode(f0a7);
$nc-mobile-chat: unicode(f0a8);
$nc-mobile-contact: unicode(f0a9);
$nc-mobile-design: unicode(f0aa);
$nc-mobile-dev: unicode(f0ab);
$nc-mobile-phone: unicode(f0ac);
$nc-moka: unicode(f0ad);
$nc-molecule-39: unicode(f0ae);
$nc-molecule-40: unicode(f0af);
$nc-molecule: unicode(f0b0);
$nc-money-11: unicode(f0b1);
$nc-money-12: unicode(f0b2);
$nc-money-13: unicode(f0b3);
$nc-money-bag: unicode(f0b4);
$nc-money-coins: unicode(f0b5);
$nc-money-growth: unicode(f0b6);
$nc-money-time: unicode(f0b7);
$nc-money-transfer: unicode(f0b8);
$nc-monster: unicode(f0b9);
$nc-moon-cloud-drop: unicode(f0ba);
$nc-moon-cloud-fog: unicode(f0bb);
$nc-moon-cloud-hail: unicode(f0bc);
$nc-moon-cloud-light: unicode(f0bd);
$nc-moon-cloud-rain: unicode(f0be);
$nc-moon-cloud-snow-61: unicode(f0bf);
$nc-moon-cloud-snow-62: unicode(f0c0);
$nc-moon-fog: unicode(f0c1);
$nc-moon-full: unicode(f0c2);
$nc-moon-stars: unicode(f0c3);
$nc-moon: unicode(f0c4);
$nc-mortar: unicode(f0c5);
$nc-mortgage: unicode(f0c6);
$nc-mosque: unicode(f0c7);
$nc-moto: unicode(f0c8);
$nc-mountain: unicode(f0c9);
$nc-mouse-2: unicode(f0ca);
$nc-mouse-anim: unicode(f0cb);
$nc-mouse: unicode(f0cc);
$nc-move-2: unicode(f0cd);
$nc-move-3: unicode(f0ce);
$nc-move-down-2: unicode(f0cf);
$nc-move-down-right: unicode(f0d0);
$nc-move-down: unicode(f0d1);
$nc-move-layer-down: unicode(f0d2);
$nc-move-layer-left: unicode(f0d3);
$nc-move-layer-right: unicode(f0d4);
$nc-move-layer-up: unicode(f0d5);
$nc-move-left: unicode(f0d6);
$nc-move-right: unicode(f0d7);
$nc-move-up-2: unicode(f0d8);
$nc-move-up-left: unicode(f0d9);
$nc-move-up: unicode(f0da);
$nc-move: unicode(f0db);
$nc-movie-2: unicode(f0dc);
$nc-movie-3: unicode(f0dd);
$nc-movie-reel: unicode(f0de);
$nc-movie: unicode(f0df);
$nc-mower: unicode(f0e0);
$nc-muffin: unicode(f0e1);
$nc-mug: unicode(f0e2);
$nc-multiple-11: unicode(f0e3);
$nc-multiple-19: unicode(f0e4);
$nc-multiple: unicode(f0e5);
$nc-mushroom: unicode(f0e6);
$nc-music-album: unicode(f0e7);
$nc-music-cloud: unicode(f0e8);
$nc-music-note: unicode(f0e9);
$nc-music-player: unicode(f0ea);
$nc-music-playlist: unicode(f0eb);
$nc-music: unicode(f0ec);
$nc-mustache: unicode(f0ed);
$nc-n-check: unicode(f0ee);
$nc-n-edit: unicode(f0ef);
$nc-nail-file: unicode(f0f0);
$nc-nail-polish-2: unicode(f0f1);
$nc-nail-polish: unicode(f0f2);
$nc-name-card: unicode(f0f3);
$nc-nav-down: unicode(f0f4);
$nc-nav-left: unicode(f0f5);
$nc-nav-right: unicode(f0f6);
$nc-nav-up: unicode(f0f7);
$nc-navigation: unicode(f0f8);
$nc-neck-duster: unicode(f0f9);
$nc-needle: unicode(f0fa);
$nc-negative-judgement: unicode(f0fb);
$nc-nerd: unicode(f0fc);
$nc-net: unicode(f0fd);
$nc-network-communication: unicode(f0fe);
$nc-network-connection: unicode(f0ff);
$nc-network: unicode(f100);
$nc-networking: unicode(f101);
$nc-new-construction: unicode(f102);
$nc-new-notification: unicode(f103);
$nc-new: unicode(f104);
$nc-news: unicode(f10a);
$nc-newsletter-dev: unicode(f105);
$nc-newsletter: unicode(f106);
$nc-night-table: unicode(f107);
$nc-night: unicode(f108);
$nc-nine: unicode(f109);
$nc-ninja: unicode(f10b);
$nc-no-contact: unicode(f10c);
$nc-no-guns: unicode(f10d);
$nc-no-photo: unicode(f10e);
$nc-no-results: unicode(f10f);
$nc-no-smoking: unicode(f110);
$nc-no-words: unicode(f111);
$nc-nodes: unicode(f112);
$nc-noodles: unicode(f113);
$nc-note-code: unicode(f114);
$nc-note: unicode(f115);
$nc-notebook: unicode(f116);
$nc-notepad: unicode(f117);
$nc-notes: unicode(f118);
$nc-notification-2: unicode(f119);
$nc-notification: unicode(f11a);
$nc-nurse: unicode(f11b);
$nc-nutrition: unicode(f11c);
$nc-ny: unicode(f11d);
$nc-o-check: unicode(f11e);
$nc-o-warning: unicode(f11f);
$nc-octagon-m: unicode(f120);
$nc-octagon: unicode(f121);
$nc-octopus: unicode(f122);
$nc-office-chair: unicode(f123);
$nc-office: unicode(f124);
$nc-offline: unicode(f125);
$nc-oil-2: unicode(f126);
$nc-oil: unicode(f127);
$nc-olympic-flame: unicode(f128);
$nc-one: unicode(f129);
$nc-onion: unicode(f12a);
$nc-online-banking: unicode(f12b);
$nc-open-book: unicode(f12c);
$nc-open-folder: unicode(f12d);
$nc-open-in-browser: unicode(f12e);
$nc-opening-times: unicode(f12f);
$nc-opposite-directions-2: unicode(f130);
$nc-opposite-directions: unicode(f131);
$nc-options: unicode(f132);
$nc-orange: unicode(f133);
$nc-organic-2: unicode(f134);
$nc-organic: unicode(f135);
$nc-orientation: unicode(f136);
$nc-oven: unicode(f137);
$nc-ovum-sperm: unicode(f138);
$nc-owl: unicode(f139);
$nc-p-add: unicode(f13a);
$nc-p-chart: unicode(f13b);
$nc-p-check: unicode(f13c);
$nc-p-edit: unicode(f13d);
$nc-p-heart: unicode(f13e);
$nc-p-location: unicode(f13f);
$nc-p-remove: unicode(f144);
$nc-p-search: unicode(f140);
$nc-p-settings: unicode(f141);
$nc-p-share: unicode(f142);
$nc-p-sync: unicode(f143);
$nc-p-system-update: unicode(f148);
$nc-p-time: unicode(f145);
$nc-pacifier: unicode(f146);
$nc-pacman: unicode(f14d);
$nc-padlock-unlocked: unicode(f147);
$nc-padlock: unicode(f149);
$nc-paint-16: unicode(f14a);
$nc-paint-37: unicode(f14b);
$nc-paint-38: unicode(f14c);
$nc-paint-brush: unicode(f14e);
$nc-paint-bucket-39: unicode(f14f);
$nc-paint-bucket-40: unicode(f150);
$nc-pajamas: unicode(f151);
$nc-palette: unicode(f152);
$nc-palm-tree: unicode(f153);
$nc-pan: unicode(f157);
$nc-pancake: unicode(f154);
$nc-panda: unicode(f155);
$nc-panel: unicode(f156);
$nc-pantone: unicode(f158);
$nc-paper-design: unicode(f159);
$nc-paper-dev: unicode(f15a);
$nc-paper-diploma: unicode(f15b);
$nc-paper: unicode(f15c);
$nc-parachute: unicode(f15d);
$nc-paragraph-2: unicode(f15e);
$nc-paragraph: unicode(f15f);
$nc-paralympic-games: unicode(f160);
$nc-parent: unicode(f161);
$nc-paris-tower: unicode(f162);
$nc-park: unicode(f163);
$nc-parking-sensors: unicode(f164);
$nc-parking: unicode(f165);
$nc-parrot: unicode(f166);
$nc-party: unicode(f167);
$nc-passenger: unicode(f168);
$nc-passport: unicode(f169);
$nc-password: unicode(f16a);
$nc-pasta: unicode(f16b);
$nc-patch-19: unicode(f16c);
$nc-patch-34: unicode(f16d);
$nc-patch: unicode(f172);
$nc-path-exclude: unicode(f16e);
$nc-path-intersect: unicode(f16f);
$nc-path-minus: unicode(f170);
$nc-path-unite: unicode(f171);
$nc-pattern-recognition: unicode(f173);
$nc-paw: unicode(f174);
$nc-payee: unicode(f175);
$nc-payment-method: unicode(f176);
$nc-payment: unicode(f177);
$nc-payor: unicode(f178);
$nc-pc-monitor: unicode(f179);
$nc-pc-mouse: unicode(f17a);
$nc-pc-play-media: unicode(f17b);
$nc-pc: unicode(f17c);
$nc-pci-card: unicode(f17d);
$nc-peanut: unicode(f17e);
$nc-pear: unicode(f17f);
$nc-peas: unicode(f180);
$nc-pectoral-machine: unicode(f181);
$nc-pen-01: unicode(f182);
$nc-pen-2: unicode(f183);
$nc-pen-23: unicode(f184);
$nc-pen-tool: unicode(f185);
$nc-pen: unicode(f186);
$nc-pencil-47: unicode(f187);
$nc-pencil: unicode(f188);
$nc-pendant-lighting: unicode(f189);
$nc-pendulum: unicode(f18a);
$nc-penguin: unicode(f18b);
$nc-pennant: unicode(f190);
$nc-pepper: unicode(f18c);
$nc-percent-sign: unicode(f18d);
$nc-percentage-38: unicode(f18e);
$nc-percentage-39: unicode(f18f);
$nc-perfume: unicode(f191);
$nc-personal-trainer: unicode(f199);
$nc-pet-food: unicode(f192);
$nc-pharmacy: unicode(f193);
$nc-phone-button: unicode(f194);
$nc-phone-call-end: unicode(f195);
$nc-phone-call: unicode(f196);
$nc-phone-camera-back: unicode(f197);
$nc-phone-camera-front: unicode(f198);
$nc-phone-charging-2: unicode(f19a);
$nc-phone-charging-3: unicode(f19b);
$nc-phone-charging: unicode(f19c);
$nc-phone-dock: unicode(f19d);
$nc-phone-heart: unicode(f19e);
$nc-phone-heartbeat: unicode(f1a2);
$nc-phone-music: unicode(f19f);
$nc-phone-toolbar: unicode(f1a0);
$nc-phone: unicode(f1a8);
$nc-photo-album: unicode(f1a1);
$nc-photo-editor: unicode(f1aa);
$nc-photo-frame: unicode(f1a3);
$nc-photo-not-allowed: unicode(f1a4);
$nc-photo: unicode(f1a5);
$nc-piano-2: unicode(f1a6);
$nc-piano: unicode(f1a7);
$nc-pickaxe: unicode(f1a9);
$nc-pickle: unicode(f1ab);
$nc-picnic-basket: unicode(f1ac);
$nc-picture: unicode(f1ad);
$nc-pie: unicode(f1ae);
$nc-pig-2: unicode(f1af);
$nc-pig: unicode(f1b0);
$nc-pilcrow: unicode(f1b1);
$nc-pilgrim-hat: unicode(f1b2);
$nc-pill-42: unicode(f1b3);
$nc-pill-43: unicode(f1b4);
$nc-pill-bottle: unicode(f1b5);
$nc-pin-2: unicode(f1b6);
$nc-pin-3: unicode(f1bb);
$nc-pin-4: unicode(f1b7);
$nc-pin-add-2: unicode(f1b8);
$nc-pin-add: unicode(f1b9);
$nc-pin-check: unicode(f1ba);
$nc-pin-copy: unicode(f1bc);
$nc-pin-delete: unicode(f1bd);
$nc-pin-edit: unicode(f1be);
$nc-pin-heart: unicode(f1bf);
$nc-pin-remove-2: unicode(f1c0);
$nc-pin-remove: unicode(f1c1);
$nc-pin-search: unicode(f1c2);
$nc-pin-security: unicode(f1c3);
$nc-pin-settings: unicode(f1c4);
$nc-pin-share: unicode(f1c5);
$nc-pin-star: unicode(f1c6);
$nc-pin-sync: unicode(f1c7);
$nc-pin-time: unicode(f1c8);
$nc-pin-user: unicode(f1c9);
$nc-pin: unicode(f1ca);
$nc-pinch: unicode(f1cb);
$nc-pineapple: unicode(f1cc);
$nc-pins: unicode(f1cd);
$nc-pipe: unicode(f1ce);
$nc-pirate: unicode(f1cf);
$nc-pizza-slice: unicode(f1d0);
$nc-pizza: unicode(f1d1);
$nc-plane: unicode(f1d2);
$nc-planet: unicode(f1d3);
$nc-plant-ground: unicode(f1d4);
$nc-plant-leaf: unicode(f1d5);
$nc-plant-vase: unicode(f1d6);
$nc-plate: unicode(f1d7);
$nc-play-media: unicode(f1d8);
$nc-play-movie: unicode(f1d9);
$nc-player: unicode(f1da);
$nc-playground: unicode(f1db);
$nc-playing-cards: unicode(f1dc);
$nc-playlist: unicode(f1dd);
$nc-plug-2: unicode(f1de);
$nc-plug: unicode(f1df);
$nc-podcast-mic: unicode(f1e0);
$nc-podcast: unicode(f1e1);
$nc-podium-trophy: unicode(f1e2);
$nc-podium: unicode(f1e3);
$nc-point-a: unicode(f1e4);
$nc-point-b: unicode(f1e5);
$nc-pointing-down: unicode(f1e6);
$nc-pointing-left: unicode(f1e7);
$nc-pointing-right: unicode(f1e8);
$nc-pointing-up: unicode(f1e9);
$nc-polaroid-photo: unicode(f1ea);
$nc-polaroid-portrait: unicode(f1eb);
$nc-polaroid-shot-delete: unicode(f1ec);
$nc-polaroid-shot-new: unicode(f1ed);
$nc-polaroid-shots: unicode(f1ee);
$nc-polaroid: unicode(f1ef);
$nc-police: unicode(f1f0);
$nc-poop: unicode(f1f1);
$nc-popcorn: unicode(f1f2);
$nc-pos: unicode(f1f3);
$nc-position-marker: unicode(f1f4);
$nc-position-pin: unicode(f1f5);
$nc-position-user: unicode(f1f6);
$nc-position: unicode(f1f7);
$nc-positive-judgement: unicode(f1f8);
$nc-pot: unicode(f1f9);
$nc-potato: unicode(f1fa);
$nc-potion: unicode(f1fb);
$nc-power-level: unicode(f1fc);
$nc-power-lifting: unicode(f1fd);
$nc-power-rack: unicode(f1fe);
$nc-pram: unicode(f1ff);
$nc-preferences: unicode(f200);
$nc-pregnancy-test: unicode(f201);
$nc-pregnant-woman: unicode(f202);
$nc-present: unicode(f203);
$nc-presentation: unicode(f204);
$nc-print: unicode(f205);
$nc-printer: unicode(f206);
$nc-priority-high: unicode(f207);
$nc-priority-highest: unicode(f208);
$nc-priority-low: unicode(f209);
$nc-priority-lowest: unicode(f20a);
$nc-priority-normal: unicode(f20b);
$nc-privacy-policy: unicode(f20c);
$nc-privacy-settings: unicode(f20d);
$nc-privacy: unicode(f20e);
$nc-profile: unicode(f20f);
$nc-progress-2: unicode(f210);
$nc-progress-indicator: unicode(f211);
$nc-progress: unicode(f212);
$nc-projector: unicode(f213);
$nc-property-agreement: unicode(f214);
$nc-property-app: unicode(f215);
$nc-property-for-sale: unicode(f216);
$nc-property-location: unicode(f217);
$nc-property-sold: unicode(f218);
$nc-property-to-rent: unicode(f219);
$nc-property: unicode(f21a);
$nc-prosciutto: unicode(f21b);
$nc-prototype: unicode(f21c);
$nc-pulse-chart: unicode(f21d);
$nc-pulse-sleep: unicode(f21e);
$nc-pulse: unicode(f21f);
$nc-pumpkin: unicode(f220);
$nc-puzzle-09: unicode(f221);
$nc-puzzle-10: unicode(f222);
$nc-puzzle-toy: unicode(f223);
$nc-puzzled: unicode(f224);
$nc-pyramid: unicode(f225);
$nc-question-mark: unicode(f226);
$nc-questionnaire: unicode(f227);
$nc-quite-happy: unicode(f228);
$nc-quote: unicode(f229);
$nc-r-chat: unicode(f22a);
$nc-r-down-left-arrows: unicode(f22b);
$nc-r-down-right-arrows: unicode(f22c);
$nc-r-up-left-arrows: unicode(f22d);
$nc-r-up-right-arrows: unicode(f22e);
$nc-rabbit: unicode(f22f);
$nc-radar: unicode(f230);
$nc-radiation: unicode(f231);
$nc-radio-btn-checked: unicode(f232);
$nc-radio-btn: unicode(f233);
$nc-radio: unicode(f234);
$nc-rain-hail: unicode(f235);
$nc-rain: unicode(f236);
$nc-rainbow: unicode(f237);
$nc-ram-2: unicode(f238);
$nc-ram: unicode(f239);
$nc-random: unicode(f23a);
$nc-ranking: unicode(f23b);
$nc-rat-head: unicode(f23c);
$nc-rat: unicode(f23d);
$nc-rate-down: unicode(f23e);
$nc-rate-up: unicode(f240);
$nc-raw-image: unicode(f23f);
$nc-razor: unicode(f241);
$nc-read: unicode(f242);
$nc-reading-tablet: unicode(f243);
$nc-reading: unicode(f244);
$nc-real-estate: unicode(f245);
$nc-receipt-list-42: unicode(f246);
$nc-receipt-list-43: unicode(f247);
$nc-receipt: unicode(f248);
$nc-recipe-book-46: unicode(f249);
$nc-recipe-book-47: unicode(f24a);
$nc-recipe-create: unicode(f24b);
$nc-recipe: unicode(f24c);
$nc-record-player: unicode(f24d);
$nc-recycling: unicode(f24e);
$nc-redo: unicode(f24f);
$nc-referee: unicode(f250);
$nc-refresh-01: unicode(f251);
$nc-refresh-02: unicode(f252);
$nc-refresh: unicode(f253);
$nc-refund: unicode(f254);
$nc-reload: unicode(f255);
$nc-remote-control: unicode(f256);
$nc-remove-fav: unicode(f257);
$nc-remove-favorite: unicode(f258);
$nc-remove-like: unicode(f259);
$nc-remove: unicode(f25a);
$nc-repeat-cycle: unicode(f25b);
$nc-repeat: unicode(f25f);
$nc-replay: unicode(f25c);
$nc-reply-all: unicode(f25d);
$nc-reply-arrow: unicode(f25e);
$nc-reply: unicode(f260);
$nc-research: unicode(f261);
$nc-reservation: unicode(f262);
$nc-resistance-band: unicode(f263);
$nc-resize-h: unicode(f264);
$nc-resize-v: unicode(f265);
$nc-respond-arrow: unicode(f266);
$nc-restaurant-menu: unicode(f267);
$nc-restore: unicode(f268);
$nc-rice: unicode(f269);
$nc-right-arrow: unicode(f26a);
$nc-rim: unicode(f26b);
$nc-ring: unicode(f26c);
$nc-rings: unicode(f26d);
$nc-rio: unicode(f26e);
$nc-ripple-anim: unicode(f26f);
$nc-road-2: unicode(f270);
$nc-road-sign-left: unicode(f271);
$nc-road-sign-right: unicode(f272);
$nc-road: unicode(f273);
$nc-roadmap: unicode(f274);
$nc-roast-chicken: unicode(f275);
$nc-roast-turkey: unicode(f276);
$nc-robot-cleaner: unicode(f277);
$nc-robot: unicode(f278);
$nc-robotic-arm: unicode(f279);
$nc-rock: unicode(f27a);
$nc-rolling-pin: unicode(f27b);
$nc-romantic-dinner: unicode(f27c);
$nc-romantic-restaurant: unicode(f27d);
$nc-rome: unicode(f27e);
$nc-rotate-22: unicode(f27f);
$nc-rotate-23: unicode(f280);
$nc-rotate-camera: unicode(f281);
$nc-rotate-left: unicode(f282);
$nc-rotate-right: unicode(f283);
$nc-rotating-bars-anim: unicode(f284);
$nc-roulette: unicode(f289);
$nc-round-dollar: unicode(f285);
$nc-round-euro: unicode(f286);
$nc-round-pound: unicode(f287);
$nc-round-yen: unicode(f288);
$nc-route-alert: unicode(f28a);
$nc-route-close: unicode(f28b);
$nc-route-open: unicode(f28c);
$nc-route: unicode(f28d);
$nc-router: unicode(f28e);
$nc-row-machine: unicode(f28f);
$nc-row-table: unicode(f290);
$nc-rowing-oars: unicode(f291);
$nc-rowing: unicode(f292);
$nc-rugby-ball: unicode(f293);
$nc-rugby: unicode(f294);
$nc-ruler-pencil: unicode(f295);
$nc-run-shoes: unicode(f296);
$nc-runny-nose: unicode(f297);
$nc-s-add: unicode(f298);
$nc-s-ban: unicode(f299);
$nc-s-check: unicode(f29a);
$nc-s-delete: unicode(f29b);
$nc-s-edit: unicode(f29c);
$nc-s-info: unicode(f29d);
$nc-s-pulse: unicode(f29e);
$nc-s-question: unicode(f29f);
$nc-s-remove: unicode(f2a0);
$nc-s-warning: unicode(f2a1);
$nc-sad: unicode(f2a2);
$nc-safe: unicode(f2a3);
$nc-salad: unicode(f2a4);
$nc-sale: unicode(f2a5);
$nc-salt: unicode(f2a6);
$nc-santa-hat: unicode(f2a7);
$nc-satellite-dish: unicode(f2a8);
$nc-satellite: unicode(f2a9);
$nc-satisfied: unicode(f2aa);
$nc-sauna: unicode(f2ab);
$nc-sausage: unicode(f2ac);
$nc-save-for-later: unicode(f2ad);
$nc-save-planet: unicode(f2ae);
$nc-save-the-date: unicode(f2af);
$nc-save-to-list: unicode(f2b0);
$nc-saved-items: unicode(f2b1);
$nc-savings: unicode(f2b2);
$nc-saxophone: unicode(f2b3);
$nc-scale-2: unicode(f2b4);
$nc-scale-3: unicode(f2b5);
$nc-scale-4: unicode(f2b6);
$nc-scale-down: unicode(f2b7);
$nc-scale-horizontal: unicode(f2b8);
$nc-scale-up: unicode(f2b9);
$nc-scale-vertical: unicode(f2ba);
$nc-scale: unicode(f2bb);
$nc-scan: unicode(f2bc);
$nc-scarf: unicode(f2bd);
$nc-scented-candle: unicode(f2c1);
$nc-school: unicode(f2be);
$nc-scissors-2: unicode(f2bf);
$nc-scissors-dashed: unicode(f2c0);
$nc-scissors: unicode(f2c7);
$nc-scooter: unicode(f2c2);
$nc-scotch: unicode(f2c3);
$nc-screen-enlarge: unicode(f2c4);
$nc-screen-expand: unicode(f2c5);
$nc-screen-maximize: unicode(f2c6);
$nc-screen-reader: unicode(f2c8);
$nc-screen-rotation: unicode(f2c9);
$nc-screen-sharing-2: unicode(f2ca);
$nc-screen-sharing-off-2: unicode(f2cb);
$nc-screen-touch: unicode(f2cc);
$nc-scroll-horizontal: unicode(f2cd);
$nc-scroll-vertical: unicode(f2ce);
$nc-sd-card: unicode(f2cf);
$nc-search-3: unicode(f2d0);
$nc-search-content: unicode(f2d1);
$nc-search-property: unicode(f2d2);
$nc-search-zoom-in: unicode(f2d3);
$nc-search-zoom-out: unicode(f2d4);
$nc-search: unicode(f2d5);
$nc-seat: unicode(f2d6);
$nc-seatbelt: unicode(f2d7);
$nc-security-gate: unicode(f2d8);
$nc-security-officer: unicode(f2d9);
$nc-security: unicode(f2da);
$nc-segmentation: unicode(f2db);
$nc-select: unicode(f2dc);
$nc-selection: unicode(f2dd);
$nc-selfie-2: unicode(f2de);
$nc-selfie: unicode(f2df);
$nc-send-message: unicode(f2e0);
$nc-send-to-phone: unicode(f2e1);
$nc-send: unicode(f2e2);
$nc-sensor: unicode(f2e3);
$nc-separate-branch: unicode(f2e4);
$nc-separate-directions: unicode(f2e5);
$nc-separate: unicode(f2e6);
$nc-server-rack: unicode(f2e7);
$nc-server: unicode(f2eb);
$nc-settings-gear: unicode(f2e8);
$nc-settings: unicode(f2e9);
$nc-setup-options: unicode(f2ea);
$nc-setup-preferences: unicode(f2ec);
$nc-setup-tools: unicode(f2ed);
$nc-seven: unicode(f2ee);
$nc-sf-bridge: unicode(f2ef);
$nc-shaker: unicode(f2f0);
$nc-shape-adjust: unicode(f2f1);
$nc-shape-arrow: unicode(f2f2);
$nc-shape-circle: unicode(f2f3);
$nc-shape-custom: unicode(f2f4);
$nc-shape-line: unicode(f2f5);
$nc-shape-oval: unicode(f2f6);
$nc-shape-polygon-2: unicode(f2f7);
$nc-shape-polygon: unicode(f2fc);
$nc-shape-rectangle: unicode(f2f8);
$nc-shape-square: unicode(f2f9);
$nc-shape-star: unicode(f2fa);
$nc-shape-triangle-2: unicode(f2fb);
$nc-shape-triangle: unicode(f2fd);
$nc-shapes: unicode(f2fe);
$nc-share-2: unicode(f2ff);
$nc-share-3: unicode(f300);
$nc-share: unicode(f306);
$nc-sharing: unicode(f301);
$nc-shark-2: unicode(f302);
$nc-shark: unicode(f303);
$nc-sharpen: unicode(f304);
$nc-sharpener: unicode(f305);
$nc-sheep: unicode(f307);
$nc-shell: unicode(f30e);
$nc-shield: unicode(f308);
$nc-shinto: unicode(f311);
$nc-shirt-business: unicode(f309);
$nc-shirt-buttons: unicode(f30a);
$nc-shirt-neck: unicode(f30b);
$nc-shirt: unicode(f30c);
$nc-shoe-man: unicode(f30d);
$nc-shoe-woman: unicode(f30f);
$nc-shop-location: unicode(f310);
$nc-shop: unicode(f315);
$nc-shopping-bag: unicode(f312);
$nc-shopping-cart-2: unicode(f313);
$nc-shopping-cart: unicode(f314);
$nc-shopping-label: unicode(f316);
$nc-shopping-tag: unicode(f317);
$nc-shorts: unicode(f318);
$nc-shotgun: unicode(f319);
$nc-shovel: unicode(f31a);
$nc-show: unicode(f31b);
$nc-shower: unicode(f31c);
$nc-shrimp: unicode(f31d);
$nc-shuffle-2: unicode(f31e);
$nc-shuffle: unicode(f31f);
$nc-shuttle: unicode(f320);
$nc-shuttlecock: unicode(f321);
$nc-shy: unicode(f322);
$nc-sick: unicode(f323);
$nc-sickle: unicode(f324);
$nc-sidebar: unicode(f328);
$nc-sign-board: unicode(f325);
$nc-sign-down: unicode(f326);
$nc-sign-left: unicode(f327);
$nc-sign-right: unicode(f329);
$nc-sign-up: unicode(f32a);
$nc-sign: unicode(f32b);
$nc-signal: unicode(f32c);
$nc-signature: unicode(f32d);
$nc-silly: unicode(f32e);
$nc-sim-card: unicode(f32f);
$nc-single-05: unicode(f330);
$nc-single-bed: unicode(f331);
$nc-single-position: unicode(f332);
$nc-sink-faucet: unicode(f333);
$nc-sink: unicode(f334);
$nc-six: unicode(f335);
$nc-size-large: unicode(f336);
$nc-size-medium: unicode(f337);
$nc-size-small: unicode(f338);
$nc-skateboard-2: unicode(f339);
$nc-skateboard: unicode(f33a);
$nc-skateboarding: unicode(f33b);
$nc-skating: unicode(f33c);
$nc-skiing: unicode(f33d);
$nc-skipping-rope: unicode(f33e);
$nc-skirt: unicode(f33f);
$nc-skull-2: unicode(f340);
$nc-skull: unicode(f341);
$nc-slacks-12: unicode(f342);
$nc-slacks-13: unicode(f343);
$nc-sleep-2: unicode(f344);
$nc-sleep: unicode(f345);
$nc-sleeping-baby: unicode(f34d);
$nc-slice: unicode(f346);
$nc-slide-left: unicode(f347);
$nc-slide-right: unicode(f348);
$nc-slider: unicode(f349);
$nc-slippers: unicode(f34a);
$nc-slot-machine: unicode(f34b);
$nc-sloth: unicode(f34c);
$nc-smart-house: unicode(f34e);
$nc-smart: unicode(f34f);
$nc-smartphone: unicode(f350);
$nc-smartwatch: unicode(f351);
$nc-smile: unicode(f352);
$nc-smiling-face-glasses: unicode(f353);
$nc-smiling-face-sunglasses: unicode(f354);
$nc-smoking: unicode(f355);
$nc-smoothie: unicode(f356);
$nc-snack: unicode(f357);
$nc-snake: unicode(f358);
$nc-sneeze: unicode(f359);
$nc-sniper-rifle: unicode(f35a);
$nc-snorkel-mask: unicode(f35b);
$nc-snow-ball: unicode(f35c);
$nc-snow: unicode(f35d);
$nc-snowboard: unicode(f35e);
$nc-snowboarding: unicode(f35f);
$nc-snowman-head: unicode(f360);
$nc-snowman: unicode(f361);
$nc-soap: unicode(f362);
$nc-soccer-ball: unicode(f363);
$nc-soccer-field: unicode(f364);
$nc-soccer: unicode(f365);
$nc-social-distancing: unicode(f366);
$nc-social-sharing: unicode(f367);
$nc-sock: unicode(f368);
$nc-socket-europe-1: unicode(f369);
$nc-socket-europe-2: unicode(f36a);
$nc-socket-uk: unicode(f36b);
$nc-socket: unicode(f36c);
$nc-sofa: unicode(f36d);
$nc-soft-drink: unicode(f36e);
$nc-soldier: unicode(f36f);
$nc-solider-helmet: unicode(f370);
$nc-sort-tool: unicode(f371);
$nc-sound-wave: unicode(f372);
$nc-sound: unicode(f373);
$nc-soundwave: unicode(f374);
$nc-soup: unicode(f375);
$nc-soy-sauce: unicode(f376);
$nc-spa-rocks: unicode(f377);
$nc-spa: unicode(f378);
$nc-spaceship: unicode(f379);
$nc-spades-suit: unicode(f37a);
$nc-speaker-2: unicode(f37b);
$nc-speaker: unicode(f37c);
$nc-speechless: unicode(f37d);
$nc-speedometer: unicode(f37e);
$nc-sperm: unicode(f37f);
$nc-spider: unicode(f380);
$nc-spinning-bike: unicode(f381);
$nc-spiteful: unicode(f382);
$nc-split-branch: unicode(f383);
$nc-split-horizontal: unicode(f384);
$nc-split-vertical: unicode(f385);
$nc-split: unicode(f386);
$nc-sport-bag: unicode(f387);
$nc-sport-mode: unicode(f388);
$nc-sports-bra: unicode(f389);
$nc-sports-fan: unicode(f38a);
$nc-sports-tank: unicode(f38b);
$nc-spray-bottle: unicode(f38c);
$nc-spray-can: unicode(f38d);
$nc-square-marker: unicode(f38e);
$nc-square-pin: unicode(f38f);
$nc-squares-anim-2: unicode(f390);
$nc-squares-anim: unicode(f391);
$nc-ssd: unicode(f392);
$nc-stack: unicode(f393);
$nc-stadium: unicode(f394);
$nc-stair-climber: unicode(f395);
$nc-stairs: unicode(f396);
$nc-stamp: unicode(f397);
$nc-standing-man: unicode(f398);
$nc-standing-woman: unicode(f399);
$nc-star-rate: unicode(f39a);
$nc-star: unicode(f39b);
$nc-statistics: unicode(f39c);
$nc-stay-home: unicode(f39d);
$nc-steak-2: unicode(f39e);
$nc-steak: unicode(f39f);
$nc-steam-iron: unicode(f3a0);
$nc-steering-wheel: unicode(f3a1);
$nc-steps: unicode(f3a2);
$nc-stethoscope: unicode(f3a3);
$nc-sticker: unicode(f3a4);
$nc-stock-2: unicode(f3a5);
$nc-stock-market: unicode(f3a6);
$nc-stopwatch: unicode(f3a7);
$nc-storage-hanger: unicode(f3a8);
$nc-storage-shelves: unicode(f3a9);
$nc-storage-unit: unicode(f3aa);
$nc-store: unicode(f3ab);
$nc-strawberry: unicode(f3ac);
$nc-stretch: unicode(f3ad);
$nc-stretching: unicode(f3ae);
$nc-strikethrough: unicode(f3af);
$nc-style: unicode(f3b0);
$nc-submachine-gun: unicode(f3b1);
$nc-submarine: unicode(f3b2);
$nc-subscript: unicode(f3b3);
$nc-subtitles: unicode(f3b4);
$nc-sugar: unicode(f3b5);
$nc-sun-cloud-drop: unicode(f3b6);
$nc-sun-cloud-fog: unicode(f3b7);
$nc-sun-cloud-hail: unicode(f3b8);
$nc-sun-cloud-light: unicode(f3b9);
$nc-sun-cloud-rain: unicode(f3ba);
$nc-sun-cloud-snow-54: unicode(f3bb);
$nc-sun-cloud-snow-55: unicode(f3bc);
$nc-sun-cloud: unicode(f3bd);
$nc-sun-fog-29: unicode(f3be);
$nc-sun-fog-30: unicode(f3bf);
$nc-sun-fog-43: unicode(f3c0);
$nc-sun: unicode(f3c1);
$nc-sunglasses-48: unicode(f3c2);
$nc-sunglasses: unicode(f3c3);
$nc-superscript: unicode(f3c4);
$nc-support: unicode(f3c5);
$nc-surfboard: unicode(f3c6);
$nc-surprise: unicode(f3c7);
$nc-survey: unicode(f3c8);
$nc-sushi: unicode(f3c9);
$nc-swap-horizontal: unicode(f3ca);
$nc-swap-vertical: unicode(f3cb);
$nc-swimming-pool: unicode(f3cc);
$nc-swimming: unicode(f3cd);
$nc-swimsuit: unicode(f3ce);
$nc-swipe-bottom: unicode(f3cf);
$nc-swipe-left: unicode(f3d0);
$nc-swipe-right: unicode(f3d1);
$nc-swipe-up: unicode(f3d2);
$nc-swiss-knife: unicode(f3d3);
$nc-switches: unicode(f3d4);
$nc-sword: unicode(f3d5);
$nc-sync-devices: unicode(f3d6);
$nc-syringe: unicode(f3d7);
$nc-system-configuration: unicode(f3d8);
$nc-system-preferences: unicode(f3d9);
$nc-system-update: unicode(f3da);
$nc-t-add: unicode(f3db);
$nc-t-delete: unicode(f3dc);
$nc-t-remove: unicode(f3dd);
$nc-table-lamp: unicode(f3de);
$nc-table-layout: unicode(f3df);
$nc-table-move: unicode(f3e0);
$nc-table-slide: unicode(f3e1);
$nc-table-tennis-bat: unicode(f3e2);
$nc-table: unicode(f3e3);
$nc-tablet-2: unicode(f3e4);
$nc-tablet-charging: unicode(f3e5);
$nc-tablet-mobile: unicode(f3e6);
$nc-tablet-toolbar: unicode(f3e7);
$nc-tablet: unicode(f3e8);
$nc-tacos: unicode(f3e9);
$nc-tactic: unicode(f3ea);
$nc-tag-add: unicode(f3eb);
$nc-tag-check: unicode(f3ec);
$nc-tag-cut: unicode(f3ed);
$nc-tag-loyalty: unicode(f3ee);
$nc-tag-remove: unicode(f3ef);
$nc-tag-sale: unicode(f3f0);
$nc-tag: unicode(f3f1);
$nc-tags-stack: unicode(f3f2);
$nc-take-off: unicode(f3f3);
$nc-takeaway: unicode(f3f4);
$nc-taoism: unicode(f3f5);
$nc-tap-01: unicode(f3f6);
$nc-tap-02: unicode(f3f7);
$nc-tape: unicode(f3f8);
$nc-target: unicode(f3f9);
$nc-taxi: unicode(f3fa);
$nc-tea-bag: unicode(f3fb);
$nc-tea: unicode(f3fc);
$nc-teddy-bear: unicode(f3fd);
$nc-telephone: unicode(f3fe);
$nc-telescope: unicode(f3ff);
$nc-temperature-2: unicode(f400);
$nc-temperature: unicode(f401);
$nc-temple-25: unicode(f402);
$nc-temple: unicode(f403);
$nc-tennis-ball: unicode(f404);
$nc-tennis-racket: unicode(f405);
$nc-tennis: unicode(f406);
$nc-terrace: unicode(f407);
$nc-text-2: unicode(f408);
$nc-text-size: unicode(f409);
$nc-text: unicode(f40a);
$nc-texture: unicode(f40b);
$nc-theater-curtains: unicode(f40c);
$nc-theater: unicode(f40d);
$nc-thermometer: unicode(f40e);
$nc-three-dimensional-object: unicode(f40f);
$nc-three-dimensional-world: unicode(f410);
$nc-three-way-direction: unicode(f411);
$nc-three: unicode(f412);
$nc-thumb-down: unicode(f413);
$nc-thumb-up: unicode(f414);
$nc-ticket: unicode(f415);
$nc-tie-01: unicode(f416);
$nc-tie-02: unicode(f417);
$nc-tie-bow: unicode(f418);
$nc-time-alarm: unicode(f419);
$nc-time-clock: unicode(f41a);
$nc-time-machine: unicode(f41b);
$nc-timeline: unicode(f41c);
$nc-timer: unicode(f41d);
$nc-tnt-explosives: unicode(f41e);
$nc-toast: unicode(f41f);
$nc-toaster: unicode(f420);
$nc-todo: unicode(f421);
$nc-toggle: unicode(f422);
$nc-toilet-paper: unicode(f423);
$nc-toilet: unicode(f424);
$nc-toilette: unicode(f425);
$nc-tomato: unicode(f426);
$nc-tool-blur: unicode(f427);
$nc-tool-hand: unicode(f428);
$nc-tool-select: unicode(f429);
$nc-tooth: unicode(f42a);
$nc-towel-hanger: unicode(f42b);
$nc-towel: unicode(f42c);
$nc-track-delivery: unicode(f42d);
$nc-tracking: unicode(f42e);
$nc-tractor: unicode(f42f);
$nc-traffic: unicode(f430);
$nc-train-speed: unicode(f431);
$nc-train: unicode(f432);
$nc-tram: unicode(f433);
$nc-transaction: unicode(f434);
$nc-transactions: unicode(f435);
$nc-transform-2d: unicode(f436);
$nc-transform-origin: unicode(f437);
$nc-transform: unicode(f438);
$nc-translation: unicode(f439);
$nc-transparent: unicode(f43a);
$nc-trash-can: unicode(f43b);
$nc-trash: unicode(f43c);
$nc-travel-makeup-mirror: unicode(f442);
$nc-treadmill: unicode(f43d);
$nc-treasure-map-21: unicode(f43e);
$nc-treasure-map-40: unicode(f43f);
$nc-tree-01: unicode(f440);
$nc-tree-02: unicode(f441);
$nc-tree-03: unicode(f443);
$nc-tree-ball: unicode(f444);
$nc-tree: unicode(f445);
$nc-trend-down: unicode(f446);
$nc-trend-up: unicode(f447);
$nc-triangle-down: unicode(f448);
$nc-triangle-left: unicode(f449);
$nc-triangle-line-down: unicode(f44a);
$nc-triangle-line-left: unicode(f44b);
$nc-triangle-line-right: unicode(f44c);
$nc-triangle-line-up: unicode(f44d);
$nc-triangle-right: unicode(f44e);
$nc-triangle-sm-down: unicode(f44f);
$nc-triangle-sm-left: unicode(f450);
$nc-triangle-sm-right: unicode(f451);
$nc-triangle-sm-up: unicode(f452);
$nc-triangle-up: unicode(f453);
$nc-tripod: unicode(f454);
$nc-trophy: unicode(f455);
$nc-truck-front: unicode(f456);
$nc-trumpet: unicode(f45d);
$nc-trunk: unicode(f457);
$nc-tshirt-53: unicode(f458);
$nc-tshirt-54: unicode(f459);
$nc-tshirt-sport: unicode(f45a);
$nc-tty: unicode(f45b);
$nc-turn-e: unicode(f45c);
$nc-turn-n: unicode(f45e);
$nc-turn-s: unicode(f45f);
$nc-turn-w: unicode(f460);
$nc-turtle: unicode(f461);
$nc-tv-stand: unicode(f462);
$nc-tv: unicode(f463);
$nc-two-way-direction: unicode(f464);
$nc-two: unicode(f465);
$nc-umbrella-13: unicode(f46c);
$nc-umbrella-14: unicode(f466);
$nc-underline: unicode(f467);
$nc-underwear-man: unicode(f468);
$nc-underwear: unicode(f469);
$nc-undo: unicode(f46a);
$nc-ungroup: unicode(f46b);
$nc-unite-2: unicode(f46d);
$nc-unite: unicode(f46e);
$nc-unlink: unicode(f46f);
$nc-unlocked: unicode(f470);
$nc-up-arrow: unicode(f471);
$nc-upload-data: unicode(f472);
$nc-upload-file: unicode(f473);
$nc-upload: unicode(f474);
$nc-upset-13: unicode(f475);
$nc-upset-14: unicode(f476);
$nc-url: unicode(f477);
$nc-usb: unicode(f478);
$nc-user-frame-31: unicode(f47e);
$nc-user-frame-32: unicode(f479);
$nc-user-frame-33: unicode(f47a);
$nc-user: unicode(f47b);
$nc-users-mm: unicode(f47c);
$nc-users-wm: unicode(f47d);
$nc-users-ww: unicode(f47f);
$nc-utility-bench: unicode(f480);
$nc-vacuum-cleaner: unicode(f481);
$nc-vampire: unicode(f482);
$nc-vector: unicode(f483);
$nc-vegan: unicode(f484);
$nc-ventilation: unicode(f485);
$nc-verified: unicode(f486);
$nc-vespa-front: unicode(f48a);
$nc-vespa: unicode(f487);
$nc-vest-31: unicode(f48e);
$nc-vest: unicode(f488);
$nc-vibrance: unicode(f489);
$nc-video-camera: unicode(f48b);
$nc-video-gallery-2: unicode(f48c);
$nc-video-gallery: unicode(f48d);
$nc-video-off: unicode(f48f);
$nc-video-player: unicode(f490);
$nc-video-playlist: unicode(f494);
$nc-video: unicode(f491);
$nc-view: unicode(f492);
$nc-vignette: unicode(f493);
$nc-vintage-computer: unicode(f495);
$nc-vintage-tv: unicode(f49b);
$nc-violin: unicode(f496);
$nc-virtual-assistant-2: unicode(f497);
$nc-virtual-assistant: unicode(f498);
$nc-virtual-environment: unicode(f499);
$nc-virtual-reality: unicode(f49a);
$nc-virus: unicode(f4a0);
$nc-voice-recognition: unicode(f49c);
$nc-voice-record: unicode(f49d);
$nc-volleyball-player: unicode(f49e);
$nc-volleyball: unicode(f49f);
$nc-volume-2: unicode(f4a1);
$nc-volume-down: unicode(f4a2);
$nc-volume-mute: unicode(f4a3);
$nc-volume-off: unicode(f4a4);
$nc-volume-up: unicode(f4a5);
$nc-volume: unicode(f4a6);
$nc-vpn: unicode(f4a7);
$nc-vr-controller: unicode(f4a8);
$nc-vr-headset: unicode(f4a9);
$nc-waffle: unicode(f4aa);
$nc-walk: unicode(f4ab);
$nc-walking-aid: unicode(f4ae);
$nc-walking-support: unicode(f4af);
$nc-wallet-43: unicode(f4ac);
$nc-wallet-44: unicode(f4ad);
$nc-wallet-90: unicode(f4b0);
$nc-wallet: unicode(f4b1);
$nc-wand-11: unicode(f4b2);
$nc-wardrobe-2: unicode(f4b3);
$nc-wardrobe-3: unicode(f4b4);
$nc-wardrobe-4: unicode(f4b5);
$nc-wardrobe: unicode(f4b6);
$nc-warning-sign: unicode(f4b7);
$nc-wash-30: unicode(f4b8);
$nc-wash-60: unicode(f4b9);
$nc-wash-90: unicode(f4ba);
$nc-wash-hand: unicode(f4bb);
$nc-wash-hands: unicode(f4bc);
$nc-washing-fluid: unicode(f4bd);
$nc-washing-machine: unicode(f4be);
$nc-waste-danger: unicode(f4bf);
$nc-waste-recycling: unicode(f4c0);
$nc-waste: unicode(f4c1);
$nc-watch-2: unicode(f4c4);
$nc-watch-dev: unicode(f4c2);
$nc-watch-heart: unicode(f4c3);
$nc-watch-heartbeat: unicode(f4ca);
$nc-watch: unicode(f4c5);
$nc-water-aerobics: unicode(f4c6);
$nc-water-hand: unicode(f4c7);
$nc-water-polo-ball: unicode(f4c8);
$nc-water-polo: unicode(f4c9);
$nc-water-sink: unicode(f4cb);
$nc-water-surface: unicode(f4cc);
$nc-water-wave: unicode(f4cd);
$nc-water: unicode(f4ce);
$nc-watermelon: unicode(f4cf);
$nc-wc: unicode(f4d0);
$nc-web-design: unicode(f4d1);
$nc-web-hyperlink: unicode(f4d2);
$nc-web-link: unicode(f4d3);
$nc-web-url: unicode(f4d4);
$nc-webcam-2: unicode(f4d5);
$nc-webcam: unicode(f4d6);
$nc-webpage: unicode(f4d7);
$nc-wedding-arch: unicode(f4d8);
$nc-wedding-cake: unicode(f4d9);
$nc-wedding-ring: unicode(f4da);
$nc-wedding-rings: unicode(f4db);
$nc-weed: unicode(f4e0);
$nc-weight-bench: unicode(f4dc);
$nc-weight-gain: unicode(f4dd);
$nc-weight-loss: unicode(f4de);
$nc-weight-plate: unicode(f4df);
$nc-weight-scale: unicode(f4e1);
$nc-what: unicode(f4e2);
$nc-wheel-2: unicode(f4e3);
$nc-wheel: unicode(f4e4);
$nc-wheelchair-2: unicode(f4e5);
$nc-wheelchair-ramp: unicode(f4e6);
$nc-wheelchair: unicode(f4e7);
$nc-whisk: unicode(f4e8);
$nc-whiskers: unicode(f4e9);
$nc-whistle: unicode(f4ea);
$nc-white-balance: unicode(f4eb);
$nc-white-house: unicode(f4ec);
$nc-widget: unicode(f4ed);
$nc-wifi-2: unicode(f4ee);
$nc-wifi-off: unicode(f4ef);
$nc-wifi-protected: unicode(f4f0);
$nc-wifi-router: unicode(f4f1);
$nc-wifi: unicode(f4f2);
$nc-wind-2: unicode(f4f3);
$nc-wind: unicode(f4f4);
$nc-window-add: unicode(f4f5);
$nc-window-code: unicode(f4f6);
$nc-window-delete: unicode(f4f7);
$nc-window-dev: unicode(f4f8);
$nc-window-maximize: unicode(f4f9);
$nc-window-minimize: unicode(f4fa);
$nc-window-paragraph: unicode(f4fb);
$nc-window-responsive: unicode(f4fc);
$nc-window: unicode(f4fd);
$nc-windsurfing: unicode(f4fe);
$nc-wine-list: unicode(f4ff);
$nc-wink-06: unicode(f500);
$nc-wink-11: unicode(f501);
$nc-wink-69: unicode(f502);
$nc-winner: unicode(f503);
$nc-wireframe: unicode(f504);
$nc-wireless-charging: unicode(f505);
$nc-witch-hat: unicode(f506);
$nc-wolf: unicode(f507);
$nc-woman-2: unicode(f508);
$nc-woman-21: unicode(f509);
$nc-woman-24: unicode(f50a);
$nc-woman-down: unicode(f50b);
$nc-woman-man: unicode(f50c);
$nc-woman-up-front: unicode(f50d);
$nc-woman-up: unicode(f50e);
$nc-wood: unicode(f50f);
$nc-wool-ball: unicode(f510);
$nc-workout-plan: unicode(f511);
$nc-world-2: unicode(f512);
$nc-world-marker: unicode(f513);
$nc-world-pin: unicode(f514);
$nc-world: unicode(f515);
$nc-wrench-tool: unicode(f516);
$nc-wrench: unicode(f517);
$nc-xmas-sock: unicode(f518);
$nc-yoga: unicode(f519);
$nc-yogurt: unicode(f51a);
$nc-zero: unicode(f51b);
$nc-zipped-file: unicode(f51c);
$nc-zombie: unicode(f51d);
$nc-zoom-e: unicode(f51e);
$nc-zoom-in: unicode(f51f);
$nc-zoom-out: unicode(f520);
$nc-zoom: unicode(f521);

.nc-2x-drag-down::before {
  content: $nc-2x-drag-down;
}

.nc-2x-drag-up::before {
  content: $nc-2x-drag-up;
}

.nc-2x-swipe-down::before {
  content: $nc-2x-swipe-down;
}

.nc-2x-swipe-left::before {
  content: $nc-2x-swipe-left;
}

.nc-2x-swipe-right::before {
  content: $nc-2x-swipe-right;
}

.nc-2x-swipe-up::before {
  content: $nc-2x-swipe-up;
}

.nc-2x-tap::before {
  content: $nc-2x-tap;
}

.nc-3d-29::before {
  content: $nc-3d-29;
}

.nc-3d-glasses::before {
  content: $nc-3d-glasses;
}

.nc-3d-model::before {
  content: $nc-3d-model;
}

.nc-3d-printing::before {
  content: $nc-3d-printing;
}

.nc-3x-swipe-left::before {
  content: $nc-3x-swipe-left;
}

.nc-3x-swipe-right::before {
  content: $nc-3x-swipe-right;
}

.nc-3x-swipe-up::before {
  content: $nc-3x-swipe-up;
}

.nc-3x-tap::before {
  content: $nc-3x-tap;
}

.nc-4x-swipe-left::before {
  content: $nc-4x-swipe-left;
}

.nc-4x-swipe-right::before {
  content: $nc-4x-swipe-right;
}

.nc-4x-swipe-up::before {
  content: $nc-4x-swipe-up;
}

.nc-a-add::before {
  content: $nc-a-add;
}

.nc-a-chart::before {
  content: $nc-a-chart;
}

.nc-a-chat::before {
  content: $nc-a-chat;
}

.nc-a-check::before {
  content: $nc-a-check;
}

.nc-a-delete::before {
  content: $nc-a-delete;
}

.nc-a-edit::before {
  content: $nc-a-edit;
}

.nc-a-heart::before {
  content: $nc-a-heart;
}

.nc-a-location::before {
  content: $nc-a-location;
}

.nc-a-remove::before {
  content: $nc-a-remove;
}

.nc-a-search::before {
  content: $nc-a-search;
}

.nc-a-security::before {
  content: $nc-a-security;
}

.nc-a-share::before {
  content: $nc-a-share;
}

.nc-a-star::before {
  content: $nc-a-star;
}

.nc-a-sync::before {
  content: $nc-a-sync;
}

.nc-a-tag-add::before {
  content: $nc-a-tag-add;
}

.nc-a-tag-remove::before {
  content: $nc-a-tag-remove;
}

.nc-a-tag::before {
  content: $nc-a-tag;
}

.nc-a-time::before {
  content: $nc-a-time;
}

.nc-abc::before {
  content: $nc-abc;
}

.nc-access-key::before {
  content: $nc-access-key;
}

.nc-accessibility-lift::before {
  content: $nc-accessibility-lift;
}

.nc-accessibility::before {
  content: $nc-accessibility;
}

.nc-account::before {
  content: $nc-account;
}

.nc-acorn::before {
  content: $nc-acorn;
}

.nc-active-38::before {
  content: $nc-active-38;
}

.nc-active-40::before {
  content: $nc-active-40;
}

.nc-adaptive-bike::before {
  content: $nc-adaptive-bike;
}

.nc-add-27::before {
  content: $nc-add-27;
}

.nc-add-29::before {
  content: $nc-add-29;
}

.nc-add-fav::before {
  content: $nc-add-fav;
}

.nc-add-favorite::before {
  content: $nc-add-favorite;
}

.nc-add-like::before {
  content: $nc-add-like;
}

.nc-add-notification::before {
  content: $nc-add-notification;
}

.nc-add-to-cart-2::before {
  content: $nc-add-to-cart-2;
}

.nc-add-to-cart::before {
  content: $nc-add-to-cart;
}

.nc-add::before {
  content: $nc-add;
}

.nc-adult-content::before {
  content: $nc-adult-content;
}

.nc-agenda-bookmark::before {
  content: $nc-agenda-bookmark;
}

.nc-agenda::before {
  content: $nc-agenda;
}

.nc-ai::before {
  content: $nc-ai;
}

.nc-air-baloon::before {
  content: $nc-air-baloon;
}

.nc-air-bomb::before {
  content: $nc-air-bomb;
}

.nc-air-conditioner::before {
  content: $nc-air-conditioner;
}

.nc-airbag::before {
  content: $nc-airbag;
}

.nc-airplane::before {
  content: $nc-airplane;
}

.nc-airport-trolley::before {
  content: $nc-airport-trolley;
}

.nc-airport::before {
  content: $nc-airport;
}

.nc-alarm-add::before {
  content: $nc-alarm-add;
}

.nc-alarm-disable::before {
  content: $nc-alarm-disable;
}

.nc-alarm::before {
  content: $nc-alarm;
}

.nc-album::before {
  content: $nc-album;
}

.nc-alcohol::before {
  content: $nc-alcohol;
}

.nc-algorithm::before {
  content: $nc-algorithm;
}

.nc-alien-29::before {
  content: $nc-alien-29;
}

.nc-alien-33::before {
  content: $nc-alien-33;
}

.nc-align-bottom::before {
  content: $nc-align-bottom;
}

.nc-align-center-horizontal::before {
  content: $nc-align-center-horizontal;
}

.nc-align-center-vertical::before {
  content: $nc-align-center-vertical;
}

.nc-align-center::before {
  content: $nc-align-center;
}

.nc-align-justify::before {
  content: $nc-align-justify;
}

.nc-align-left-2::before {
  content: $nc-align-left-2;
}

.nc-align-left::before {
  content: $nc-align-left;
}

.nc-align-right-2::before {
  content: $nc-align-right-2;
}

.nc-align-right::before {
  content: $nc-align-right;
}

.nc-align-top::before {
  content: $nc-align-top;
}

.nc-all-directions::before {
  content: $nc-all-directions;
}

.nc-alpha-order::before {
  content: $nc-alpha-order;
}

.nc-ambulance::before {
  content: $nc-ambulance;
}

.nc-ampersand::before {
  content: $nc-ampersand;
}

.nc-analytics::before {
  content: $nc-analytics;
}

.nc-anchor::before {
  content: $nc-anchor;
}

.nc-android::before {
  content: $nc-android;
}

.nc-angle::before {
  content: $nc-angle;
}

.nc-angry-10::before {
  content: $nc-angry-10;
}

.nc-angry-44::before {
  content: $nc-angry-44;
}

.nc-animation-14::before {
  content: $nc-animation-14;
}

.nc-animation-31::before {
  content: $nc-animation-31;
}

.nc-animation-32::before {
  content: $nc-animation-32;
}

.nc-antenna::before {
  content: $nc-antenna;
}

.nc-anti-shake::before {
  content: $nc-anti-shake;
}

.nc-apartment::before {
  content: $nc-apartment;
}

.nc-aperture::before {
  content: $nc-aperture;
}

.nc-api::before {
  content: $nc-api;
}

.nc-app-services::before {
  content: $nc-app-services;
}

.nc-app-store::before {
  content: $nc-app-store;
}

.nc-app::before {
  content: $nc-app;
}

.nc-apple-2::before {
  content: $nc-apple-2;
}

.nc-apple::before {
  content: $nc-apple;
}

.nc-appointment::before {
  content: $nc-appointment;
}

.nc-apps::before {
  content: $nc-apps;
}

.nc-apron::before {
  content: $nc-apron;
}

.nc-arcade::before {
  content: $nc-arcade;
}

.nc-archer::before {
  content: $nc-archer;
}

.nc-archery-target::before {
  content: $nc-archery-target;
}

.nc-archery::before {
  content: $nc-archery;
}

.nc-archive-check::before {
  content: $nc-archive-check;
}

.nc-archive-content::before {
  content: $nc-archive-content;
}

.nc-archive-doc-check::before {
  content: $nc-archive-doc-check;
}

.nc-archive-doc::before {
  content: $nc-archive-doc;
}

.nc-archive-drawer::before {
  content: $nc-archive-drawer;
}

.nc-archive-file-check::before {
  content: $nc-archive-file-check;
}

.nc-archive-file::before {
  content: $nc-archive-file;
}

.nc-archive::before {
  content: $nc-archive;
}

.nc-armchair::before {
  content: $nc-armchair;
}

.nc-armor::before {
  content: $nc-armor;
}

.nc-army::before {
  content: $nc-army;
}

.nc-arrow-bottom-left::before {
  content: $nc-arrow-bottom-left;
}

.nc-arrow-bottom-right::before {
  content: $nc-arrow-bottom-right;
}

.nc-arrow-down-2::before {
  content: $nc-arrow-down-2;
}

.nc-arrow-down-3::before {
  content: $nc-arrow-down-3;
}

.nc-arrow-down::before {
  content: $nc-arrow-down;
}

.nc-arrow-e::before {
  content: $nc-arrow-e;
}

.nc-arrow-left-2::before {
  content: $nc-arrow-left-2;
}

.nc-arrow-left-3::before {
  content: $nc-arrow-left-3;
}

.nc-arrow-left::before {
  content: $nc-arrow-left;
}

.nc-arrow-n::before {
  content: $nc-arrow-n;
}

.nc-arrow-right-2::before {
  content: $nc-arrow-right-2;
}

.nc-arrow-right-3::before {
  content: $nc-arrow-right-3;
}

.nc-arrow-right::before {
  content: $nc-arrow-right;
}

.nc-arrow-s::before {
  content: $nc-arrow-s;
}

.nc-arrow-sm-down::before {
  content: $nc-arrow-sm-down;
}

.nc-arrow-sm-left::before {
  content: $nc-arrow-sm-left;
}

.nc-arrow-sm-right::before {
  content: $nc-arrow-sm-right;
}

.nc-arrow-tool::before {
  content: $nc-arrow-tool;
}

.nc-arrow-top-left::before {
  content: $nc-arrow-top-left;
}

.nc-arrow-top-right::before {
  content: $nc-arrow-top-right;
}

.nc-arrow-up-2::before {
  content: $nc-arrow-up-2;
}

.nc-arrow-up-3::before {
  content: $nc-arrow-up-3;
}

.nc-arrow-up::before {
  content: $nc-arrow-up;
}

.nc-arrow-w::before {
  content: $nc-arrow-w;
}

.nc-arrows-expand-2::before {
  content: $nc-arrows-expand-2;
}

.nc-arrows-expand::before {
  content: $nc-arrows-expand;
}

.nc-arrows-fullscreen-2::before {
  content: $nc-arrows-fullscreen-2;
}

.nc-arrows-fullscreen::before {
  content: $nc-arrows-fullscreen;
}

.nc-arrows-maximize-2::before {
  content: $nc-arrows-maximize-2;
}

.nc-arrows-maximize::before {
  content: $nc-arrows-maximize;
}

.nc-arrows-opposite-directions::before {
  content: $nc-arrows-opposite-directions;
}

.nc-arrows-same-direction::before {
  content: $nc-arrows-same-direction;
}

.nc-artboard::before {
  content: $nc-artboard;
}

.nc-artificial-brain::before {
  content: $nc-artificial-brain;
}

.nc-artificial-intelligence::before {
  content: $nc-artificial-intelligence;
}

.nc-assault-rifle::before {
  content: $nc-assault-rifle;
}

.nc-astronaut::before {
  content: $nc-astronaut;
}

.nc-astronomy::before {
  content: $nc-astronomy;
}

.nc-at-sign-2::before {
  content: $nc-at-sign-2;
}

.nc-at-sign::before {
  content: $nc-at-sign;
}

.nc-athletics::before {
  content: $nc-athletics;
}

.nc-atm::before {
  content: $nc-atm;
}

.nc-atom::before {
  content: $nc-atom;
}

.nc-attach::before {
  content: $nc-attach;
}

.nc-attachment::before {
  content: $nc-attachment;
}

.nc-aubergine::before {
  content: $nc-aubergine;
}

.nc-audio-description::before {
  content: $nc-audio-description;
}

.nc-audio-jack::before {
  content: $nc-audio-jack;
}

.nc-audio-mixer::before {
  content: $nc-audio-mixer;
}

.nc-augmented-reality::before {
  content: $nc-augmented-reality;
}

.nc-auto-flash-2::before {
  content: $nc-auto-flash-2;
}

.nc-auto-flash::before {
  content: $nc-auto-flash;
}

.nc-auto-focus::before {
  content: $nc-auto-focus;
}

.nc-automated-logistics::before {
  content: $nc-automated-logistics;
}

.nc-avocado::before {
  content: $nc-avocado;
}

.nc-award-49::before {
  content: $nc-award-49;
}

.nc-award::before {
  content: $nc-award;
}

.nc-axe::before {
  content: $nc-axe;
}

.nc-b-add::before {
  content: $nc-b-add;
}

.nc-b-chart::before {
  content: $nc-b-chart;
}

.nc-b-check::before {
  content: $nc-b-check;
}

.nc-b-comment::before {
  content: $nc-b-comment;
}

.nc-b-eye::before {
  content: $nc-b-eye;
}

.nc-b-location::before {
  content: $nc-b-location;
}

.nc-b-love::before {
  content: $nc-b-love;
}

.nc-b-meeting::before {
  content: $nc-b-meeting;
}

.nc-b-remove::before {
  content: $nc-b-remove;
}

.nc-b-security::before {
  content: $nc-b-security;
}

.nc-baby-bottle::before {
  content: $nc-baby-bottle;
}

.nc-baby-car-seat::before {
  content: $nc-baby-car-seat;
}

.nc-baby-clothes::before {
  content: $nc-baby-clothes;
}

.nc-baby-monitor::before {
  content: $nc-baby-monitor;
}

.nc-baby-stroller::before {
  content: $nc-baby-stroller;
}

.nc-baby::before {
  content: $nc-baby;
}

.nc-back-arrow::before {
  content: $nc-back-arrow;
}

.nc-background::before {
  content: $nc-background;
}

.nc-backpack-2::before {
  content: $nc-backpack-2;
}

.nc-backpack-57::before {
  content: $nc-backpack-57;
}

.nc-backpack-58::before {
  content: $nc-backpack-58;
}

.nc-backpack::before {
  content: $nc-backpack;
}

.nc-backup::before {
  content: $nc-backup;
}

.nc-backward::before {
  content: $nc-backward;
}

.nc-bacon::before {
  content: $nc-bacon;
}

.nc-badge-13::before {
  content: $nc-badge-13;
}

.nc-badge-14::before {
  content: $nc-badge-14;
}

.nc-badge-15::before {
  content: $nc-badge-15;
}

.nc-badge::before {
  content: $nc-badge;
}

.nc-bag-16::before {
  content: $nc-bag-16;
}

.nc-bag-17::before {
  content: $nc-bag-17;
}

.nc-bag-20::before {
  content: $nc-bag-20;
}

.nc-bag-21::before {
  content: $nc-bag-21;
}

.nc-bag-22::before {
  content: $nc-bag-22;
}

.nc-bag-49::before {
  content: $nc-bag-49;
}

.nc-bag-50::before {
  content: $nc-bag-50;
}

.nc-bag-add-18::before {
  content: $nc-bag-add-18;
}

.nc-bag-add-21::before {
  content: $nc-bag-add-21;
}

.nc-bag-delivery::before {
  content: $nc-bag-delivery;
}

.nc-bag-edit::before {
  content: $nc-bag-edit;
}

.nc-bag-remove-19::before {
  content: $nc-bag-remove-19;
}

.nc-bag-remove-22::before {
  content: $nc-bag-remove-22;
}

.nc-bag-time::before {
  content: $nc-bag-time;
}

.nc-bag::before {
  content: $nc-bag;
}

.nc-baggage-collection::before {
  content: $nc-baggage-collection;
}

.nc-baggage-scale::before {
  content: $nc-baggage-scale;
}

.nc-baguette::before {
  content: $nc-baguette;
}

.nc-bahai::before {
  content: $nc-bahai;
}

.nc-bakery::before {
  content: $nc-bakery;
}

.nc-balance::before {
  content: $nc-balance;
}

.nc-baloon::before {
  content: $nc-baloon;
}

.nc-bamboo::before {
  content: $nc-bamboo;
}

.nc-ban::before {
  content: $nc-ban;
}

.nc-banana::before {
  content: $nc-banana;
}

.nc-bank-statement::before {
  content: $nc-bank-statement;
}

.nc-barbecue-15::before {
  content: $nc-barbecue-15;
}

.nc-barbecue-tools::before {
  content: $nc-barbecue-tools;
}

.nc-barbecue::before {
  content: $nc-barbecue;
}

.nc-barbell::before {
  content: $nc-barbell;
}

.nc-barbershop::before {
  content: $nc-barbershop;
}

.nc-barcode-qr::before {
  content: $nc-barcode-qr;
}

.nc-barcode-scan::before {
  content: $nc-barcode-scan;
}

.nc-barcode::before {
  content: $nc-barcode;
}

.nc-bars-anim-3::before {
  content: $nc-bars-anim-3;
}

.nc-bars-anim::before {
  content: $nc-bars-anim;
}

.nc-baseball-bat::before {
  content: $nc-baseball-bat;
}

.nc-baseball-pitch::before {
  content: $nc-baseball-pitch;
}

.nc-baseball-player::before {
  content: $nc-baseball-player;
}

.nc-baseball::before {
  content: $nc-baseball;
}

.nc-basket-add::before {
  content: $nc-basket-add;
}

.nc-basket-edit::before {
  content: $nc-basket-edit;
}

.nc-basket-favorite::before {
  content: $nc-basket-favorite;
}

.nc-basket-remove::before {
  content: $nc-basket-remove;
}

.nc-basket-search::before {
  content: $nc-basket-search;
}

.nc-basket-share::before {
  content: $nc-basket-share;
}

.nc-basket-simple-add::before {
  content: $nc-basket-simple-add;
}

.nc-basket-simple-remove::before {
  content: $nc-basket-simple-remove;
}

.nc-basket-simple::before {
  content: $nc-basket-simple;
}

.nc-basket-update::before {
  content: $nc-basket-update;
}

.nc-basket::before {
  content: $nc-basket;
}

.nc-basketball-board::before {
  content: $nc-basketball-board;
}

.nc-basketball-player::before {
  content: $nc-basketball-player;
}

.nc-basketball-ring::before {
  content: $nc-basketball-ring;
}

.nc-basketball::before {
  content: $nc-basketball;
}

.nc-bat::before {
  content: $nc-bat;
}

.nc-bath-faucet::before {
  content: $nc-bath-faucet;
}

.nc-bathroom-cabinet::before {
  content: $nc-bathroom-cabinet;
}

.nc-bathtub::before {
  content: $nc-bathtub;
}

.nc-battery-charging::before {
  content: $nc-battery-charging;
}

.nc-battery-level::before {
  content: $nc-battery-level;
}

.nc-battery-low::before {
  content: $nc-battery-low;
}

.nc-battery-power::before {
  content: $nc-battery-power;
}

.nc-battery-status::before {
  content: $nc-battery-status;
}

.nc-battery::before {
  content: $nc-battery;
}

.nc-beach-bat::before {
  content: $nc-beach-bat;
}

.nc-bear-2::before {
  content: $nc-bear-2;
}

.nc-bear::before {
  content: $nc-bear;
}

.nc-beard::before {
  content: $nc-beard;
}

.nc-bed::before {
  content: $nc-bed;
}

.nc-bedroom::before {
  content: $nc-bedroom;
}

.nc-bee::before {
  content: $nc-bee;
}

.nc-beer-95::before {
  content: $nc-beer-95;
}

.nc-beer-96::before {
  content: $nc-beer-96;
}

.nc-bell::before {
  content: $nc-bell;
}

.nc-belt::before {
  content: $nc-belt;
}

.nc-berlin::before {
  content: $nc-berlin;
}

.nc-beverage::before {
  content: $nc-beverage;
}

.nc-bicep::before {
  content: $nc-bicep;
}

.nc-big-eyes::before {
  content: $nc-big-eyes;
}

.nc-big-smile::before {
  content: $nc-big-smile;
}

.nc-bigmouth::before {
  content: $nc-bigmouth;
}

.nc-bike-bmx::before {
  content: $nc-bike-bmx;
}

.nc-bike::before {
  content: $nc-bike;
}

.nc-bikini::before {
  content: $nc-bikini;
}

.nc-bill::before {
  content: $nc-bill;
}

.nc-billboard::before {
  content: $nc-billboard;
}

.nc-billiard-ball::before {
  content: $nc-billiard-ball;
}

.nc-bin::before {
  content: $nc-bin;
}

.nc-binoculars::before {
  content: $nc-binoculars;
}

.nc-biochemistry::before {
  content: $nc-biochemistry;
}

.nc-biology::before {
  content: $nc-biology;
}

.nc-biscuit::before {
  content: $nc-biscuit;
}

.nc-bitcoin::before {
  content: $nc-bitcoin;
}

.nc-bleah::before {
  content: $nc-bleah;
}

.nc-blend::before {
  content: $nc-blend;
}

.nc-blender::before {
  content: $nc-blender;
}

.nc-blindness::before {
  content: $nc-blindness;
}

.nc-block-down::before {
  content: $nc-block-down;
}

.nc-block-left::before {
  content: $nc-block-left;
}

.nc-block-right::before {
  content: $nc-block-right;
}

.nc-block-up::before {
  content: $nc-block-up;
}

.nc-block::before {
  content: $nc-block;
}

.nc-blockchain::before {
  content: $nc-blockchain;
}

.nc-blog::before {
  content: $nc-blog;
}

.nc-blueberries::before {
  content: $nc-blueberries;
}

.nc-blueprint::before {
  content: $nc-blueprint;
}

.nc-bluetooth::before {
  content: $nc-bluetooth;
}

.nc-board-2::before {
  content: $nc-board-2;
}

.nc-board-27::before {
  content: $nc-board-27;
}

.nc-board-28::before {
  content: $nc-board-28;
}

.nc-board-29::before {
  content: $nc-board-29;
}

.nc-board-30::before {
  content: $nc-board-30;
}

.nc-board-51::before {
  content: $nc-board-51;
}

.nc-board-game::before {
  content: $nc-board-game;
}

.nc-board::before {
  content: $nc-board;
}

.nc-boat-front::before {
  content: $nc-boat-front;
}

.nc-boat-small-02::before {
  content: $nc-boat-small-02;
}

.nc-boat-small-03::before {
  content: $nc-boat-small-03;
}

.nc-boat::before {
  content: $nc-boat;
}

.nc-body-back::before {
  content: $nc-body-back;
}

.nc-body-butt::before {
  content: $nc-body-butt;
}

.nc-body-cream::before {
  content: $nc-body-cream;
}

.nc-bodybuilder::before {
  content: $nc-bodybuilder;
}

.nc-boiling-water::before {
  content: $nc-boiling-water;
}

.nc-bold::before {
  content: $nc-bold;
}

.nc-bolt::before {
  content: $nc-bolt;
}

.nc-bomb::before {
  content: $nc-bomb;
}

.nc-bones::before {
  content: $nc-bones;
}

.nc-book-39::before {
  content: $nc-book-39;
}

.nc-book-bookmark-2::before {
  content: $nc-book-bookmark-2;
}

.nc-book-bookmark::before {
  content: $nc-book-bookmark;
}

.nc-book-open-2::before {
  content: $nc-book-open-2;
}

.nc-book-open::before {
  content: $nc-book-open;
}

.nc-book::before {
  content: $nc-book;
}

.nc-bookmark-add-2::before {
  content: $nc-bookmark-add-2;
}

.nc-bookmark-add::before {
  content: $nc-bookmark-add;
}

.nc-bookmark-delete-2::before {
  content: $nc-bookmark-delete-2;
}

.nc-bookmark-delete::before {
  content: $nc-bookmark-delete;
}

.nc-bookmark::before {
  content: $nc-bookmark;
}

.nc-bookmarks::before {
  content: $nc-bookmarks;
}

.nc-books-46::before {
  content: $nc-books-46;
}

.nc-books::before {
  content: $nc-books;
}

.nc-boot-2::before {
  content: $nc-boot-2;
}

.nc-boot-woman::before {
  content: $nc-boot-woman;
}

.nc-boot::before {
  content: $nc-boot;
}

.nc-border-radius::before {
  content: $nc-border-radius;
}

.nc-border::before {
  content: $nc-border;
}

.nc-bored::before {
  content: $nc-bored;
}

.nc-botany::before {
  content: $nc-botany;
}

.nc-bottle-wine::before {
  content: $nc-bottle-wine;
}

.nc-bottle::before {
  content: $nc-bottle;
}

.nc-bouquet::before {
  content: $nc-bouquet;
}

.nc-bow::before {
  content: $nc-bow;
}

.nc-bowl::before {
  content: $nc-bowl;
}

.nc-bowling-ball::before {
  content: $nc-bowling-ball;
}

.nc-bowling-pins::before {
  content: $nc-bowling-pins;
}

.nc-box-2::before {
  content: $nc-box-2;
}

.nc-box-3d-50::before {
  content: $nc-box-3d-50;
}

.nc-box-arrow-bottom-left::before {
  content: $nc-box-arrow-bottom-left;
}

.nc-box-arrow-bottom-right::before {
  content: $nc-box-arrow-bottom-right;
}

.nc-box-arrow-down::before {
  content: $nc-box-arrow-down;
}

.nc-box-arrow-left::before {
  content: $nc-box-arrow-left;
}

.nc-box-arrow-right::before {
  content: $nc-box-arrow-right;
}

.nc-box-arrow-top-left::before {
  content: $nc-box-arrow-top-left;
}

.nc-box-arrow-top-right::before {
  content: $nc-box-arrow-top-right;
}

.nc-box-arrow-up::before {
  content: $nc-box-arrow-up;
}

.nc-box-caret-down::before {
  content: $nc-box-caret-down;
}

.nc-box-caret-left::before {
  content: $nc-box-caret-left;
}

.nc-box-caret-right::before {
  content: $nc-box-caret-right;
}

.nc-box-caret-up::before {
  content: $nc-box-caret-up;
}

.nc-box-ctrl-down::before {
  content: $nc-box-ctrl-down;
}

.nc-box-ctrl-left::before {
  content: $nc-box-ctrl-left;
}

.nc-box-ctrl-right::before {
  content: $nc-box-ctrl-right;
}

.nc-box-ctrl-up::before {
  content: $nc-box-ctrl-up;
}

.nc-box-ribbon::before {
  content: $nc-box-ribbon;
}

.nc-box::before {
  content: $nc-box;
}

.nc-boxing-bag::before {
  content: $nc-boxing-bag;
}

.nc-boxing-glove::before {
  content: $nc-boxing-glove;
}

.nc-boxing::before {
  content: $nc-boxing;
}

.nc-bra::before {
  content: $nc-bra;
}

.nc-braille::before {
  content: $nc-braille;
}

.nc-brain::before {
  content: $nc-brain;
}

.nc-brakes::before {
  content: $nc-brakes;
}

.nc-bread::before {
  content: $nc-bread;
}

.nc-bride::before {
  content: $nc-bride;
}

.nc-briefcase-24::before {
  content: $nc-briefcase-24;
}

.nc-briefcase-25::before {
  content: $nc-briefcase-25;
}

.nc-briefcase-26::before {
  content: $nc-briefcase-26;
}

.nc-brightness::before {
  content: $nc-brightness;
}

.nc-brioche::before {
  content: $nc-brioche;
}

.nc-broccoli::before {
  content: $nc-broccoli;
}

.nc-broken-heart::before {
  content: $nc-broken-heart;
}

.nc-broom::before {
  content: $nc-broom;
}

.nc-browse::before {
  content: $nc-browse;
}

.nc-browser-chrome::before {
  content: $nc-browser-chrome;
}

.nc-browser-edge-legacy::before {
  content: $nc-browser-edge-legacy;
}

.nc-browser-edge::before {
  content: $nc-browser-edge;
}

.nc-browser-firefox::before {
  content: $nc-browser-firefox;
}

.nc-browser-ie::before {
  content: $nc-browser-ie;
}

.nc-browser-opera::before {
  content: $nc-browser-opera;
}

.nc-browser-safari::before {
  content: $nc-browser-safari;
}

.nc-brush::before {
  content: $nc-brush;
}

.nc-btn-play-2::before {
  content: $nc-btn-play-2;
}

.nc-btn-play::before {
  content: $nc-btn-play;
}

.nc-btn-stop::before {
  content: $nc-btn-stop;
}

.nc-bucket::before {
  content: $nc-bucket;
}

.nc-buddhism::before {
  content: $nc-buddhism;
}

.nc-bug::before {
  content: $nc-bug;
}

.nc-bulb-61::before {
  content: $nc-bulb-61;
}

.nc-bulb-62::before {
  content: $nc-bulb-62;
}

.nc-bulb-63::before {
  content: $nc-bulb-63;
}

.nc-bulb-saver::before {
  content: $nc-bulb-saver;
}

.nc-bulb::before {
  content: $nc-bulb;
}

.nc-bullet-list-67::before {
  content: $nc-bullet-list-67;
}

.nc-bullet-list-68::before {
  content: $nc-bullet-list-68;
}

.nc-bullet-list-69::before {
  content: $nc-bullet-list-69;
}

.nc-bullet-list-70::before {
  content: $nc-bullet-list-70;
}

.nc-bullet-list::before {
  content: $nc-bullet-list;
}

.nc-bullets::before {
  content: $nc-bullets;
}

.nc-bureau-dresser::before {
  content: $nc-bureau-dresser;
}

.nc-bus-front-10::before {
  content: $nc-bus-front-10;
}

.nc-bus-front-12::before {
  content: $nc-bus-front-12;
}

.nc-bus::before {
  content: $nc-bus;
}

.nc-business-agent::before {
  content: $nc-business-agent;
}

.nc-business-contact-85::before {
  content: $nc-business-contact-85;
}

.nc-businessman-03::before {
  content: $nc-businessman-03;
}

.nc-businessman-04::before {
  content: $nc-businessman-04;
}

.nc-butter::before {
  content: $nc-butter;
}

.nc-butterfly::before {
  content: $nc-butterfly;
}

.nc-button-2::before {
  content: $nc-button-2;
}

.nc-button-eject::before {
  content: $nc-button-eject;
}

.nc-button-next::before {
  content: $nc-button-next;
}

.nc-button-pause::before {
  content: $nc-button-pause;
}

.nc-button-play::before {
  content: $nc-button-play;
}

.nc-button-power::before {
  content: $nc-button-power;
}

.nc-button-previous::before {
  content: $nc-button-previous;
}

.nc-button-record::before {
  content: $nc-button-record;
}

.nc-button-rewind::before {
  content: $nc-button-rewind;
}

.nc-button-skip::before {
  content: $nc-button-skip;
}

.nc-button-stop::before {
  content: $nc-button-stop;
}

.nc-button::before {
  content: $nc-button;
}

.nc-buzz::before {
  content: $nc-buzz;
}

.nc-c-add::before {
  content: $nc-c-add;
}

.nc-c-check::before {
  content: $nc-c-check;
}

.nc-c-delete::before {
  content: $nc-c-delete;
}

.nc-c-edit::before {
  content: $nc-c-edit;
}

.nc-c-info::before {
  content: $nc-c-info;
}

.nc-c-pulse::before {
  content: $nc-c-pulse;
}

.nc-c-question::before {
  content: $nc-c-question;
}

.nc-c-remove::before {
  content: $nc-c-remove;
}

.nc-c-warning::before {
  content: $nc-c-warning;
}

.nc-cabinet::before {
  content: $nc-cabinet;
}

.nc-cable::before {
  content: $nc-cable;
}

.nc-cactus::before {
  content: $nc-cactus;
}

.nc-cake-13::before {
  content: $nc-cake-13;
}

.nc-cake-2::before {
  content: $nc-cake-2;
}

.nc-cake-slice::before {
  content: $nc-cake-slice;
}

.nc-cake::before {
  content: $nc-cake;
}

.nc-calculator::before {
  content: $nc-calculator;
}

.nc-calendar-2::before {
  content: $nc-calendar-2;
}

.nc-calendar-date-2::before {
  content: $nc-calendar-date-2;
}

.nc-calendar-date::before {
  content: $nc-calendar-date;
}

.nc-calendar-day-view::before {
  content: $nc-calendar-day-view;
}

.nc-calendar-event-2::before {
  content: $nc-calendar-event-2;
}

.nc-calendar-event-create::before {
  content: $nc-calendar-event-create;
}

.nc-calendar-event::before {
  content: $nc-calendar-event;
}

.nc-calendar::before {
  content: $nc-calendar;
}

.nc-call-doctor::before {
  content: $nc-call-doctor;
}

.nc-camcorder::before {
  content: $nc-camcorder;
}

.nc-camera-2::before {
  content: $nc-camera-2;
}

.nc-camera-3::before {
  content: $nc-camera-3;
}

.nc-camera-button::before {
  content: $nc-camera-button;
}

.nc-camera-flash::before {
  content: $nc-camera-flash;
}

.nc-camera-flashlight::before {
  content: $nc-camera-flashlight;
}

.nc-camera-focus-2::before {
  content: $nc-camera-focus-2;
}

.nc-camera-focus::before {
  content: $nc-camera-focus;
}

.nc-camera-lens::before {
  content: $nc-camera-lens;
}

.nc-camera-roll::before {
  content: $nc-camera-roll;
}

.nc-camera-screen::before {
  content: $nc-camera-screen;
}

.nc-camera-shooting::before {
  content: $nc-camera-shooting;
}

.nc-camera-timer::before {
  content: $nc-camera-timer;
}

.nc-camera::before {
  content: $nc-camera;
}

.nc-camper::before {
  content: $nc-camper;
}

.nc-camping::before {
  content: $nc-camping;
}

.nc-can::before {
  content: $nc-can;
}

.nc-candle::before {
  content: $nc-candle;
}

.nc-candlestick-chart::before {
  content: $nc-candlestick-chart;
}

.nc-candy-2::before {
  content: $nc-candy-2;
}

.nc-candy::before {
  content: $nc-candy;
}

.nc-canvas::before {
  content: $nc-canvas;
}

.nc-cap::before {
  content: $nc-cap;
}

.nc-capitalize::before {
  content: $nc-capitalize;
}

.nc-caps-all::before {
  content: $nc-caps-all;
}

.nc-caps-small::before {
  content: $nc-caps-small;
}

.nc-car-2::before {
  content: $nc-car-2;
}

.nc-car-accident::before {
  content: $nc-car-accident;
}

.nc-car-connect::before {
  content: $nc-car-connect;
}

.nc-car-door::before {
  content: $nc-car-door;
}

.nc-car-front::before {
  content: $nc-car-front;
}

.nc-car-lights::before {
  content: $nc-car-lights;
}

.nc-car-parking::before {
  content: $nc-car-parking;
}

.nc-car-simple::before {
  content: $nc-car-simple;
}

.nc-car-sport::before {
  content: $nc-car-sport;
}

.nc-car-ventilation::before {
  content: $nc-car-ventilation;
}

.nc-car-wash::before {
  content: $nc-car-wash;
}

.nc-car::before {
  content: $nc-car;
}

.nc-card-edit::before {
  content: $nc-card-edit;
}

.nc-card-favorite::before {
  content: $nc-card-favorite;
}

.nc-card-remove::before {
  content: $nc-card-remove;
}

.nc-card-update::before {
  content: $nc-card-update;
}

.nc-cards::before {
  content: $nc-cards;
}

.nc-caret-sm-up::before {
  content: $nc-caret-sm-up;
}

.nc-carrot::before {
  content: $nc-carrot;
}

.nc-cart-add-9::before {
  content: $nc-cart-add-9;
}

.nc-cart-add::before {
  content: $nc-cart-add;
}

.nc-cart-favorite::before {
  content: $nc-cart-favorite;
}

.nc-cart-full::before {
  content: $nc-cart-full;
}

.nc-cart-refresh::before {
  content: $nc-cart-refresh;
}

.nc-cart-remove-9::before {
  content: $nc-cart-remove-9;
}

.nc-cart-remove::before {
  content: $nc-cart-remove;
}

.nc-cart-return::before {
  content: $nc-cart-return;
}

.nc-cart-simple-add::before {
  content: $nc-cart-simple-add;
}

.nc-cart-simple-remove::before {
  content: $nc-cart-simple-remove;
}

.nc-cart-speed::before {
  content: $nc-cart-speed;
}

.nc-cart::before {
  content: $nc-cart;
}

.nc-cash-register::before {
  content: $nc-cash-register;
}

.nc-casino-chip::before {
  content: $nc-casino-chip;
}

.nc-casino::before {
  content: $nc-casino;
}

.nc-castle::before {
  content: $nc-castle;
}

.nc-cat::before {
  content: $nc-cat;
}

.nc-catalog::before {
  content: $nc-catalog;
}

.nc-cauldron::before {
  content: $nc-cauldron;
}

.nc-cctv::before {
  content: $nc-cctv;
}

.nc-cd-reader::before {
  content: $nc-cd-reader;
}

.nc-celsius::before {
  content: $nc-celsius;
}

.nc-centralize::before {
  content: $nc-centralize;
}

.nc-certificate::before {
  content: $nc-certificate;
}

.nc-chain::before {
  content: $nc-chain;
}

.nc-chair::before {
  content: $nc-chair;
}

.nc-chalkboard::before {
  content: $nc-chalkboard;
}

.nc-champagne::before {
  content: $nc-champagne;
}

.nc-chandelier::before {
  content: $nc-chandelier;
}

.nc-change-direction::before {
  content: $nc-change-direction;
}

.nc-charger-cable::before {
  content: $nc-charger-cable;
}

.nc-chart-bar-32::before {
  content: $nc-chart-bar-32;
}

.nc-chart-bar-33::before {
  content: $nc-chart-bar-33;
}

.nc-chart-growth::before {
  content: $nc-chart-growth;
}

.nc-chart-pie-35::before {
  content: $nc-chart-pie-35;
}

.nc-chart-pie-36::before {
  content: $nc-chart-pie-36;
}

.nc-chart::before {
  content: $nc-chart;
}

.nc-chat::before {
  content: $nc-chat;
}

.nc-check-all::before {
  content: $nc-check-all;
}

.nc-check-double::before {
  content: $nc-check-double;
}

.nc-check-in::before {
  content: $nc-check-in;
}

.nc-check-list::before {
  content: $nc-check-list;
}

.nc-check-out::before {
  content: $nc-check-out;
}

.nc-check-single::before {
  content: $nc-check-single;
}

.nc-check::before {
  content: $nc-check;
}

.nc-checkbox-btn-checked::before {
  content: $nc-checkbox-btn-checked;
}

.nc-checkbox-btn::before {
  content: $nc-checkbox-btn;
}

.nc-cheese-24::before {
  content: $nc-cheese-24;
}

.nc-cheese-87::before {
  content: $nc-cheese-87;
}

.nc-cheeseburger::before {
  content: $nc-cheeseburger;
}

.nc-chef-hat::before {
  content: $nc-chef-hat;
}

.nc-chef::before {
  content: $nc-chef;
}

.nc-chemistry::before {
  content: $nc-chemistry;
}

.nc-cheque-2::before {
  content: $nc-cheque-2;
}

.nc-cheque-3::before {
  content: $nc-cheque-3;
}

.nc-cheque::before {
  content: $nc-cheque;
}

.nc-chequered-flag::before {
  content: $nc-chequered-flag;
}

.nc-cherry::before {
  content: $nc-cherry;
}

.nc-chess-bishop::before {
  content: $nc-chess-bishop;
}

.nc-chess-king::before {
  content: $nc-chess-king;
}

.nc-chess-knight::before {
  content: $nc-chess-knight;
}

.nc-chess-pawn::before {
  content: $nc-chess-pawn;
}

.nc-chess-queen::before {
  content: $nc-chess-queen;
}

.nc-chess-tower::before {
  content: $nc-chess-tower;
}

.nc-chicken-2::before {
  content: $nc-chicken-2;
}

.nc-chicken::before {
  content: $nc-chicken;
}

.nc-child::before {
  content: $nc-child;
}

.nc-chili::before {
  content: $nc-chili;
}

.nc-chimney::before {
  content: $nc-chimney;
}

.nc-china::before {
  content: $nc-china;
}

.nc-chips::before {
  content: $nc-chips;
}

.nc-choco-cream::before {
  content: $nc-choco-cream;
}

.nc-chocolate-mousse::before {
  content: $nc-chocolate-mousse;
}

.nc-chocolate::before {
  content: $nc-chocolate;
}

.nc-christianity::before {
  content: $nc-christianity;
}

.nc-church::before {
  content: $nc-church;
}

.nc-churros::before {
  content: $nc-churros;
}

.nc-cinema::before {
  content: $nc-cinema;
}

.nc-circle-08::before {
  content: $nc-circle-08;
}

.nc-circle-09::before {
  content: $nc-circle-09;
}

.nc-circle-10::before {
  content: $nc-circle-10;
}

.nc-circle-anim-2::before {
  content: $nc-circle-anim-2;
}

.nc-circle-anim-3::before {
  content: $nc-circle-anim-3;
}

.nc-circle-anim::before {
  content: $nc-circle-anim;
}

.nc-circle-arrow-down::before {
  content: $nc-circle-arrow-down;
}

.nc-circle-arrow-left::before {
  content: $nc-circle-arrow-left;
}

.nc-circle-arrow-right::before {
  content: $nc-circle-arrow-right;
}

.nc-circle-arrow-up::before {
  content: $nc-circle-arrow-up;
}

.nc-circle-caret-down::before {
  content: $nc-circle-caret-down;
}

.nc-circle-caret-left::before {
  content: $nc-circle-caret-left;
}

.nc-circle-caret-right::before {
  content: $nc-circle-caret-right;
}

.nc-circle-caret-up::before {
  content: $nc-circle-caret-up;
}

.nc-circle-ctrl-down::before {
  content: $nc-circle-ctrl-down;
}

.nc-circle-ctrl-left::before {
  content: $nc-circle-ctrl-left;
}

.nc-circle-ctrl-right::before {
  content: $nc-circle-ctrl-right;
}

.nc-circle-ctrl-up::before {
  content: $nc-circle-ctrl-up;
}

.nc-circle-in::before {
  content: $nc-circle-in;
}

.nc-circle-out::before {
  content: $nc-circle-out;
}

.nc-circle::before {
  content: $nc-circle;
}

.nc-circuit-round::before {
  content: $nc-circuit-round;
}

.nc-circuit::before {
  content: $nc-circuit;
}

.nc-clapperboard-2::before {
  content: $nc-clapperboard-2;
}

.nc-clapperboard::before {
  content: $nc-clapperboard;
}

.nc-clarinet::before {
  content: $nc-clarinet;
}

.nc-clear-data::before {
  content: $nc-clear-data;
}

.nc-climbing::before {
  content: $nc-climbing;
}

.nc-clock-anim::before {
  content: $nc-clock-anim;
}

.nc-clock::before {
  content: $nc-clock;
}

.nc-clone::before {
  content: $nc-clone;
}

.nc-closed-captioning::before {
  content: $nc-closed-captioning;
}

.nc-clothes-hanger::before {
  content: $nc-clothes-hanger;
}

.nc-clothing-hanger::before {
  content: $nc-clothing-hanger;
}

.nc-cloud-data-download::before {
  content: $nc-cloud-data-download;
}

.nc-cloud-download::before {
  content: $nc-cloud-download;
}

.nc-cloud-drop::before {
  content: $nc-cloud-drop;
}

.nc-cloud-fog-31::before {
  content: $nc-cloud-fog-31;
}

.nc-cloud-fog-32::before {
  content: $nc-cloud-fog-32;
}

.nc-cloud-forecast::before {
  content: $nc-cloud-forecast;
}

.nc-cloud-hail::before {
  content: $nc-cloud-hail;
}

.nc-cloud-light::before {
  content: $nc-cloud-light;
}

.nc-cloud-mining::before {
  content: $nc-cloud-mining;
}

.nc-cloud-moon::before {
  content: $nc-cloud-moon;
}

.nc-cloud-rain::before {
  content: $nc-cloud-rain;
}

.nc-cloud-rainbow::before {
  content: $nc-cloud-rainbow;
}

.nc-cloud-snow-34::before {
  content: $nc-cloud-snow-34;
}

.nc-cloud-snow-42::before {
  content: $nc-cloud-snow-42;
}

.nc-cloud-sun-17::before {
  content: $nc-cloud-sun-17;
}

.nc-cloud-sun-19::before {
  content: $nc-cloud-sun-19;
}

.nc-cloud-upload::before {
  content: $nc-cloud-upload;
}

.nc-cloud::before {
  content: $nc-cloud;
}

.nc-clover::before {
  content: $nc-clover;
}

.nc-clubs-suit::before {
  content: $nc-clubs-suit;
}

.nc-coat-hanger::before {
  content: $nc-coat-hanger;
}

.nc-coat::before {
  content: $nc-coat;
}

.nc-cockade::before {
  content: $nc-cockade;
}

.nc-cocktail::before {
  content: $nc-cocktail;
}

.nc-code-editor::before {
  content: $nc-code-editor;
}

.nc-code::before {
  content: $nc-code;
}

.nc-coffe-long::before {
  content: $nc-coffe-long;
}

.nc-coffee-bean::before {
  content: $nc-coffee-bean;
}

.nc-coffee-long::before {
  content: $nc-coffee-long;
}

.nc-coffee-maker::before {
  content: $nc-coffee-maker;
}

.nc-coffee::before {
  content: $nc-coffee;
}

.nc-coffin::before {
  content: $nc-coffin;
}

.nc-cogwheel::before {
  content: $nc-cogwheel;
}

.nc-coins::before {
  content: $nc-coins;
}

.nc-collar::before {
  content: $nc-collar;
}

.nc-collection::before {
  content: $nc-collection;
}

.nc-color::before {
  content: $nc-color;
}

.nc-comb::before {
  content: $nc-comb;
}

.nc-command::before {
  content: $nc-command;
}

.nc-comment-add::before {
  content: $nc-comment-add;
}

.nc-comment::before {
  content: $nc-comment;
}

.nc-comments::before {
  content: $nc-comments;
}

.nc-compact-camera::before {
  content: $nc-compact-camera;
}

.nc-compare::before {
  content: $nc-compare;
}

.nc-compass-04::before {
  content: $nc-compass-04;
}

.nc-compass-05::before {
  content: $nc-compass-05;
}

.nc-compass-06::before {
  content: $nc-compass-06;
}

.nc-compass-2::before {
  content: $nc-compass-2;
}

.nc-compass-3::before {
  content: $nc-compass-3;
}

.nc-compass::before {
  content: $nc-compass;
}

.nc-components::before {
  content: $nc-components;
}

.nc-compressed-file::before {
  content: $nc-compressed-file;
}

.nc-computer-monitor::before {
  content: $nc-computer-monitor;
}

.nc-computer-upload::before {
  content: $nc-computer-upload;
}

.nc-computer::before {
  content: $nc-computer;
}

.nc-concierge::before {
  content: $nc-concierge;
}

.nc-condom::before {
  content: $nc-condom;
}

.nc-cone::before {
  content: $nc-cone;
}

.nc-conference-room::before {
  content: $nc-conference-room;
}

.nc-configuration-tools::before {
  content: $nc-configuration-tools;
}

.nc-connect::before {
  content: $nc-connect;
}

.nc-connection::before {
  content: $nc-connection;
}

.nc-construction-sign::before {
  content: $nc-construction-sign;
}

.nc-contact-86::before {
  content: $nc-contact-86;
}

.nc-contact-87::before {
  content: $nc-contact-87;
}

.nc-contact-88::before {
  content: $nc-contact-88;
}

.nc-contact::before {
  content: $nc-contact;
}

.nc-contactless-card::before {
  content: $nc-contactless-card;
}

.nc-contactless::before {
  content: $nc-contactless;
}

.nc-contacts-2::before {
  content: $nc-contacts-2;
}

.nc-contacts-44::before {
  content: $nc-contacts-44;
}

.nc-contacts-45::before {
  content: $nc-contacts-45;
}

.nc-contacts::before {
  content: $nc-contacts;
}

.nc-content-360deg::before {
  content: $nc-content-360deg;
}

.nc-content-delivery::before {
  content: $nc-content-delivery;
}

.nc-contrast-2::before {
  content: $nc-contrast-2;
}

.nc-contrast::before {
  content: $nc-contrast;
}

.nc-control-panel::before {
  content: $nc-control-panel;
}

.nc-controller-2::before {
  content: $nc-controller-2;
}

.nc-controller::before {
  content: $nc-controller;
}

.nc-conversion::before {
  content: $nc-conversion;
}

.nc-cookies::before {
  content: $nc-cookies;
}

.nc-copy-2::before {
  content: $nc-copy-2;
}

.nc-copy::before {
  content: $nc-copy;
}

.nc-copyright::before {
  content: $nc-copyright;
}

.nc-corn::before {
  content: $nc-corn;
}

.nc-corner-bottom-left::before {
  content: $nc-corner-bottom-left;
}

.nc-corner-bottom-right::before {
  content: $nc-corner-bottom-right;
}

.nc-corner-down-round::before {
  content: $nc-corner-down-round;
}

.nc-corner-left-down::before {
  content: $nc-corner-left-down;
}

.nc-corner-left-round::before {
  content: $nc-corner-left-round;
}

.nc-corner-right-down::before {
  content: $nc-corner-right-down;
}

.nc-corner-right-round::before {
  content: $nc-corner-right-round;
}

.nc-corner-top-left::before {
  content: $nc-corner-top-left;
}

.nc-corner-top-right::before {
  content: $nc-corner-top-right;
}

.nc-corner-up-left::before {
  content: $nc-corner-up-left;
}

.nc-corner-up-right::before {
  content: $nc-corner-up-right;
}

.nc-corner-up-round::before {
  content: $nc-corner-up-round;
}

.nc-cornucopia::before {
  content: $nc-cornucopia;
}

.nc-corset::before {
  content: $nc-corset;
}

.nc-coughing::before {
  content: $nc-coughing;
}

.nc-countdown-2::before {
  content: $nc-countdown-2;
}

.nc-countdown::before {
  content: $nc-countdown;
}

.nc-couple-gay::before {
  content: $nc-couple-gay;
}

.nc-couple-lesbian::before {
  content: $nc-couple-lesbian;
}

.nc-coupon::before {
  content: $nc-coupon;
}

.nc-cow::before {
  content: $nc-cow;
}

.nc-cpu::before {
  content: $nc-cpu;
}

.nc-crab::before {
  content: $nc-crab;
}

.nc-cradle::before {
  content: $nc-cradle;
}

.nc-crane::before {
  content: $nc-crane;
}

.nc-creative-commons::before {
  content: $nc-creative-commons;
}

.nc-credit-card-in::before {
  content: $nc-credit-card-in;
}

.nc-credit-card::before {
  content: $nc-credit-card;
}

.nc-credit-locked::before {
  content: $nc-credit-locked;
}

.nc-crepe::before {
  content: $nc-crepe;
}

.nc-cricket-bat::before {
  content: $nc-cricket-bat;
}

.nc-croissant::before {
  content: $nc-croissant;
}

.nc-crop::before {
  content: $nc-crop;
}

.nc-cross-down::before {
  content: $nc-cross-down;
}

.nc-cross-horizontal::before {
  content: $nc-cross-horizontal;
}

.nc-cross-left::before {
  content: $nc-cross-left;
}

.nc-cross-right::before {
  content: $nc-cross-right;
}

.nc-cross-up::before {
  content: $nc-cross-up;
}

.nc-cross-vertical::before {
  content: $nc-cross-vertical;
}

.nc-cross::before {
  content: $nc-cross;
}

.nc-crosshair::before {
  content: $nc-crosshair;
}

.nc-crossing-directions::before {
  content: $nc-crossing-directions;
}

.nc-crossroad::before {
  content: $nc-crossroad;
}

.nc-croupier::before {
  content: $nc-croupier;
}

.nc-crown::before {
  content: $nc-crown;
}

.nc-crumpet::before {
  content: $nc-crumpet;
}

.nc-crunches::before {
  content: $nc-crunches;
}

.nc-cry-15::before {
  content: $nc-cry-15;
}

.nc-cry-57::before {
  content: $nc-cry-57;
}

.nc-crying-baby::before {
  content: $nc-crying-baby;
}

.nc-crypto-wallet::before {
  content: $nc-crypto-wallet;
}

.nc-cryptography::before {
  content: $nc-cryptography;
}

.nc-css3::before {
  content: $nc-css3;
}

.nc-ctrl-backward::before {
  content: $nc-ctrl-backward;
}

.nc-ctrl-down::before {
  content: $nc-ctrl-down;
}

.nc-ctrl-forward::before {
  content: $nc-ctrl-forward;
}

.nc-ctrl-left::before {
  content: $nc-ctrl-left;
}

.nc-ctrl-right::before {
  content: $nc-ctrl-right;
}

.nc-ctrl-up::before {
  content: $nc-ctrl-up;
}

.nc-cubes-anim::before {
  content: $nc-cubes-anim;
}

.nc-cupcake::before {
  content: $nc-cupcake;
}

.nc-cure::before {
  content: $nc-cure;
}

.nc-curling-stone::before {
  content: $nc-curling-stone;
}

.nc-curling::before {
  content: $nc-curling;
}

.nc-currency-dollar::before {
  content: $nc-currency-dollar;
}

.nc-currency-euro::before {
  content: $nc-currency-euro;
}

.nc-currency-exchange-2::before {
  content: $nc-currency-exchange-2;
}

.nc-currency-exchange::before {
  content: $nc-currency-exchange;
}

.nc-currency-pound::before {
  content: $nc-currency-pound;
}

.nc-currency-yen::before {
  content: $nc-currency-yen;
}

.nc-cursor-48::before {
  content: $nc-cursor-48;
}

.nc-cursor-49::before {
  content: $nc-cursor-49;
}

.nc-cursor-add::before {
  content: $nc-cursor-add;
}

.nc-cursor-grab::before {
  content: $nc-cursor-grab;
}

.nc-cursor-load::before {
  content: $nc-cursor-load;
}

.nc-cursor-menu::before {
  content: $nc-cursor-menu;
}

.nc-cursor-not-allowed::before {
  content: $nc-cursor-not-allowed;
}

.nc-cursor-pointer::before {
  content: $nc-cursor-pointer;
}

.nc-cursor-text::before {
  content: $nc-cursor-text;
}

.nc-curtains::before {
  content: $nc-curtains;
}

.nc-curved-arrow-down::before {
  content: $nc-curved-arrow-down;
}

.nc-curved-arrow-left::before {
  content: $nc-curved-arrow-left;
}

.nc-curved-arrow-right::before {
  content: $nc-curved-arrow-right;
}

.nc-curved-circuit::before {
  content: $nc-curved-circuit;
}

.nc-customer-support::before {
  content: $nc-customer-support;
}

.nc-cut::before {
  content: $nc-cut;
}

.nc-cute::before {
  content: $nc-cute;
}

.nc-cutlery-75::before {
  content: $nc-cutlery-75;
}

.nc-cutlery-76::before {
  content: $nc-cutlery-76;
}

.nc-cutlery-77::before {
  content: $nc-cutlery-77;
}

.nc-cutlery::before {
  content: $nc-cutlery;
}

.nc-cyborg::before {
  content: $nc-cyborg;
}

.nc-cycle::before {
  content: $nc-cycle;
}

.nc-cycling-track::before {
  content: $nc-cycling-track;
}

.nc-cycling::before {
  content: $nc-cycling;
}

.nc-d-add::before {
  content: $nc-d-add;
}

.nc-d-chart::before {
  content: $nc-d-chart;
}

.nc-d-check::before {
  content: $nc-d-check;
}

.nc-d-delete::before {
  content: $nc-d-delete;
}

.nc-d-edit::before {
  content: $nc-d-edit;
}

.nc-d-remove::before {
  content: $nc-d-remove;
}

.nc-dancer::before {
  content: $nc-dancer;
}

.nc-dart::before {
  content: $nc-dart;
}

.nc-dashboard::before {
  content: $nc-dashboard;
}

.nc-data-download::before {
  content: $nc-data-download;
}

.nc-data-settings::before {
  content: $nc-data-settings;
}

.nc-data-table::before {
  content: $nc-data-table;
}

.nc-data-upload::before {
  content: $nc-data-upload;
}

.nc-database::before {
  content: $nc-database;
}

.nc-dead-hand::before {
  content: $nc-dead-hand;
}

.nc-deadlift::before {
  content: $nc-deadlift;
}

.nc-deaf::before {
  content: $nc-deaf;
}

.nc-debt::before {
  content: $nc-debt;
}

.nc-decentralize::before {
  content: $nc-decentralize;
}

.nc-decision-process::before {
  content: $nc-decision-process;
}

.nc-decoration::before {
  content: $nc-decoration;
}

.nc-decrease-font-size::before {
  content: $nc-decrease-font-size;
}

.nc-deer::before {
  content: $nc-deer;
}

.nc-delete-28::before {
  content: $nc-delete-28;
}

.nc-delete-30::before {
  content: $nc-delete-30;
}

.nc-delete-forever::before {
  content: $nc-delete-forever;
}

.nc-delete-key::before {
  content: $nc-delete-key;
}

.nc-delete-x::before {
  content: $nc-delete-x;
}

.nc-delete::before {
  content: $nc-delete;
}

.nc-delivery-2::before {
  content: $nc-delivery-2;
}

.nc-delivery-3::before {
  content: $nc-delivery-3;
}

.nc-delivery-fast::before {
  content: $nc-delivery-fast;
}

.nc-delivery-time::before {
  content: $nc-delivery-time;
}

.nc-delivery-track::before {
  content: $nc-delivery-track;
}

.nc-delivery::before {
  content: $nc-delivery;
}

.nc-design-system::before {
  content: $nc-design-system;
}

.nc-design::before {
  content: $nc-design;
}

.nc-desk-drawer::before {
  content: $nc-desk-drawer;
}

.nc-desk-lamp::before {
  content: $nc-desk-lamp;
}

.nc-desk::before {
  content: $nc-desk;
}

.nc-detached-property::before {
  content: $nc-detached-property;
}

.nc-detox::before {
  content: $nc-detox;
}

.nc-device-connection::before {
  content: $nc-device-connection;
}

.nc-devil::before {
  content: $nc-devil;
}

.nc-diamond::before {
  content: $nc-diamond;
}

.nc-diamonds-suits::before {
  content: $nc-diamonds-suits;
}

.nc-diaper-changing-area::before {
  content: $nc-diaper-changing-area;
}

.nc-diaper::before {
  content: $nc-diaper;
}

.nc-dice-2::before {
  content: $nc-dice-2;
}

.nc-dice::before {
  content: $nc-dice;
}

.nc-diet-food::before {
  content: $nc-diet-food;
}

.nc-diet-plan::before {
  content: $nc-diet-plan;
}

.nc-diet::before {
  content: $nc-diet;
}

.nc-digital-key::before {
  content: $nc-digital-key;
}

.nc-digital-piano::before {
  content: $nc-digital-piano;
}

.nc-direction-down::before {
  content: $nc-direction-down;
}

.nc-direction-left::before {
  content: $nc-direction-left;
}

.nc-direction-right::before {
  content: $nc-direction-right;
}

.nc-direction-up::before {
  content: $nc-direction-up;
}

.nc-direction::before {
  content: $nc-direction;
}

.nc-directions::before {
  content: $nc-directions;
}

.nc-discount-2::before {
  content: $nc-discount-2;
}

.nc-disgusted::before {
  content: $nc-disgusted;
}

.nc-dish::before {
  content: $nc-dish;
}

.nc-dishwasher::before {
  content: $nc-dishwasher;
}

.nc-disinfectant::before {
  content: $nc-disinfectant;
}

.nc-disk-reader::before {
  content: $nc-disk-reader;
}

.nc-disk::before {
  content: $nc-disk;
}

.nc-disperse::before {
  content: $nc-disperse;
}

.nc-distance::before {
  content: $nc-distance;
}

.nc-distribute-horizontal::before {
  content: $nc-distribute-horizontal;
}

.nc-distribute-vertical::before {
  content: $nc-distribute-vertical;
}

.nc-divider::before {
  content: $nc-divider;
}

.nc-dizzy-face::before {
  content: $nc-dizzy-face;
}

.nc-dna-27::before {
  content: $nc-dna-27;
}

.nc-dna-38::before {
  content: $nc-dna-38;
}

.nc-doc-folder::before {
  content: $nc-doc-folder;
}

.nc-dock-bottom::before {
  content: $nc-dock-bottom;
}

.nc-dock-left::before {
  content: $nc-dock-left;
}

.nc-dock-right::before {
  content: $nc-dock-right;
}

.nc-dock-top::before {
  content: $nc-dock-top;
}

.nc-doctor::before {
  content: $nc-doctor;
}

.nc-document-2::before {
  content: $nc-document-2;
}

.nc-document-copy::before {
  content: $nc-document-copy;
}

.nc-document::before {
  content: $nc-document;
}

.nc-dog-house::before {
  content: $nc-dog-house;
}

.nc-dog-leash::before {
  content: $nc-dog-leash;
}

.nc-dog::before {
  content: $nc-dog;
}

.nc-dont-touch-eyes::before {
  content: $nc-dont-touch-eyes;
}

.nc-dont-touch-mouth::before {
  content: $nc-dont-touch-mouth;
}

.nc-donut::before {
  content: $nc-donut;
}

.nc-door-2::before {
  content: $nc-door-2;
}

.nc-door-3::before {
  content: $nc-door-3;
}

.nc-door-handle::before {
  content: $nc-door-handle;
}

.nc-door::before {
  content: $nc-door;
}

.nc-doorphone::before {
  content: $nc-doorphone;
}

.nc-dots-anim-2::before {
  content: $nc-dots-anim-2;
}

.nc-dots-anim-3::before {
  content: $nc-dots-anim-3;
}

.nc-dots-anim-4::before {
  content: $nc-dots-anim-4;
}

.nc-dots-anim-5::before {
  content: $nc-dots-anim-5;
}

.nc-dots-anim-6::before {
  content: $nc-dots-anim-6;
}

.nc-dots-anim-7::before {
  content: $nc-dots-anim-7;
}

.nc-dots-anim::before {
  content: $nc-dots-anim;
}

.nc-dots::before {
  content: $nc-dots;
}

.nc-double-arrow-left::before {
  content: $nc-double-arrow-left;
}

.nc-double-arrow-right::before {
  content: $nc-double-arrow-right;
}

.nc-double-bed::before {
  content: $nc-double-bed;
}

.nc-double-tap::before {
  content: $nc-double-tap;
}

.nc-down-arrow::before {
  content: $nc-down-arrow;
}

.nc-download-data::before {
  content: $nc-download-data;
}

.nc-download-file::before {
  content: $nc-download-file;
}

.nc-download::before {
  content: $nc-download;
}

.nc-drag-21::before {
  content: $nc-drag-21;
}

.nc-drag-31::before {
  content: $nc-drag-31;
}

.nc-drag-down::before {
  content: $nc-drag-down;
}

.nc-drag-left::before {
  content: $nc-drag-left;
}

.nc-drag-right::before {
  content: $nc-drag-right;
}

.nc-drag-up::before {
  content: $nc-drag-up;
}

.nc-drag::before {
  content: $nc-drag;
}

.nc-drawer-2::before {
  content: $nc-drawer-2;
}

.nc-drawer::before {
  content: $nc-drawer;
}

.nc-dress-man::before {
  content: $nc-dress-man;
}

.nc-dress-woman::before {
  content: $nc-dress-woman;
}

.nc-dresser-2::before {
  content: $nc-dresser-2;
}

.nc-dresser-3::before {
  content: $nc-dresser-3;
}

.nc-dresser::before {
  content: $nc-dresser;
}

.nc-drill::before {
  content: $nc-drill;
}

.nc-drink-2::before {
  content: $nc-drink-2;
}

.nc-drink-list::before {
  content: $nc-drink-list;
}

.nc-drink::before {
  content: $nc-drink;
}

.nc-drinking-bottle::before {
  content: $nc-drinking-bottle;
}

.nc-drone-2::before {
  content: $nc-drone-2;
}

.nc-drone::before {
  content: $nc-drone;
}

.nc-drop-15::before {
  content: $nc-drop-15;
}

.nc-drop::before {
  content: $nc-drop;
}

.nc-drops::before {
  content: $nc-drops;
}

.nc-druidism::before {
  content: $nc-druidism;
}

.nc-drums::before {
  content: $nc-drums;
}

.nc-duck::before {
  content: $nc-duck;
}

.nc-dumbbell::before {
  content: $nc-dumbbell;
}

.nc-duplicate::before {
  content: $nc-duplicate;
}

.nc-e-add::before {
  content: $nc-e-add;
}

.nc-e-delete::before {
  content: $nc-e-delete;
}

.nc-e-reader::before {
  content: $nc-e-reader;
}

.nc-e-remove::before {
  content: $nc-e-remove;
}

.nc-earbuds::before {
  content: $nc-earbuds;
}

.nc-earth-science::before {
  content: $nc-earth-science;
}

.nc-eclipse::before {
  content: $nc-eclipse;
}

.nc-eco-home::before {
  content: $nc-eco-home;
}

.nc-ecology::before {
  content: $nc-ecology;
}

.nc-edge-razor::before {
  content: $nc-edge-razor;
}

.nc-edit-2::before {
  content: $nc-edit-2;
}

.nc-edit-brightness::before {
  content: $nc-edit-brightness;
}

.nc-edit-color::before {
  content: $nc-edit-color;
}

.nc-edit-contrast::before {
  content: $nc-edit-contrast;
}

.nc-edit-curves::before {
  content: $nc-edit-curves;
}

.nc-edit-levels::before {
  content: $nc-edit-levels;
}

.nc-edit-note::before {
  content: $nc-edit-note;
}

.nc-edit-saturation::before {
  content: $nc-edit-saturation;
}

.nc-edit::before {
  content: $nc-edit;
}

.nc-egg-38::before {
  content: $nc-egg-38;
}

.nc-egg-39::before {
  content: $nc-egg-39;
}

.nc-egg::before {
  content: $nc-egg;
}

.nc-eggs::before {
  content: $nc-eggs;
}

.nc-eight::before {
  content: $nc-eight;
}

.nc-eject::before {
  content: $nc-eject;
}

.nc-electronic-circuit::before {
  content: $nc-electronic-circuit;
}

.nc-elephant::before {
  content: $nc-elephant;
}

.nc-elliptical-cross-trainer::before {
  content: $nc-elliptical-cross-trainer;
}

.nc-email::before {
  content: $nc-email;
}

.nc-embryo::before {
  content: $nc-embryo;
}

.nc-empty::before {
  content: $nc-empty;
}

.nc-energy-drink::before {
  content: $nc-energy-drink;
}

.nc-energy-shaker::before {
  content: $nc-energy-shaker;
}

.nc-energy-supplement::before {
  content: $nc-energy-supplement;
}

.nc-energy::before {
  content: $nc-energy;
}

.nc-engine-start::before {
  content: $nc-engine-start;
}

.nc-engine::before {
  content: $nc-engine;
}

.nc-enlarge-diagonal-2::before {
  content: $nc-enlarge-diagonal-2;
}

.nc-enlarge-diagonal::before {
  content: $nc-enlarge-diagonal;
}

.nc-enlarge-h::before {
  content: $nc-enlarge-h;
}

.nc-enlarge-horizontal::before {
  content: $nc-enlarge-horizontal;
}

.nc-enlarge-vertical::before {
  content: $nc-enlarge-vertical;
}

.nc-enlarge::before {
  content: $nc-enlarge;
}

.nc-enter::before {
  content: $nc-enter;
}

.nc-equestrian-helmet::before {
  content: $nc-equestrian-helmet;
}

.nc-eraser-32::before {
  content: $nc-eraser-32;
}

.nc-eraser-33::before {
  content: $nc-eraser-33;
}

.nc-eraser-46::before {
  content: $nc-eraser-46;
}

.nc-escalator::before {
  content: $nc-escalator;
}

.nc-event-confirm::before {
  content: $nc-event-confirm;
}

.nc-event-create::before {
  content: $nc-event-create;
}

.nc-event-ticket::before {
  content: $nc-event-ticket;
}

.nc-exchange::before {
  content: $nc-exchange;
}

.nc-exclamation-mark::before {
  content: $nc-exclamation-mark;
}

.nc-exercise-bike::before {
  content: $nc-exercise-bike;
}

.nc-exhibition::before {
  content: $nc-exhibition;
}

.nc-exit-right::before {
  content: $nc-exit-right;
}

.nc-expand-h::before {
  content: $nc-expand-h;
}

.nc-expand-window::before {
  content: $nc-expand-window;
}

.nc-expand::before {
  content: $nc-expand;
}

.nc-explore-2::before {
  content: $nc-explore-2;
}

.nc-explore-user::before {
  content: $nc-explore-user;
}

.nc-explore::before {
  content: $nc-explore;
}

.nc-export::before {
  content: $nc-export;
}

.nc-eye-recognition::before {
  content: $nc-eye-recognition;
}

.nc-eye::before {
  content: $nc-eye;
}

.nc-eyelash::before {
  content: $nc-eyelash;
}

.nc-eyeliner::before {
  content: $nc-eyeliner;
}

.nc-eyeshadow::before {
  content: $nc-eyeshadow;
}

.nc-ez-bar::before {
  content: $nc-ez-bar;
}

.nc-f-add::before {
  content: $nc-f-add;
}

.nc-f-chat::before {
  content: $nc-f-chat;
}

.nc-f-check::before {
  content: $nc-f-check;
}

.nc-f-comment::before {
  content: $nc-f-comment;
}

.nc-f-dashboard::before {
  content: $nc-f-dashboard;
}

.nc-f-delete::before {
  content: $nc-f-delete;
}

.nc-f-remove::before {
  content: $nc-f-remove;
}

.nc-face-man::before {
  content: $nc-face-man;
}

.nc-face-powder::before {
  content: $nc-face-powder;
}

.nc-face-recognition::before {
  content: $nc-face-recognition;
}

.nc-face-woman::before {
  content: $nc-face-woman;
}

.nc-factory::before {
  content: $nc-factory;
}

.nc-fahrenheit::before {
  content: $nc-fahrenheit;
}

.nc-family-roof::before {
  content: $nc-family-roof;
}

.nc-family::before {
  content: $nc-family;
}

.nc-fan::before {
  content: $nc-fan;
}

.nc-fav-list::before {
  content: $nc-fav-list;
}

.nc-fav-property::before {
  content: $nc-fav-property;
}

.nc-fav-remove::before {
  content: $nc-fav-remove;
}

.nc-favorite::before {
  content: $nc-favorite;
}

.nc-feedback::before {
  content: $nc-feedback;
}

.nc-feeding-bottle::before {
  content: $nc-feeding-bottle;
}

.nc-female::before {
  content: $nc-female;
}

.nc-fence::before {
  content: $nc-fence;
}

.nc-fencing-swords::before {
  content: $nc-fencing-swords;
}

.nc-fencing::before {
  content: $nc-fencing;
}

.nc-file-2::before {
  content: $nc-file-2;
}

.nc-file-add::before {
  content: $nc-file-add;
}

.nc-file-alert::before {
  content: $nc-file-alert;
}

.nc-file-archive::before {
  content: $nc-file-archive;
}

.nc-file-article::before {
  content: $nc-file-article;
}

.nc-file-audio-2::before {
  content: $nc-file-audio-2;
}

.nc-file-audio::before {
  content: $nc-file-audio;
}

.nc-file-bookmark::before {
  content: $nc-file-bookmark;
}

.nc-file-chart-bar::before {
  content: $nc-file-chart-bar;
}

.nc-file-chart-pie::before {
  content: $nc-file-chart-pie;
}

.nc-file-check::before {
  content: $nc-file-check;
}

.nc-file-cloud::before {
  content: $nc-file-cloud;
}

.nc-file-copies::before {
  content: $nc-file-copies;
}

.nc-file-copy::before {
  content: $nc-file-copy;
}

.nc-file-delete::before {
  content: $nc-file-delete;
}

.nc-file-dev::before {
  content: $nc-file-dev;
}

.nc-file-download-3::before {
  content: $nc-file-download-3;
}

.nc-file-download::before {
  content: $nc-file-download;
}

.nc-file-edit::before {
  content: $nc-file-edit;
}

.nc-file-export::before {
  content: $nc-file-export;
}

.nc-file-favorite::before {
  content: $nc-file-favorite;
}

.nc-file-folder::before {
  content: $nc-file-folder;
}

.nc-file-gallery::before {
  content: $nc-file-gallery;
}

.nc-file-history::before {
  content: $nc-file-history;
}

.nc-file-image::before {
  content: $nc-file-image;
}

.nc-file-import::before {
  content: $nc-file-import;
}

.nc-file-info::before {
  content: $nc-file-info;
}

.nc-file-link::before {
  content: $nc-file-link;
}

.nc-file-locked::before {
  content: $nc-file-locked;
}

.nc-file-money::before {
  content: $nc-file-money;
}

.nc-file-new::before {
  content: $nc-file-new;
}

.nc-file-no-access::before {
  content: $nc-file-no-access;
}

.nc-file-play::before {
  content: $nc-file-play;
}

.nc-file-preferences::before {
  content: $nc-file-preferences;
}

.nc-file-question::before {
  content: $nc-file-question;
}

.nc-file-remove::before {
  content: $nc-file-remove;
}

.nc-file-replace::before {
  content: $nc-file-replace;
}

.nc-file-search::before {
  content: $nc-file-search;
}

.nc-file-settings::before {
  content: $nc-file-settings;
}

.nc-file-shared::before {
  content: $nc-file-shared;
}

.nc-file-starred::before {
  content: $nc-file-starred;
}

.nc-file-sync::before {
  content: $nc-file-sync;
}

.nc-file-text::before {
  content: $nc-file-text;
}

.nc-file-upload-2::before {
  content: $nc-file-upload-2;
}

.nc-file-upload-3::before {
  content: $nc-file-upload-3;
}

.nc-file-upload::before {
  content: $nc-file-upload;
}

.nc-file-user::before {
  content: $nc-file-user;
}

.nc-file-vector::before {
  content: $nc-file-vector;
}

.nc-file::before {
  content: $nc-file;
}

.nc-film::before {
  content: $nc-film;
}

.nc-filter-check::before {
  content: $nc-filter-check;
}

.nc-filter-organization::before {
  content: $nc-filter-organization;
}

.nc-filter-remove::before {
  content: $nc-filter-remove;
}

.nc-filter-tool::before {
  content: $nc-filter-tool;
}

.nc-filter::before {
  content: $nc-filter;
}

.nc-final-score::before {
  content: $nc-final-score;
}

.nc-find-baggage::before {
  content: $nc-find-baggage;
}

.nc-find-replace::before {
  content: $nc-find-replace;
}

.nc-finger-snap::before {
  content: $nc-finger-snap;
}

.nc-fire::before {
  content: $nc-fire;
}

.nc-firearm::before {
  content: $nc-firearm;
}

.nc-fireplace::before {
  content: $nc-fireplace;
}

.nc-firewall::before {
  content: $nc-firewall;
}

.nc-fireworks::before {
  content: $nc-fireworks;
}

.nc-fish::before {
  content: $nc-fish;
}

.nc-fishbone::before {
  content: $nc-fishbone;
}

.nc-fist::before {
  content: $nc-fist;
}

.nc-fit-horizontal::before {
  content: $nc-fit-horizontal;
}

.nc-fit-vertical::before {
  content: $nc-fit-vertical;
}

.nc-five::before {
  content: $nc-five;
}

.nc-flag-complex::before {
  content: $nc-flag-complex;
}

.nc-flag-diagonal-33::before {
  content: $nc-flag-diagonal-33;
}

.nc-flag-diagonal-34::before {
  content: $nc-flag-diagonal-34;
}

.nc-flag-points-31::before {
  content: $nc-flag-points-31;
}

.nc-flag-points-32::before {
  content: $nc-flag-points-32;
}

.nc-flag-simple::before {
  content: $nc-flag-simple;
}

.nc-flag::before {
  content: $nc-flag;
}

.nc-flame::before {
  content: $nc-flame;
}

.nc-flash-off-2::before {
  content: $nc-flash-off-2;
}

.nc-flash-off::before {
  content: $nc-flash-off;
}

.nc-flashlight::before {
  content: $nc-flashlight;
}

.nc-flask-2::before {
  content: $nc-flask-2;
}

.nc-flask::before {
  content: $nc-flask;
}

.nc-flick-down::before {
  content: $nc-flick-down;
}

.nc-flick-left::before {
  content: $nc-flick-left;
}

.nc-flick-right::before {
  content: $nc-flick-right;
}

.nc-flick-up::before {
  content: $nc-flick-up;
}

.nc-flight-connection::before {
  content: $nc-flight-connection;
}

.nc-flight::before {
  content: $nc-flight;
}

.nc-flip-horizontal::before {
  content: $nc-flip-horizontal;
}

.nc-flip-up::before {
  content: $nc-flip-up;
}

.nc-flip-vertical::before {
  content: $nc-flip-vertical;
}

.nc-flip::before {
  content: $nc-flip;
}

.nc-floor-lamp::before {
  content: $nc-floor-lamp;
}

.nc-floor::before {
  content: $nc-floor;
}

.nc-floors::before {
  content: $nc-floors;
}

.nc-floppy-disk::before {
  content: $nc-floppy-disk;
}

.nc-flower-05::before {
  content: $nc-flower-05;
}

.nc-flower-06::before {
  content: $nc-flower-06;
}

.nc-flower-07::before {
  content: $nc-flower-07;
}

.nc-flower-rose::before {
  content: $nc-flower-rose;
}

.nc-focus::before {
  content: $nc-focus;
}

.nc-fog::before {
  content: $nc-fog;
}

.nc-folder-2::before {
  content: $nc-folder-2;
}

.nc-folder-3::before {
  content: $nc-folder-3;
}

.nc-folder-add::before {
  content: $nc-folder-add;
}

.nc-folder-alert::before {
  content: $nc-folder-alert;
}

.nc-folder-audio::before {
  content: $nc-folder-audio;
}

.nc-folder-bookmark::before {
  content: $nc-folder-bookmark;
}

.nc-folder-chart-bar::before {
  content: $nc-folder-chart-bar;
}

.nc-folder-chart-pie::before {
  content: $nc-folder-chart-pie;
}

.nc-folder-check::before {
  content: $nc-folder-check;
}

.nc-folder-cloud::before {
  content: $nc-folder-cloud;
}

.nc-folder-dev::before {
  content: $nc-folder-dev;
}

.nc-folder-download::before {
  content: $nc-folder-download;
}

.nc-folder-edit::before {
  content: $nc-folder-edit;
}

.nc-folder-favorite::before {
  content: $nc-folder-favorite;
}

.nc-folder-gallery::before {
  content: $nc-folder-gallery;
}

.nc-folder-history::before {
  content: $nc-folder-history;
}

.nc-folder-image::before {
  content: $nc-folder-image;
}

.nc-folder-info::before {
  content: $nc-folder-info;
}

.nc-folder-link::before {
  content: $nc-folder-link;
}

.nc-folder-locked::before {
  content: $nc-folder-locked;
}

.nc-folder-money::before {
  content: $nc-folder-money;
}

.nc-folder-music::before {
  content: $nc-folder-music;
}

.nc-folder-no-access::before {
  content: $nc-folder-no-access;
}

.nc-folder-play::before {
  content: $nc-folder-play;
}

.nc-folder-preferences::before {
  content: $nc-folder-preferences;
}

.nc-folder-question::before {
  content: $nc-folder-question;
}

.nc-folder-remove::before {
  content: $nc-folder-remove;
}

.nc-folder-replace::before {
  content: $nc-folder-replace;
}

.nc-folder-search::before {
  content: $nc-folder-search;
}

.nc-folder-settings::before {
  content: $nc-folder-settings;
}

.nc-folder-shared::before {
  content: $nc-folder-shared;
}

.nc-folder-starred::before {
  content: $nc-folder-starred;
}

.nc-folder-sync::before {
  content: $nc-folder-sync;
}

.nc-folder-upload::before {
  content: $nc-folder-upload;
}

.nc-folder-user::before {
  content: $nc-folder-user;
}

.nc-folder-vector::before {
  content: $nc-folder-vector;
}

.nc-folder::before {
  content: $nc-folder;
}

.nc-food-course::before {
  content: $nc-food-course;
}

.nc-food-dog::before {
  content: $nc-food-dog;
}

.nc-food-scale::before {
  content: $nc-food-scale;
}

.nc-food-supplement::before {
  content: $nc-food-supplement;
}

.nc-football-headguard::before {
  content: $nc-football-headguard;
}

.nc-forecast::before {
  content: $nc-forecast;
}

.nc-forest::before {
  content: $nc-forest;
}

.nc-fork-2::before {
  content: $nc-fork-2;
}

.nc-fork::before {
  content: $nc-fork;
}

.nc-form::before {
  content: $nc-form;
}

.nc-format-left::before {
  content: $nc-format-left;
}

.nc-format-right::before {
  content: $nc-format-right;
}

.nc-forward::before {
  content: $nc-forward;
}

.nc-four::before {
  content: $nc-four;
}

.nc-frame-effect::before {
  content: $nc-frame-effect;
}

.nc-frame::before {
  content: $nc-frame;
}

.nc-frankenstein::before {
  content: $nc-frankenstein;
}

.nc-fridge::before {
  content: $nc-fridge;
}

.nc-fuel-2::before {
  content: $nc-fuel-2;
}

.nc-fuel-electric::before {
  content: $nc-fuel-electric;
}

.nc-fuel::before {
  content: $nc-fuel;
}

.nc-full-screen::before {
  content: $nc-full-screen;
}

.nc-fullscreen-2::before {
  content: $nc-fullscreen-2;
}

.nc-fullscreen::before {
  content: $nc-fullscreen;
}

.nc-fullsize::before {
  content: $nc-fullsize;
}

.nc-funnel::before {
  content: $nc-funnel;
}

.nc-furnished-property::before {
  content: $nc-furnished-property;
}

.nc-g-chart::before {
  content: $nc-g-chart;
}

.nc-g-check::before {
  content: $nc-g-check;
}

.nc-gallery-layout::before {
  content: $nc-gallery-layout;
}

.nc-gallery-view::before {
  content: $nc-gallery-view;
}

.nc-gaming-console::before {
  content: $nc-gaming-console;
}

.nc-gaming-controller::before {
  content: $nc-gaming-controller;
}

.nc-gantt::before {
  content: $nc-gantt;
}

.nc-garlic::before {
  content: $nc-garlic;
}

.nc-gas-mask::before {
  content: $nc-gas-mask;
}

.nc-gathering-restrictions::before {
  content: $nc-gathering-restrictions;
}

.nc-gear::before {
  content: $nc-gear;
}

.nc-geometry::before {
  content: $nc-geometry;
}

.nc-ghost-2::before {
  content: $nc-ghost-2;
}

.nc-ghost::before {
  content: $nc-ghost;
}

.nc-gift-exchange::before {
  content: $nc-gift-exchange;
}

.nc-gift::before {
  content: $nc-gift;
}

.nc-git-commit::before {
  content: $nc-git-commit;
}

.nc-git-merge::before {
  content: $nc-git-merge;
}

.nc-glass-water::before {
  content: $nc-glass-water;
}

.nc-glass::before {
  content: $nc-glass;
}

.nc-glasses-2::before {
  content: $nc-glasses-2;
}

.nc-glasses::before {
  content: $nc-glasses;
}

.nc-globe-2::before {
  content: $nc-globe-2;
}

.nc-globe::before {
  content: $nc-globe;
}

.nc-glove::before {
  content: $nc-glove;
}

.nc-gloves::before {
  content: $nc-gloves;
}

.nc-goal-65::before {
  content: $nc-goal-65;
}

.nc-gold-coin::before {
  content: $nc-gold-coin;
}

.nc-gold::before {
  content: $nc-gold;
}

.nc-golf-ball::before {
  content: $nc-golf-ball;
}

.nc-golf-club::before {
  content: $nc-golf-club;
}

.nc-golf-course::before {
  content: $nc-golf-course;
}

.nc-golf-player::before {
  content: $nc-golf-player;
}

.nc-golf-strike::before {
  content: $nc-golf-strike;
}

.nc-gps::before {
  content: $nc-gps;
}

.nc-grab::before {
  content: $nc-grab;
}

.nc-gradient::before {
  content: $nc-gradient;
}

.nc-grain-effect::before {
  content: $nc-grain-effect;
}

.nc-grain::before {
  content: $nc-grain;
}

.nc-grammar-check::before {
  content: $nc-grammar-check;
}

.nc-grandparent::before {
  content: $nc-grandparent;
}

.nc-grape::before {
  content: $nc-grape;
}

.nc-graphics-tablet::before {
  content: $nc-graphics-tablet;
}

.nc-grave::before {
  content: $nc-grave;
}

.nc-grenade::before {
  content: $nc-grenade;
}

.nc-grid-interface::before {
  content: $nc-grid-interface;
}

.nc-grid-layout::before {
  content: $nc-grid-layout;
}

.nc-grid-system::before {
  content: $nc-grid-system;
}

.nc-grid-view::before {
  content: $nc-grid-view;
}

.nc-grid::before {
  content: $nc-grid;
}

.nc-groom::before {
  content: $nc-groom;
}

.nc-group::before {
  content: $nc-group;
}

.nc-guitar::before {
  content: $nc-guitar;
}

.nc-gym-class::before {
  content: $nc-gym-class;
}

.nc-gym-shoes::before {
  content: $nc-gym-shoes;
}

.nc-gym::before {
  content: $nc-gym;
}

.nc-gymnastics::before {
  content: $nc-gymnastics;
}

.nc-hacker::before {
  content: $nc-hacker;
}

.nc-hair-clipper::before {
  content: $nc-hair-clipper;
}

.nc-hair-dryer::before {
  content: $nc-hair-dryer;
}

.nc-hair-gel::before {
  content: $nc-hair-gel;
}

.nc-hair-man::before {
  content: $nc-hair-man;
}

.nc-hair-straightener::before {
  content: $nc-hair-straightener;
}

.nc-hair-towel::before {
  content: $nc-hair-towel;
}

.nc-hair-woman::before {
  content: $nc-hair-woman;
}

.nc-hairdresser::before {
  content: $nc-hairdresser;
}

.nc-halloween-pumpkin::before {
  content: $nc-halloween-pumpkin;
}

.nc-hammer::before {
  content: $nc-hammer;
}

.nc-hand-card::before {
  content: $nc-hand-card;
}

.nc-hand-heart::before {
  content: $nc-hand-heart;
}

.nc-hand-mixer::before {
  content: $nc-hand-mixer;
}

.nc-handball::before {
  content: $nc-handball;
}

.nc-handheld-console::before {
  content: $nc-handheld-console;
}

.nc-handout::before {
  content: $nc-handout;
}

.nc-hands-heart::before {
  content: $nc-hands-heart;
}

.nc-handshake::before {
  content: $nc-handshake;
}

.nc-hanging-toys::before {
  content: $nc-hanging-toys;
}

.nc-happy-baby::before {
  content: $nc-happy-baby;
}

.nc-happy-sun::before {
  content: $nc-happy-sun;
}

.nc-hash-mark::before {
  content: $nc-hash-mark;
}

.nc-hat-2::before {
  content: $nc-hat-2;
}

.nc-hat-3::before {
  content: $nc-hat-3;
}

.nc-hat-top::before {
  content: $nc-hat-top;
}

.nc-hat::before {
  content: $nc-hat;
}

.nc-hazelnut::before {
  content: $nc-hazelnut;
}

.nc-hdmi::before {
  content: $nc-hdmi;
}

.nc-heading-1::before {
  content: $nc-heading-1;
}

.nc-heading-2::before {
  content: $nc-heading-2;
}

.nc-heading-3::before {
  content: $nc-heading-3;
}

.nc-heading-4::before {
  content: $nc-heading-4;
}

.nc-heading-5::before {
  content: $nc-heading-5;
}

.nc-heading-6::before {
  content: $nc-heading-6;
}

.nc-headphones-2::before {
  content: $nc-headphones-2;
}

.nc-headphones-3::before {
  content: $nc-headphones-3;
}

.nc-headphones-mic::before {
  content: $nc-headphones-mic;
}

.nc-headphones::before {
  content: $nc-headphones;
}

.nc-headset::before {
  content: $nc-headset;
}

.nc-heart-anim::before {
  content: $nc-heart-anim;
}

.nc-heart-balloons::before {
  content: $nc-heart-balloons;
}

.nc-heart-lock::before {
  content: $nc-heart-lock;
}

.nc-heart::before {
  content: $nc-heart;
}

.nc-heartbeat::before {
  content: $nc-heartbeat;
}

.nc-hearts-suit::before {
  content: $nc-hearts-suit;
}

.nc-heater::before {
  content: $nc-heater;
}

.nc-height::before {
  content: $nc-height;
}

.nc-helicopter::before {
  content: $nc-helicopter;
}

.nc-helmet::before {
  content: $nc-helmet;
}

.nc-hide::before {
  content: $nc-hide;
}

.nc-hierarchy-53::before {
  content: $nc-hierarchy-53;
}

.nc-hierarchy-54::before {
  content: $nc-hierarchy-54;
}

.nc-hierarchy-55::before {
  content: $nc-hierarchy-55;
}

.nc-hierarchy-56::before {
  content: $nc-hierarchy-56;
}

.nc-high-priority::before {
  content: $nc-high-priority;
}

.nc-hinduism::before {
  content: $nc-hinduism;
}

.nc-hob::before {
  content: $nc-hob;
}

.nc-hockey-stick::before {
  content: $nc-hockey-stick;
}

.nc-hockey::before {
  content: $nc-hockey;
}

.nc-hold::before {
  content: $nc-hold;
}

.nc-home-2::before {
  content: $nc-home-2;
}

.nc-home-3::before {
  content: $nc-home-3;
}

.nc-home-search::before {
  content: $nc-home-search;
}

.nc-home::before {
  content: $nc-home;
}

.nc-honey::before {
  content: $nc-honey;
}

.nc-honeymoon::before {
  content: $nc-honeymoon;
}

.nc-hoodie::before {
  content: $nc-hoodie;
}

.nc-hook::before {
  content: $nc-hook;
}

.nc-horse-2::before {
  content: $nc-horse-2;
}

.nc-horse-hopper::before {
  content: $nc-horse-hopper;
}

.nc-horse::before {
  content: $nc-horse;
}

.nc-horseshoe::before {
  content: $nc-horseshoe;
}

.nc-hospital-32::before {
  content: $nc-hospital-32;
}

.nc-hospital-33::before {
  content: $nc-hospital-33;
}

.nc-hospital-34::before {
  content: $nc-hospital-34;
}

.nc-hospital-bed::before {
  content: $nc-hospital-bed;
}

.nc-hot-dog::before {
  content: $nc-hot-dog;
}

.nc-hot-key::before {
  content: $nc-hot-key;
}

.nc-hotel-bell::before {
  content: $nc-hotel-bell;
}

.nc-hotel-symbol::before {
  content: $nc-hotel-symbol;
}

.nc-hotel::before {
  content: $nc-hotel;
}

.nc-hotspot::before {
  content: $nc-hotspot;
}

.nc-hourglass::before {
  content: $nc-hourglass;
}

.nc-house-pricing::before {
  content: $nc-house-pricing;
}

.nc-house-property::before {
  content: $nc-house-property;
}

.nc-house-search-engine::before {
  content: $nc-house-search-engine;
}

.nc-house::before {
  content: $nc-house;
}

.nc-html5::before {
  content: $nc-html5;
}

.nc-humidity-26::before {
  content: $nc-humidity-26;
}

.nc-humidity-52::before {
  content: $nc-humidity-52;
}

.nc-hurricane-44::before {
  content: $nc-hurricane-44;
}

.nc-hurricane-45::before {
  content: $nc-hurricane-45;
}

.nc-hut::before {
  content: $nc-hut;
}

.nc-hybrid-car::before {
  content: $nc-hybrid-car;
}

.nc-hyperlink::before {
  content: $nc-hyperlink;
}

.nc-i-add::before {
  content: $nc-i-add;
}

.nc-i-check::before {
  content: $nc-i-check;
}

.nc-i-delete::before {
  content: $nc-i-delete;
}

.nc-i-edit::before {
  content: $nc-i-edit;
}

.nc-i-remove::before {
  content: $nc-i-remove;
}

.nc-ice-cream-22::before {
  content: $nc-ice-cream-22;
}

.nc-ice-cream-72::before {
  content: $nc-ice-cream-72;
}

.nc-ice-cream::before {
  content: $nc-ice-cream;
}

.nc-ice-skates::before {
  content: $nc-ice-skates;
}

.nc-igloo::before {
  content: $nc-igloo;
}

.nc-image-2::before {
  content: $nc-image-2;
}

.nc-image-add::before {
  content: $nc-image-add;
}

.nc-image-delete::before {
  content: $nc-image-delete;
}

.nc-image-location::before {
  content: $nc-image-location;
}

.nc-image::before {
  content: $nc-image;
}

.nc-img-rotate-left::before {
  content: $nc-img-rotate-left;
}

.nc-img-rotate-right::before {
  content: $nc-img-rotate-right;
}

.nc-img-stack::before {
  content: $nc-img-stack;
}

.nc-img::before {
  content: $nc-img;
}

.nc-incense::before {
  content: $nc-incense;
}

.nc-incognito::before {
  content: $nc-incognito;
}

.nc-increase-font-size::before {
  content: $nc-increase-font-size;
}

.nc-increase::before {
  content: $nc-increase;
}

.nc-infinite-loop::before {
  content: $nc-infinite-loop;
}

.nc-infinite::before {
  content: $nc-infinite;
}

.nc-info-point::before {
  content: $nc-info-point;
}

.nc-info::before {
  content: $nc-info;
}

.nc-infrared-thermometer::before {
  content: $nc-infrared-thermometer;
}

.nc-input::before {
  content: $nc-input;
}

.nc-instant-camera-2::before {
  content: $nc-instant-camera-2;
}

.nc-instant-camera::before {
  content: $nc-instant-camera;
}

.nc-interview::before {
  content: $nc-interview;
}

.nc-intestine::before {
  content: $nc-intestine;
}

.nc-invert-direction::before {
  content: $nc-invert-direction;
}

.nc-invert-process::before {
  content: $nc-invert-process;
}

.nc-iron-2::before {
  content: $nc-iron-2;
}

.nc-iron-dont::before {
  content: $nc-iron-dont;
}

.nc-iron::before {
  content: $nc-iron;
}

.nc-islam::before {
  content: $nc-islam;
}

.nc-istanbul::before {
  content: $nc-istanbul;
}

.nc-italic::before {
  content: $nc-italic;
}

.nc-jacuzzi::before {
  content: $nc-jacuzzi;
}

.nc-jam::before {
  content: $nc-jam;
}

.nc-jeans-41::before {
  content: $nc-jeans-41;
}

.nc-jeans-43::before {
  content: $nc-jeans-43;
}

.nc-jeans-pocket::before {
  content: $nc-jeans-pocket;
}

.nc-jelly::before {
  content: $nc-jelly;
}

.nc-jellyfish::before {
  content: $nc-jellyfish;
}

.nc-jewel::before {
  content: $nc-jewel;
}

.nc-joint-account::before {
  content: $nc-joint-account;
}

.nc-journey-06::before {
  content: $nc-journey-06;
}

.nc-journey-07::before {
  content: $nc-journey-07;
}

.nc-journey-08::before {
  content: $nc-journey-08;
}

.nc-journey::before {
  content: $nc-journey;
}

.nc-js-console::before {
  content: $nc-js-console;
}

.nc-json-logo::before {
  content: $nc-json-logo;
}

.nc-judaism::before {
  content: $nc-judaism;
}

.nc-juice::before {
  content: $nc-juice;
}

.nc-jump-rope::before {
  content: $nc-jump-rope;
}

.nc-karate::before {
  content: $nc-karate;
}

.nc-ketchup::before {
  content: $nc-ketchup;
}

.nc-kettle::before {
  content: $nc-kettle;
}

.nc-kettlebell::before {
  content: $nc-kettlebell;
}

.nc-key::before {
  content: $nc-key;
}

.nc-keyboard-hide::before {
  content: $nc-keyboard-hide;
}

.nc-keyboard-mouse::before {
  content: $nc-keyboard-mouse;
}

.nc-keyboard-wired::before {
  content: $nc-keyboard-wired;
}

.nc-keyboard-wireless::before {
  content: $nc-keyboard-wireless;
}

.nc-keyboard::before {
  content: $nc-keyboard;
}

.nc-kid-2::before {
  content: $nc-kid-2;
}

.nc-kid::before {
  content: $nc-kid;
}

.nc-kiss::before {
  content: $nc-kiss;
}

.nc-kitchen-fan::before {
  content: $nc-kitchen-fan;
}

.nc-kiwi::before {
  content: $nc-kiwi;
}

.nc-knife::before {
  content: $nc-knife;
}

.nc-knob::before {
  content: $nc-knob;
}

.nc-l-add::before {
  content: $nc-l-add;
}

.nc-l-check::before {
  content: $nc-l-check;
}

.nc-l-circle::before {
  content: $nc-l-circle;
}

.nc-l-circles::before {
  content: $nc-l-circles;
}

.nc-l-location::before {
  content: $nc-l-location;
}

.nc-l-remove::before {
  content: $nc-l-remove;
}

.nc-l-search::before {
  content: $nc-l-search;
}

.nc-l-security::before {
  content: $nc-l-security;
}

.nc-l-settings::before {
  content: $nc-l-settings;
}

.nc-l-sync::before {
  content: $nc-l-sync;
}

.nc-l-system-update::before {
  content: $nc-l-system-update;
}

.nc-label::before {
  content: $nc-label;
}

.nc-ladybug::before {
  content: $nc-ladybug;
}

.nc-lamp-3::before {
  content: $nc-lamp-3;
}

.nc-land::before {
  content: $nc-land;
}

.nc-landing::before {
  content: $nc-landing;
}

.nc-landscape-orientation::before {
  content: $nc-landscape-orientation;
}

.nc-language::before {
  content: $nc-language;
}

.nc-laptop-1::before {
  content: $nc-laptop-1;
}

.nc-laptop-2::before {
  content: $nc-laptop-2;
}

.nc-laptop-71::before {
  content: $nc-laptop-71;
}

.nc-laptop-72::before {
  content: $nc-laptop-72;
}

.nc-laptop::before {
  content: $nc-laptop;
}

.nc-lat-station::before {
  content: $nc-lat-station;
}

.nc-laugh-17::before {
  content: $nc-laugh-17;
}

.nc-laugh-35::before {
  content: $nc-laugh-35;
}

.nc-launch-app::before {
  content: $nc-launch-app;
}

.nc-launch::before {
  content: $nc-launch;
}

.nc-laundry::before {
  content: $nc-laundry;
}

.nc-law::before {
  content: $nc-law;
}

.nc-layers-2::before {
  content: $nc-layers-2;
}

.nc-layers::before {
  content: $nc-layers;
}

.nc-layout-11::before {
  content: $nc-layout-11;
}

.nc-layout-25::before {
  content: $nc-layout-25;
}

.nc-layout-grid::before {
  content: $nc-layout-grid;
}

.nc-layout::before {
  content: $nc-layout;
}

.nc-leaf-36::before {
  content: $nc-leaf-36;
}

.nc-leaf-38::before {
  content: $nc-leaf-38;
}

.nc-leaf::before {
  content: $nc-leaf;
}

.nc-leave::before {
  content: $nc-leave;
}

.nc-left-arrow::before {
  content: $nc-left-arrow;
}

.nc-leggins::before {
  content: $nc-leggins;
}

.nc-lemon-slice::before {
  content: $nc-lemon-slice;
}

.nc-lemon::before {
  content: $nc-lemon;
}

.nc-letter-a::before {
  content: $nc-letter-a;
}

.nc-letter-b::before {
  content: $nc-letter-b;
}

.nc-letter-c::before {
  content: $nc-letter-c;
}

.nc-letter-d::before {
  content: $nc-letter-d;
}

.nc-letter-e::before {
  content: $nc-letter-e;
}

.nc-letter-f::before {
  content: $nc-letter-f;
}

.nc-letter-g::before {
  content: $nc-letter-g;
}

.nc-letter-h::before {
  content: $nc-letter-h;
}

.nc-letter-i::before {
  content: $nc-letter-i;
}

.nc-letter-j::before {
  content: $nc-letter-j;
}

.nc-letter-k::before {
  content: $nc-letter-k;
}

.nc-letter-l::before {
  content: $nc-letter-l;
}

.nc-letter-m::before {
  content: $nc-letter-m;
}

.nc-letter-n::before {
  content: $nc-letter-n;
}

.nc-letter-o::before {
  content: $nc-letter-o;
}

.nc-letter-p::before {
  content: $nc-letter-p;
}

.nc-letter-q::before {
  content: $nc-letter-q;
}

.nc-letter-r::before {
  content: $nc-letter-r;
}

.nc-letter-s::before {
  content: $nc-letter-s;
}

.nc-letter-t::before {
  content: $nc-letter-t;
}

.nc-letter-u::before {
  content: $nc-letter-u;
}

.nc-letter-v::before {
  content: $nc-letter-v;
}

.nc-letter-w::before {
  content: $nc-letter-w;
}

.nc-letter-x::before {
  content: $nc-letter-x;
}

.nc-letter-y::before {
  content: $nc-letter-y;
}

.nc-letter-z::before {
  content: $nc-letter-z;
}

.nc-letter::before {
  content: $nc-letter;
}

.nc-library::before {
  content: $nc-library;
}

.nc-license-key::before {
  content: $nc-license-key;
}

.nc-lifering::before {
  content: $nc-lifering;
}

.nc-lift::before {
  content: $nc-lift;
}

.nc-light-2::before {
  content: $nc-light-2;
}

.nc-light-control::before {
  content: $nc-light-control;
}

.nc-light-switch::before {
  content: $nc-light-switch;
}

.nc-light-traffic::before {
  content: $nc-light-traffic;
}

.nc-lighter::before {
  content: $nc-lighter;
}

.nc-lighthouse::before {
  content: $nc-lighthouse;
}

.nc-lightning::before {
  content: $nc-lightning;
}

.nc-like::before {
  content: $nc-like;
}

.nc-line-chart::before {
  content: $nc-line-chart;
}

.nc-line-height::before {
  content: $nc-line-height;
}

.nc-link-broken::before {
  content: $nc-link-broken;
}

.nc-link::before {
  content: $nc-link;
}

.nc-lip-gloss::before {
  content: $nc-lip-gloss;
}

.nc-lips::before {
  content: $nc-lips;
}

.nc-lipstick-2::before {
  content: $nc-lipstick-2;
}

.nc-lipstick::before {
  content: $nc-lipstick;
}

.nc-liquid-soap-container::before {
  content: $nc-liquid-soap-container;
}

.nc-list-bullet::before {
  content: $nc-list-bullet;
}

.nc-list-numbers::before {
  content: $nc-list-numbers;
}

.nc-list::before {
  content: $nc-list;
}

.nc-live-streaming::before {
  content: $nc-live-streaming;
}

.nc-loader-bars::before {
  content: $nc-loader-bars;
}

.nc-loan::before {
  content: $nc-loan;
}

.nc-lobster::before {
  content: $nc-lobster;
}

.nc-lock-landscape::before {
  content: $nc-lock-landscape;
}

.nc-lock-orientation::before {
  content: $nc-lock-orientation;
}

.nc-lock-portrait::before {
  content: $nc-lock-portrait;
}

.nc-lock::before {
  content: $nc-lock;
}

.nc-log-in::before {
  content: $nc-log-in;
}

.nc-log-out::before {
  content: $nc-log-out;
}

.nc-logic::before {
  content: $nc-logic;
}

.nc-logout::before {
  content: $nc-logout;
}

.nc-lollipop::before {
  content: $nc-lollipop;
}

.nc-london::before {
  content: $nc-london;
}

.nc-long-sleeve::before {
  content: $nc-long-sleeve;
}

.nc-loop-2::before {
  content: $nc-loop-2;
}

.nc-loop::before {
  content: $nc-loop;
}

.nc-lotus-flower::before {
  content: $nc-lotus-flower;
}

.nc-loudspeaker::before {
  content: $nc-loudspeaker;
}

.nc-love-camera::before {
  content: $nc-love-camera;
}

.nc-love-car::before {
  content: $nc-love-car;
}

.nc-love-card::before {
  content: $nc-love-card;
}

.nc-love-heart-pin::before {
  content: $nc-love-heart-pin;
}

.nc-love-letter::before {
  content: $nc-love-letter;
}

.nc-love-message::before {
  content: $nc-love-message;
}

.nc-love-movie::before {
  content: $nc-love-movie;
}

.nc-love-song::before {
  content: $nc-love-song;
}

.nc-love::before {
  content: $nc-love;
}

.nc-low-priority::before {
  content: $nc-low-priority;
}

.nc-low-vision::before {
  content: $nc-low-vision;
}

.nc-lucky-seven::before {
  content: $nc-lucky-seven;
}

.nc-luggage::before {
  content: $nc-luggage;
}

.nc-lungs-infection::before {
  content: $nc-lungs-infection;
}

.nc-lungs::before {
  content: $nc-lungs;
}

.nc-m-add::before {
  content: $nc-m-add;
}

.nc-m-check::before {
  content: $nc-m-check;
}

.nc-m-delete::before {
  content: $nc-m-delete;
}

.nc-m-edit::before {
  content: $nc-m-edit;
}

.nc-m-heart::before {
  content: $nc-m-heart;
}

.nc-m-location::before {
  content: $nc-m-location;
}

.nc-m-remove::before {
  content: $nc-m-remove;
}

.nc-m-search::before {
  content: $nc-m-search;
}

.nc-m-security::before {
  content: $nc-m-security;
}

.nc-m-settings::before {
  content: $nc-m-settings;
}

.nc-m-share::before {
  content: $nc-m-share;
}

.nc-m-star::before {
  content: $nc-m-star;
}

.nc-m-sync::before {
  content: $nc-m-sync;
}

.nc-m-time::before {
  content: $nc-m-time;
}

.nc-m-update::before {
  content: $nc-m-update;
}

.nc-machine-learning::before {
  content: $nc-machine-learning;
}

.nc-macro::before {
  content: $nc-macro;
}

.nc-mad-12::before {
  content: $nc-mad-12;
}

.nc-mad-58::before {
  content: $nc-mad-58;
}

.nc-magnet::before {
  content: $nc-magnet;
}

.nc-magnifier-zoom-in::before {
  content: $nc-magnifier-zoom-in;
}

.nc-magnifier-zoom-out::before {
  content: $nc-magnifier-zoom-out;
}

.nc-magnifier::before {
  content: $nc-magnifier;
}

.nc-mail::before {
  content: $nc-mail;
}

.nc-makeup-blush::before {
  content: $nc-makeup-blush;
}

.nc-makeup-brush::before {
  content: $nc-makeup-brush;
}

.nc-makeup-cream::before {
  content: $nc-makeup-cream;
}

.nc-makeup-foundation::before {
  content: $nc-makeup-foundation;
}

.nc-makeup-mirror::before {
  content: $nc-makeup-mirror;
}

.nc-makeup-palette::before {
  content: $nc-makeup-palette;
}

.nc-makeup::before {
  content: $nc-makeup;
}

.nc-male::before {
  content: $nc-male;
}

.nc-malicious::before {
  content: $nc-malicious;
}

.nc-man-20::before {
  content: $nc-man-20;
}

.nc-man-23::before {
  content: $nc-man-23;
}

.nc-man-down::before {
  content: $nc-man-down;
}

.nc-man-glasses::before {
  content: $nc-man-glasses;
}

.nc-man-up-front::before {
  content: $nc-man-up-front;
}

.nc-man-up::before {
  content: $nc-man-up;
}

.nc-manga-62::before {
  content: $nc-manga-62;
}

.nc-manga-63::before {
  content: $nc-manga-63;
}

.nc-map-big::before {
  content: $nc-map-big;
}

.nc-map-compass::before {
  content: $nc-map-compass;
}

.nc-map-gps::before {
  content: $nc-map-gps;
}

.nc-map-marker::before {
  content: $nc-map-marker;
}

.nc-map-pin::before {
  content: $nc-map-pin;
}

.nc-map::before {
  content: $nc-map;
}

.nc-maple-leaf::before {
  content: $nc-maple-leaf;
}

.nc-margin-left::before {
  content: $nc-margin-left;
}

.nc-margin-right::before {
  content: $nc-margin-right;
}

.nc-mario-mushroom::before {
  content: $nc-mario-mushroom;
}

.nc-markdown::before {
  content: $nc-markdown;
}

.nc-marker-2::before {
  content: $nc-marker-2;
}

.nc-marker-3::before {
  content: $nc-marker-3;
}

.nc-marker::before {
  content: $nc-marker;
}

.nc-market-music::before {
  content: $nc-market-music;
}

.nc-market-play::before {
  content: $nc-market-play;
}

.nc-mascara::before {
  content: $nc-mascara;
}

.nc-mask-face::before {
  content: $nc-mask-face;
}

.nc-mask-oval::before {
  content: $nc-mask-oval;
}

.nc-mask-rect::before {
  content: $nc-mask-rect;
}

.nc-massage::before {
  content: $nc-massage;
}

.nc-mat::before {
  content: $nc-mat;
}

.nc-matches::before {
  content: $nc-matches;
}

.nc-math::before {
  content: $nc-math;
}

.nc-maximize-area::before {
  content: $nc-maximize-area;
}

.nc-maximize::before {
  content: $nc-maximize;
}

.nc-mayo::before {
  content: $nc-mayo;
}

.nc-measure-02::before {
  content: $nc-measure-02;
}

.nc-measure-17::before {
  content: $nc-measure-17;
}

.nc-measure-big::before {
  content: $nc-measure-big;
}

.nc-measurement::before {
  content: $nc-measurement;
}

.nc-measuring-cup::before {
  content: $nc-measuring-cup;
}

.nc-meat-spit::before {
  content: $nc-meat-spit;
}

.nc-medal::before {
  content: $nc-medal;
}

.nc-media-player::before {
  content: $nc-media-player;
}

.nc-media-stream::before {
  content: $nc-media-stream;
}

.nc-medical-clipboard::before {
  content: $nc-medical-clipboard;
}

.nc-medical-mask::before {
  content: $nc-medical-mask;
}

.nc-medication::before {
  content: $nc-medication;
}

.nc-medicine-ball::before {
  content: $nc-medicine-ball;
}

.nc-medicine::before {
  content: $nc-medicine;
}

.nc-meeting::before {
  content: $nc-meeting;
}

.nc-megaphone::before {
  content: $nc-megaphone;
}

.nc-menu-2::before {
  content: $nc-menu-2;
}

.nc-menu-3::before {
  content: $nc-menu-3;
}

.nc-menu-4::before {
  content: $nc-menu-4;
}

.nc-menu-5::before {
  content: $nc-menu-5;
}

.nc-menu-6::before {
  content: $nc-menu-6;
}

.nc-menu-7::before {
  content: $nc-menu-7;
}

.nc-menu-8::before {
  content: $nc-menu-8;
}

.nc-menu::before {
  content: $nc-menu;
}

.nc-merge-2::before {
  content: $nc-merge-2;
}

.nc-merge::before {
  content: $nc-merge;
}

.nc-messaging::before {
  content: $nc-messaging;
}

.nc-metrics::before {
  content: $nc-metrics;
}

.nc-mic-2::before {
  content: $nc-mic-2;
}

.nc-mic::before {
  content: $nc-mic;
}

.nc-mickey-mouse::before {
  content: $nc-mickey-mouse;
}

.nc-microbiology::before {
  content: $nc-microbiology;
}

.nc-microphone-2::before {
  content: $nc-microphone-2;
}

.nc-microphone-off::before {
  content: $nc-microphone-off;
}

.nc-microphone::before {
  content: $nc-microphone;
}

.nc-microscope::before {
  content: $nc-microscope;
}

.nc-microsoft::before {
  content: $nc-microsoft;
}

.nc-microwave::before {
  content: $nc-microwave;
}

.nc-migration::before {
  content: $nc-migration;
}

.nc-military-camp::before {
  content: $nc-military-camp;
}

.nc-military-knife::before {
  content: $nc-military-knife;
}

.nc-military-medal::before {
  content: $nc-military-medal;
}

.nc-military-tag::before {
  content: $nc-military-tag;
}

.nc-military-tank::before {
  content: $nc-military-tank;
}

.nc-military-vest::before {
  content: $nc-military-vest;
}

.nc-milk::before {
  content: $nc-milk;
}

.nc-miner::before {
  content: $nc-miner;
}

.nc-mirror-2::before {
  content: $nc-mirror-2;
}

.nc-mirror-display::before {
  content: $nc-mirror-display;
}

.nc-mirror-tablet-phone::before {
  content: $nc-mirror-tablet-phone;
}

.nc-mirror::before {
  content: $nc-mirror;
}

.nc-missile::before {
  content: $nc-missile;
}

.nc-mistletoe::before {
  content: $nc-mistletoe;
}

.nc-mobile-banking::before {
  content: $nc-mobile-banking;
}

.nc-mobile-card::before {
  content: $nc-mobile-card;
}

.nc-mobile-chat::before {
  content: $nc-mobile-chat;
}

.nc-mobile-contact::before {
  content: $nc-mobile-contact;
}

.nc-mobile-design::before {
  content: $nc-mobile-design;
}

.nc-mobile-dev::before {
  content: $nc-mobile-dev;
}

.nc-mobile-phone::before {
  content: $nc-mobile-phone;
}

.nc-moka::before {
  content: $nc-moka;
}

.nc-molecule-39::before {
  content: $nc-molecule-39;
}

.nc-molecule-40::before {
  content: $nc-molecule-40;
}

.nc-molecule::before {
  content: $nc-molecule;
}

.nc-money-11::before {
  content: $nc-money-11;
}

.nc-money-12::before {
  content: $nc-money-12;
}

.nc-money-13::before {
  content: $nc-money-13;
}

.nc-money-bag::before {
  content: $nc-money-bag;
}

.nc-money-coins::before {
  content: $nc-money-coins;
}

.nc-money-growth::before {
  content: $nc-money-growth;
}

.nc-money-time::before {
  content: $nc-money-time;
}

.nc-money-transfer::before {
  content: $nc-money-transfer;
}

.nc-monster::before {
  content: $nc-monster;
}

.nc-moon-cloud-drop::before {
  content: $nc-moon-cloud-drop;
}

.nc-moon-cloud-fog::before {
  content: $nc-moon-cloud-fog;
}

.nc-moon-cloud-hail::before {
  content: $nc-moon-cloud-hail;
}

.nc-moon-cloud-light::before {
  content: $nc-moon-cloud-light;
}

.nc-moon-cloud-rain::before {
  content: $nc-moon-cloud-rain;
}

.nc-moon-cloud-snow-61::before {
  content: $nc-moon-cloud-snow-61;
}

.nc-moon-cloud-snow-62::before {
  content: $nc-moon-cloud-snow-62;
}

.nc-moon-fog::before {
  content: $nc-moon-fog;
}

.nc-moon-full::before {
  content: $nc-moon-full;
}

.nc-moon-stars::before {
  content: $nc-moon-stars;
}

.nc-moon::before {
  content: $nc-moon;
}

.nc-mortar::before {
  content: $nc-mortar;
}

.nc-mortgage::before {
  content: $nc-mortgage;
}

.nc-mosque::before {
  content: $nc-mosque;
}

.nc-moto::before {
  content: $nc-moto;
}

.nc-mountain::before {
  content: $nc-mountain;
}

.nc-mouse-2::before {
  content: $nc-mouse-2;
}

.nc-mouse-anim::before {
  content: $nc-mouse-anim;
}

.nc-mouse::before {
  content: $nc-mouse;
}

.nc-move-2::before {
  content: $nc-move-2;
}

.nc-move-3::before {
  content: $nc-move-3;
}

.nc-move-down-2::before {
  content: $nc-move-down-2;
}

.nc-move-down-right::before {
  content: $nc-move-down-right;
}

.nc-move-down::before {
  content: $nc-move-down;
}

.nc-move-layer-down::before {
  content: $nc-move-layer-down;
}

.nc-move-layer-left::before {
  content: $nc-move-layer-left;
}

.nc-move-layer-right::before {
  content: $nc-move-layer-right;
}

.nc-move-layer-up::before {
  content: $nc-move-layer-up;
}

.nc-move-left::before {
  content: $nc-move-left;
}

.nc-move-right::before {
  content: $nc-move-right;
}

.nc-move-up-2::before {
  content: $nc-move-up-2;
}

.nc-move-up-left::before {
  content: $nc-move-up-left;
}

.nc-move-up::before {
  content: $nc-move-up;
}

.nc-move::before {
  content: $nc-move;
}

.nc-movie-2::before {
  content: $nc-movie-2;
}

.nc-movie-3::before {
  content: $nc-movie-3;
}

.nc-movie-reel::before {
  content: $nc-movie-reel;
}

.nc-movie::before {
  content: $nc-movie;
}

.nc-mower::before {
  content: $nc-mower;
}

.nc-muffin::before {
  content: $nc-muffin;
}

.nc-mug::before {
  content: $nc-mug;
}

.nc-multiple-11::before {
  content: $nc-multiple-11;
}

.nc-multiple-19::before {
  content: $nc-multiple-19;
}

.nc-multiple::before {
  content: $nc-multiple;
}

.nc-mushroom::before {
  content: $nc-mushroom;
}

.nc-music-album::before {
  content: $nc-music-album;
}

.nc-music-cloud::before {
  content: $nc-music-cloud;
}

.nc-music-note::before {
  content: $nc-music-note;
}

.nc-music-player::before {
  content: $nc-music-player;
}

.nc-music-playlist::before {
  content: $nc-music-playlist;
}

.nc-music::before {
  content: $nc-music;
}

.nc-mustache::before {
  content: $nc-mustache;
}

.nc-n-check::before {
  content: $nc-n-check;
}

.nc-n-edit::before {
  content: $nc-n-edit;
}

.nc-nail-file::before {
  content: $nc-nail-file;
}

.nc-nail-polish-2::before {
  content: $nc-nail-polish-2;
}

.nc-nail-polish::before {
  content: $nc-nail-polish;
}

.nc-name-card::before {
  content: $nc-name-card;
}

.nc-nav-down::before {
  content: $nc-nav-down;
}

.nc-nav-left::before {
  content: $nc-nav-left;
}

.nc-nav-right::before {
  content: $nc-nav-right;
}

.nc-nav-up::before {
  content: $nc-nav-up;
}

.nc-navigation::before {
  content: $nc-navigation;
}

.nc-neck-duster::before {
  content: $nc-neck-duster;
}

.nc-needle::before {
  content: $nc-needle;
}

.nc-negative-judgement::before {
  content: $nc-negative-judgement;
}

.nc-nerd::before {
  content: $nc-nerd;
}

.nc-net::before {
  content: $nc-net;
}

.nc-network-communication::before {
  content: $nc-network-communication;
}

.nc-network-connection::before {
  content: $nc-network-connection;
}

.nc-network::before {
  content: $nc-network;
}

.nc-networking::before {
  content: $nc-networking;
}

.nc-new-construction::before {
  content: $nc-new-construction;
}

.nc-new-notification::before {
  content: $nc-new-notification;
}

.nc-new::before {
  content: $nc-new;
}

.nc-news::before {
  content: $nc-news;
}

.nc-newsletter-dev::before {
  content: $nc-newsletter-dev;
}

.nc-newsletter::before {
  content: $nc-newsletter;
}

.nc-night-table::before {
  content: $nc-night-table;
}

.nc-night::before {
  content: $nc-night;
}

.nc-nine::before {
  content: $nc-nine;
}

.nc-ninja::before {
  content: $nc-ninja;
}

.nc-no-contact::before {
  content: $nc-no-contact;
}

.nc-no-guns::before {
  content: $nc-no-guns;
}

.nc-no-photo::before {
  content: $nc-no-photo;
}

.nc-no-results::before {
  content: $nc-no-results;
}

.nc-no-smoking::before {
  content: $nc-no-smoking;
}

.nc-no-words::before {
  content: $nc-no-words;
}

.nc-nodes::before {
  content: $nc-nodes;
}

.nc-noodles::before {
  content: $nc-noodles;
}

.nc-note-code::before {
  content: $nc-note-code;
}

.nc-note::before {
  content: $nc-note;
}

.nc-notebook::before {
  content: $nc-notebook;
}

.nc-notepad::before {
  content: $nc-notepad;
}

.nc-notes::before {
  content: $nc-notes;
}

.nc-notification-2::before {
  content: $nc-notification-2;
}

.nc-notification::before {
  content: $nc-notification;
}

.nc-nurse::before {
  content: $nc-nurse;
}

.nc-nutrition::before {
  content: $nc-nutrition;
}

.nc-ny::before {
  content: $nc-ny;
}

.nc-o-check::before {
  content: $nc-o-check;
}

.nc-o-warning::before {
  content: $nc-o-warning;
}

.nc-octagon-m::before {
  content: $nc-octagon-m;
}

.nc-octagon::before {
  content: $nc-octagon;
}

.nc-octopus::before {
  content: $nc-octopus;
}

.nc-office-chair::before {
  content: $nc-office-chair;
}

.nc-office::before {
  content: $nc-office;
}

.nc-offline::before {
  content: $nc-offline;
}

.nc-oil-2::before {
  content: $nc-oil-2;
}

.nc-oil::before {
  content: $nc-oil;
}

.nc-olympic-flame::before {
  content: $nc-olympic-flame;
}

.nc-one::before {
  content: $nc-one;
}

.nc-onion::before {
  content: $nc-onion;
}

.nc-online-banking::before {
  content: $nc-online-banking;
}

.nc-open-book::before {
  content: $nc-open-book;
}

.nc-open-folder::before {
  content: $nc-open-folder;
}

.nc-open-in-browser::before {
  content: $nc-open-in-browser;
}

.nc-opening-times::before {
  content: $nc-opening-times;
}

.nc-opposite-directions-2::before {
  content: $nc-opposite-directions-2;
}

.nc-opposite-directions::before {
  content: $nc-opposite-directions;
}

.nc-options::before {
  content: $nc-options;
}

.nc-orange::before {
  content: $nc-orange;
}

.nc-organic-2::before {
  content: $nc-organic-2;
}

.nc-organic::before {
  content: $nc-organic;
}

.nc-orientation::before {
  content: $nc-orientation;
}

.nc-oven::before {
  content: $nc-oven;
}

.nc-ovum-sperm::before {
  content: $nc-ovum-sperm;
}

.nc-owl::before {
  content: $nc-owl;
}

.nc-p-add::before {
  content: $nc-p-add;
}

.nc-p-chart::before {
  content: $nc-p-chart;
}

.nc-p-check::before {
  content: $nc-p-check;
}

.nc-p-edit::before {
  content: $nc-p-edit;
}

.nc-p-heart::before {
  content: $nc-p-heart;
}

.nc-p-location::before {
  content: $nc-p-location;
}

.nc-p-remove::before {
  content: $nc-p-remove;
}

.nc-p-search::before {
  content: $nc-p-search;
}

.nc-p-settings::before {
  content: $nc-p-settings;
}

.nc-p-share::before {
  content: $nc-p-share;
}

.nc-p-sync::before {
  content: $nc-p-sync;
}

.nc-p-system-update::before {
  content: $nc-p-system-update;
}

.nc-p-time::before {
  content: $nc-p-time;
}

.nc-pacifier::before {
  content: $nc-pacifier;
}

.nc-pacman::before {
  content: $nc-pacman;
}

.nc-padlock-unlocked::before {
  content: $nc-padlock-unlocked;
}

.nc-padlock::before {
  content: $nc-padlock;
}

.nc-paint-16::before {
  content: $nc-paint-16;
}

.nc-paint-37::before {
  content: $nc-paint-37;
}

.nc-paint-38::before {
  content: $nc-paint-38;
}

.nc-paint-brush::before {
  content: $nc-paint-brush;
}

.nc-paint-bucket-39::before {
  content: $nc-paint-bucket-39;
}

.nc-paint-bucket-40::before {
  content: $nc-paint-bucket-40;
}

.nc-pajamas::before {
  content: $nc-pajamas;
}

.nc-palette::before {
  content: $nc-palette;
}

.nc-palm-tree::before {
  content: $nc-palm-tree;
}

.nc-pan::before {
  content: $nc-pan;
}

.nc-pancake::before {
  content: $nc-pancake;
}

.nc-panda::before {
  content: $nc-panda;
}

.nc-panel::before {
  content: $nc-panel;
}

.nc-pantone::before {
  content: $nc-pantone;
}

.nc-paper-design::before {
  content: $nc-paper-design;
}

.nc-paper-dev::before {
  content: $nc-paper-dev;
}

.nc-paper-diploma::before {
  content: $nc-paper-diploma;
}

.nc-paper::before {
  content: $nc-paper;
}

.nc-parachute::before {
  content: $nc-parachute;
}

.nc-paragraph-2::before {
  content: $nc-paragraph-2;
}

.nc-paragraph::before {
  content: $nc-paragraph;
}

.nc-paralympic-games::before {
  content: $nc-paralympic-games;
}

.nc-parent::before {
  content: $nc-parent;
}

.nc-paris-tower::before {
  content: $nc-paris-tower;
}

.nc-park::before {
  content: $nc-park;
}

.nc-parking-sensors::before {
  content: $nc-parking-sensors;
}

.nc-parking::before {
  content: $nc-parking;
}

.nc-parrot::before {
  content: $nc-parrot;
}

.nc-party::before {
  content: $nc-party;
}

.nc-passenger::before {
  content: $nc-passenger;
}

.nc-passport::before {
  content: $nc-passport;
}

.nc-password::before {
  content: $nc-password;
}

.nc-pasta::before {
  content: $nc-pasta;
}

.nc-patch-19::before {
  content: $nc-patch-19;
}

.nc-patch-34::before {
  content: $nc-patch-34;
}

.nc-patch::before {
  content: $nc-patch;
}

.nc-path-exclude::before {
  content: $nc-path-exclude;
}

.nc-path-intersect::before {
  content: $nc-path-intersect;
}

.nc-path-minus::before {
  content: $nc-path-minus;
}

.nc-path-unite::before {
  content: $nc-path-unite;
}

.nc-pattern-recognition::before {
  content: $nc-pattern-recognition;
}

.nc-paw::before {
  content: $nc-paw;
}

.nc-payee::before {
  content: $nc-payee;
}

.nc-payment-method::before {
  content: $nc-payment-method;
}

.nc-payment::before {
  content: $nc-payment;
}

.nc-payor::before {
  content: $nc-payor;
}

.nc-pc-monitor::before {
  content: $nc-pc-monitor;
}

.nc-pc-mouse::before {
  content: $nc-pc-mouse;
}

.nc-pc-play-media::before {
  content: $nc-pc-play-media;
}

.nc-pc::before {
  content: $nc-pc;
}

.nc-pci-card::before {
  content: $nc-pci-card;
}

.nc-peanut::before {
  content: $nc-peanut;
}

.nc-pear::before {
  content: $nc-pear;
}

.nc-peas::before {
  content: $nc-peas;
}

.nc-pectoral-machine::before {
  content: $nc-pectoral-machine;
}

.nc-pen-01::before {
  content: $nc-pen-01;
}

.nc-pen-2::before {
  content: $nc-pen-2;
}

.nc-pen-23::before {
  content: $nc-pen-23;
}

.nc-pen-tool::before {
  content: $nc-pen-tool;
}

.nc-pen::before {
  content: $nc-pen;
}

.nc-pencil-47::before {
  content: $nc-pencil-47;
}

.nc-pencil::before {
  content: $nc-pencil;
}

.nc-pendant-lighting::before {
  content: $nc-pendant-lighting;
}

.nc-pendulum::before {
  content: $nc-pendulum;
}

.nc-penguin::before {
  content: $nc-penguin;
}

.nc-pennant::before {
  content: $nc-pennant;
}

.nc-pepper::before {
  content: $nc-pepper;
}

.nc-percent-sign::before {
  content: $nc-percent-sign;
}

.nc-percentage-38::before {
  content: $nc-percentage-38;
}

.nc-percentage-39::before {
  content: $nc-percentage-39;
}

.nc-perfume::before {
  content: $nc-perfume;
}

.nc-personal-trainer::before {
  content: $nc-personal-trainer;
}

.nc-pet-food::before {
  content: $nc-pet-food;
}

.nc-pharmacy::before {
  content: $nc-pharmacy;
}

.nc-phone-button::before {
  content: $nc-phone-button;
}

.nc-phone-call-end::before {
  content: $nc-phone-call-end;
}

.nc-phone-call::before {
  content: $nc-phone-call;
}

.nc-phone-camera-back::before {
  content: $nc-phone-camera-back;
}

.nc-phone-camera-front::before {
  content: $nc-phone-camera-front;
}

.nc-phone-charging-2::before {
  content: $nc-phone-charging-2;
}

.nc-phone-charging-3::before {
  content: $nc-phone-charging-3;
}

.nc-phone-charging::before {
  content: $nc-phone-charging;
}

.nc-phone-dock::before {
  content: $nc-phone-dock;
}

.nc-phone-heart::before {
  content: $nc-phone-heart;
}

.nc-phone-heartbeat::before {
  content: $nc-phone-heartbeat;
}

.nc-phone-music::before {
  content: $nc-phone-music;
}

.nc-phone-toolbar::before {
  content: $nc-phone-toolbar;
}

.nc-phone::before {
  content: $nc-phone;
}

.nc-photo-album::before {
  content: $nc-photo-album;
}

.nc-photo-editor::before {
  content: $nc-photo-editor;
}

.nc-photo-frame::before {
  content: $nc-photo-frame;
}

.nc-photo-not-allowed::before {
  content: $nc-photo-not-allowed;
}

.nc-photo::before {
  content: $nc-photo;
}

.nc-piano-2::before {
  content: $nc-piano-2;
}

.nc-piano::before {
  content: $nc-piano;
}

.nc-pickaxe::before {
  content: $nc-pickaxe;
}

.nc-pickle::before {
  content: $nc-pickle;
}

.nc-picnic-basket::before {
  content: $nc-picnic-basket;
}

.nc-picture::before {
  content: $nc-picture;
}

.nc-pie::before {
  content: $nc-pie;
}

.nc-pig-2::before {
  content: $nc-pig-2;
}

.nc-pig::before {
  content: $nc-pig;
}

.nc-pilcrow::before {
  content: $nc-pilcrow;
}

.nc-pilgrim-hat::before {
  content: $nc-pilgrim-hat;
}

.nc-pill-42::before {
  content: $nc-pill-42;
}

.nc-pill-43::before {
  content: $nc-pill-43;
}

.nc-pill-bottle::before {
  content: $nc-pill-bottle;
}

.nc-pin-2::before {
  content: $nc-pin-2;
}

.nc-pin-3::before {
  content: $nc-pin-3;
}

.nc-pin-4::before {
  content: $nc-pin-4;
}

.nc-pin-add-2::before {
  content: $nc-pin-add-2;
}

.nc-pin-add::before {
  content: $nc-pin-add;
}

.nc-pin-check::before {
  content: $nc-pin-check;
}

.nc-pin-copy::before {
  content: $nc-pin-copy;
}

.nc-pin-delete::before {
  content: $nc-pin-delete;
}

.nc-pin-edit::before {
  content: $nc-pin-edit;
}

.nc-pin-heart::before {
  content: $nc-pin-heart;
}

.nc-pin-remove-2::before {
  content: $nc-pin-remove-2;
}

.nc-pin-remove::before {
  content: $nc-pin-remove;
}

.nc-pin-search::before {
  content: $nc-pin-search;
}

.nc-pin-security::before {
  content: $nc-pin-security;
}

.nc-pin-settings::before {
  content: $nc-pin-settings;
}

.nc-pin-share::before {
  content: $nc-pin-share;
}

.nc-pin-star::before {
  content: $nc-pin-star;
}

.nc-pin-sync::before {
  content: $nc-pin-sync;
}

.nc-pin-time::before {
  content: $nc-pin-time;
}

.nc-pin-user::before {
  content: $nc-pin-user;
}

.nc-pin::before {
  content: $nc-pin;
}

.nc-pinch::before {
  content: $nc-pinch;
}

.nc-pineapple::before {
  content: $nc-pineapple;
}

.nc-pins::before {
  content: $nc-pins;
}

.nc-pipe::before {
  content: $nc-pipe;
}

.nc-pirate::before {
  content: $nc-pirate;
}

.nc-pizza-slice::before {
  content: $nc-pizza-slice;
}

.nc-pizza::before {
  content: $nc-pizza;
}

.nc-plane::before {
  content: $nc-plane;
}

.nc-planet::before {
  content: $nc-planet;
}

.nc-plant-ground::before {
  content: $nc-plant-ground;
}

.nc-plant-leaf::before {
  content: $nc-plant-leaf;
}

.nc-plant-vase::before {
  content: $nc-plant-vase;
}

.nc-plate::before {
  content: $nc-plate;
}

.nc-play-media::before {
  content: $nc-play-media;
}

.nc-play-movie::before {
  content: $nc-play-movie;
}

.nc-player::before {
  content: $nc-player;
}

.nc-playground::before {
  content: $nc-playground;
}

.nc-playing-cards::before {
  content: $nc-playing-cards;
}

.nc-playlist::before {
  content: $nc-playlist;
}

.nc-plug-2::before {
  content: $nc-plug-2;
}

.nc-plug::before {
  content: $nc-plug;
}

.nc-podcast-mic::before {
  content: $nc-podcast-mic;
}

.nc-podcast::before {
  content: $nc-podcast;
}

.nc-podium-trophy::before {
  content: $nc-podium-trophy;
}

.nc-podium::before {
  content: $nc-podium;
}

.nc-point-a::before {
  content: $nc-point-a;
}

.nc-point-b::before {
  content: $nc-point-b;
}

.nc-pointing-down::before {
  content: $nc-pointing-down;
}

.nc-pointing-left::before {
  content: $nc-pointing-left;
}

.nc-pointing-right::before {
  content: $nc-pointing-right;
}

.nc-pointing-up::before {
  content: $nc-pointing-up;
}

.nc-polaroid-photo::before {
  content: $nc-polaroid-photo;
}

.nc-polaroid-portrait::before {
  content: $nc-polaroid-portrait;
}

.nc-polaroid-shot-delete::before {
  content: $nc-polaroid-shot-delete;
}

.nc-polaroid-shot-new::before {
  content: $nc-polaroid-shot-new;
}

.nc-polaroid-shots::before {
  content: $nc-polaroid-shots;
}

.nc-polaroid::before {
  content: $nc-polaroid;
}

.nc-police::before {
  content: $nc-police;
}

.nc-poop::before {
  content: $nc-poop;
}

.nc-popcorn::before {
  content: $nc-popcorn;
}

.nc-pos::before {
  content: $nc-pos;
}

.nc-position-marker::before {
  content: $nc-position-marker;
}

.nc-position-pin::before {
  content: $nc-position-pin;
}

.nc-position-user::before {
  content: $nc-position-user;
}

.nc-position::before {
  content: $nc-position;
}

.nc-positive-judgement::before {
  content: $nc-positive-judgement;
}

.nc-pot::before {
  content: $nc-pot;
}

.nc-potato::before {
  content: $nc-potato;
}

.nc-potion::before {
  content: $nc-potion;
}

.nc-power-level::before {
  content: $nc-power-level;
}

.nc-power-lifting::before {
  content: $nc-power-lifting;
}

.nc-power-rack::before {
  content: $nc-power-rack;
}

.nc-pram::before {
  content: $nc-pram;
}

.nc-preferences::before {
  content: $nc-preferences;
}

.nc-pregnancy-test::before {
  content: $nc-pregnancy-test;
}

.nc-pregnant-woman::before {
  content: $nc-pregnant-woman;
}

.nc-present::before {
  content: $nc-present;
}

.nc-presentation::before {
  content: $nc-presentation;
}

.nc-print::before {
  content: $nc-print;
}

.nc-printer::before {
  content: $nc-printer;
}

.nc-priority-high::before {
  content: $nc-priority-high;
}

.nc-priority-highest::before {
  content: $nc-priority-highest;
}

.nc-priority-low::before {
  content: $nc-priority-low;
}

.nc-priority-lowest::before {
  content: $nc-priority-lowest;
}

.nc-priority-normal::before {
  content: $nc-priority-normal;
}

.nc-privacy-policy::before {
  content: $nc-privacy-policy;
}

.nc-privacy-settings::before {
  content: $nc-privacy-settings;
}

.nc-privacy::before {
  content: $nc-privacy;
}

.nc-profile::before {
  content: $nc-profile;
}

.nc-progress-2::before {
  content: $nc-progress-2;
}

.nc-progress-indicator::before {
  content: $nc-progress-indicator;
}

.nc-progress::before {
  content: $nc-progress;
}

.nc-projector::before {
  content: $nc-projector;
}

.nc-property-agreement::before {
  content: $nc-property-agreement;
}

.nc-property-app::before {
  content: $nc-property-app;
}

.nc-property-for-sale::before {
  content: $nc-property-for-sale;
}

.nc-property-location::before {
  content: $nc-property-location;
}

.nc-property-sold::before {
  content: $nc-property-sold;
}

.nc-property-to-rent::before {
  content: $nc-property-to-rent;
}

.nc-property::before {
  content: $nc-property;
}

.nc-prosciutto::before {
  content: $nc-prosciutto;
}

.nc-prototype::before {
  content: $nc-prototype;
}

.nc-pulse-chart::before {
  content: $nc-pulse-chart;
}

.nc-pulse-sleep::before {
  content: $nc-pulse-sleep;
}

.nc-pulse::before {
  content: $nc-pulse;
}

.nc-pumpkin::before {
  content: $nc-pumpkin;
}

.nc-puzzle-09::before {
  content: $nc-puzzle-09;
}

.nc-puzzle-10::before {
  content: $nc-puzzle-10;
}

.nc-puzzle-toy::before {
  content: $nc-puzzle-toy;
}

.nc-puzzled::before {
  content: $nc-puzzled;
}

.nc-pyramid::before {
  content: $nc-pyramid;
}

.nc-question-mark::before {
  content: $nc-question-mark;
}

.nc-questionnaire::before {
  content: $nc-questionnaire;
}

.nc-quite-happy::before {
  content: $nc-quite-happy;
}

.nc-quote::before {
  content: $nc-quote;
}

.nc-r-chat::before {
  content: $nc-r-chat;
}

.nc-r-down-left-arrows::before {
  content: $nc-r-down-left-arrows;
}

.nc-r-down-right-arrows::before {
  content: $nc-r-down-right-arrows;
}

.nc-r-up-left-arrows::before {
  content: $nc-r-up-left-arrows;
}

.nc-r-up-right-arrows::before {
  content: $nc-r-up-right-arrows;
}

.nc-rabbit::before {
  content: $nc-rabbit;
}

.nc-radar::before {
  content: $nc-radar;
}

.nc-radiation::before {
  content: $nc-radiation;
}

.nc-radio-btn-checked::before {
  content: $nc-radio-btn-checked;
}

.nc-radio-btn::before {
  content: $nc-radio-btn;
}

.nc-radio::before {
  content: $nc-radio;
}

.nc-rain-hail::before {
  content: $nc-rain-hail;
}

.nc-rain::before {
  content: $nc-rain;
}

.nc-rainbow::before {
  content: $nc-rainbow;
}

.nc-ram-2::before {
  content: $nc-ram-2;
}

.nc-ram::before {
  content: $nc-ram;
}

.nc-random::before {
  content: $nc-random;
}

.nc-ranking::before {
  content: $nc-ranking;
}

.nc-rat-head::before {
  content: $nc-rat-head;
}

.nc-rat::before {
  content: $nc-rat;
}

.nc-rate-down::before {
  content: $nc-rate-down;
}

.nc-rate-up::before {
  content: $nc-rate-up;
}

.nc-raw-image::before {
  content: $nc-raw-image;
}

.nc-razor::before {
  content: $nc-razor;
}

.nc-read::before {
  content: $nc-read;
}

.nc-reading-tablet::before {
  content: $nc-reading-tablet;
}

.nc-reading::before {
  content: $nc-reading;
}

.nc-real-estate::before {
  content: $nc-real-estate;
}

.nc-receipt-list-42::before {
  content: $nc-receipt-list-42;
}

.nc-receipt-list-43::before {
  content: $nc-receipt-list-43;
}

.nc-receipt::before {
  content: $nc-receipt;
}

.nc-recipe-book-46::before {
  content: $nc-recipe-book-46;
}

.nc-recipe-book-47::before {
  content: $nc-recipe-book-47;
}

.nc-recipe-create::before {
  content: $nc-recipe-create;
}

.nc-recipe::before {
  content: $nc-recipe;
}

.nc-record-player::before {
  content: $nc-record-player;
}

.nc-recycling::before {
  content: $nc-recycling;
}

.nc-redo::before {
  content: $nc-redo;
}

.nc-referee::before {
  content: $nc-referee;
}

.nc-refresh-01::before {
  content: $nc-refresh-01;
}

.nc-refresh-02::before {
  content: $nc-refresh-02;
}

.nc-refresh::before {
  content: $nc-refresh;
}

.nc-refund::before {
  content: $nc-refund;
}

.nc-reload::before {
  content: $nc-reload;
}

.nc-remote-control::before {
  content: $nc-remote-control;
}

.nc-remove-fav::before {
  content: $nc-remove-fav;
}

.nc-remove-favorite::before {
  content: $nc-remove-favorite;
}

.nc-remove-like::before {
  content: $nc-remove-like;
}

.nc-remove::before {
  content: $nc-remove;
}

.nc-repeat-cycle::before {
  content: $nc-repeat-cycle;
}

.nc-repeat::before {
  content: $nc-repeat;
}

.nc-replay::before {
  content: $nc-replay;
}

.nc-reply-all::before {
  content: $nc-reply-all;
}

.nc-reply-arrow::before {
  content: $nc-reply-arrow;
}

.nc-reply::before {
  content: $nc-reply;
}

.nc-research::before {
  content: $nc-research;
}

.nc-reservation::before {
  content: $nc-reservation;
}

.nc-resistance-band::before {
  content: $nc-resistance-band;
}

.nc-resize-h::before {
  content: $nc-resize-h;
}

.nc-resize-v::before {
  content: $nc-resize-v;
}

.nc-respond-arrow::before {
  content: $nc-respond-arrow;
}

.nc-restaurant-menu::before {
  content: $nc-restaurant-menu;
}

.nc-restore::before {
  content: $nc-restore;
}

.nc-rice::before {
  content: $nc-rice;
}

.nc-right-arrow::before {
  content: $nc-right-arrow;
}

.nc-rim::before {
  content: $nc-rim;
}

.nc-ring::before {
  content: $nc-ring;
}

.nc-rings::before {
  content: $nc-rings;
}

.nc-rio::before {
  content: $nc-rio;
}

.nc-ripple-anim::before {
  content: $nc-ripple-anim;
}

.nc-road-2::before {
  content: $nc-road-2;
}

.nc-road-sign-left::before {
  content: $nc-road-sign-left;
}

.nc-road-sign-right::before {
  content: $nc-road-sign-right;
}

.nc-road::before {
  content: $nc-road;
}

.nc-roadmap::before {
  content: $nc-roadmap;
}

.nc-roast-chicken::before {
  content: $nc-roast-chicken;
}

.nc-roast-turkey::before {
  content: $nc-roast-turkey;
}

.nc-robot-cleaner::before {
  content: $nc-robot-cleaner;
}

.nc-robot::before {
  content: $nc-robot;
}

.nc-robotic-arm::before {
  content: $nc-robotic-arm;
}

.nc-rock::before {
  content: $nc-rock;
}

.nc-rolling-pin::before {
  content: $nc-rolling-pin;
}

.nc-romantic-dinner::before {
  content: $nc-romantic-dinner;
}

.nc-romantic-restaurant::before {
  content: $nc-romantic-restaurant;
}

.nc-rome::before {
  content: $nc-rome;
}

.nc-rotate-22::before {
  content: $nc-rotate-22;
}

.nc-rotate-23::before {
  content: $nc-rotate-23;
}

.nc-rotate-camera::before {
  content: $nc-rotate-camera;
}

.nc-rotate-left::before {
  content: $nc-rotate-left;
}

.nc-rotate-right::before {
  content: $nc-rotate-right;
}

.nc-rotating-bars-anim::before {
  content: $nc-rotating-bars-anim;
}

.nc-roulette::before {
  content: $nc-roulette;
}

.nc-round-dollar::before {
  content: $nc-round-dollar;
}

.nc-round-euro::before {
  content: $nc-round-euro;
}

.nc-round-pound::before {
  content: $nc-round-pound;
}

.nc-round-yen::before {
  content: $nc-round-yen;
}

.nc-route-alert::before {
  content: $nc-route-alert;
}

.nc-route-close::before {
  content: $nc-route-close;
}

.nc-route-open::before {
  content: $nc-route-open;
}

.nc-route::before {
  content: $nc-route;
}

.nc-router::before {
  content: $nc-router;
}

.nc-row-machine::before {
  content: $nc-row-machine;
}

.nc-row-table::before {
  content: $nc-row-table;
}

.nc-rowing-oars::before {
  content: $nc-rowing-oars;
}

.nc-rowing::before {
  content: $nc-rowing;
}

.nc-rugby-ball::before {
  content: $nc-rugby-ball;
}

.nc-rugby::before {
  content: $nc-rugby;
}

.nc-ruler-pencil::before {
  content: $nc-ruler-pencil;
}

.nc-run-shoes::before {
  content: $nc-run-shoes;
}

.nc-runny-nose::before {
  content: $nc-runny-nose;
}

.nc-s-add::before {
  content: $nc-s-add;
}

.nc-s-ban::before {
  content: $nc-s-ban;
}

.nc-s-check::before {
  content: $nc-s-check;
}

.nc-s-delete::before {
  content: $nc-s-delete;
}

.nc-s-edit::before {
  content: $nc-s-edit;
}

.nc-s-info::before {
  content: $nc-s-info;
}

.nc-s-pulse::before {
  content: $nc-s-pulse;
}

.nc-s-question::before {
  content: $nc-s-question;
}

.nc-s-remove::before {
  content: $nc-s-remove;
}

.nc-s-warning::before {
  content: $nc-s-warning;
}

.nc-sad::before {
  content: $nc-sad;
}

.nc-safe::before {
  content: $nc-safe;
}

.nc-salad::before {
  content: $nc-salad;
}

.nc-sale::before {
  content: $nc-sale;
}

.nc-salt::before {
  content: $nc-salt;
}

.nc-santa-hat::before {
  content: $nc-santa-hat;
}

.nc-satellite-dish::before {
  content: $nc-satellite-dish;
}

.nc-satellite::before {
  content: $nc-satellite;
}

.nc-satisfied::before {
  content: $nc-satisfied;
}

.nc-sauna::before {
  content: $nc-sauna;
}

.nc-sausage::before {
  content: $nc-sausage;
}

.nc-save-for-later::before {
  content: $nc-save-for-later;
}

.nc-save-planet::before {
  content: $nc-save-planet;
}

.nc-save-the-date::before {
  content: $nc-save-the-date;
}

.nc-save-to-list::before {
  content: $nc-save-to-list;
}

.nc-saved-items::before {
  content: $nc-saved-items;
}

.nc-savings::before {
  content: $nc-savings;
}

.nc-saxophone::before {
  content: $nc-saxophone;
}

.nc-scale-2::before {
  content: $nc-scale-2;
}

.nc-scale-3::before {
  content: $nc-scale-3;
}

.nc-scale-4::before {
  content: $nc-scale-4;
}

.nc-scale-down::before {
  content: $nc-scale-down;
}

.nc-scale-horizontal::before {
  content: $nc-scale-horizontal;
}

.nc-scale-up::before {
  content: $nc-scale-up;
}

.nc-scale-vertical::before {
  content: $nc-scale-vertical;
}

.nc-scale::before {
  content: $nc-scale;
}

.nc-scan::before {
  content: $nc-scan;
}

.nc-scarf::before {
  content: $nc-scarf;
}

.nc-scented-candle::before {
  content: $nc-scented-candle;
}

.nc-school::before {
  content: $nc-school;
}

.nc-scissors-2::before {
  content: $nc-scissors-2;
}

.nc-scissors-dashed::before {
  content: $nc-scissors-dashed;
}

.nc-scissors::before {
  content: $nc-scissors;
}

.nc-scooter::before {
  content: $nc-scooter;
}

.nc-scotch::before {
  content: $nc-scotch;
}

.nc-screen-enlarge::before {
  content: $nc-screen-enlarge;
}

.nc-screen-expand::before {
  content: $nc-screen-expand;
}

.nc-screen-maximize::before {
  content: $nc-screen-maximize;
}

.nc-screen-reader::before {
  content: $nc-screen-reader;
}

.nc-screen-rotation::before {
  content: $nc-screen-rotation;
}

.nc-screen-sharing-2::before {
  content: $nc-screen-sharing-2;
}

.nc-screen-sharing-off-2::before {
  content: $nc-screen-sharing-off-2;
}

.nc-screen-touch::before {
  content: $nc-screen-touch;
}

.nc-scroll-horizontal::before {
  content: $nc-scroll-horizontal;
}

.nc-scroll-vertical::before {
  content: $nc-scroll-vertical;
}

.nc-sd-card::before {
  content: $nc-sd-card;
}

.nc-search-3::before {
  content: $nc-search-3;
}

.nc-search-content::before {
  content: $nc-search-content;
}

.nc-search-property::before {
  content: $nc-search-property;
}

.nc-search-zoom-in::before {
  content: $nc-search-zoom-in;
}

.nc-search-zoom-out::before {
  content: $nc-search-zoom-out;
}

.nc-search::before {
  content: $nc-search;
}

.nc-seat::before {
  content: $nc-seat;
}

.nc-seatbelt::before {
  content: $nc-seatbelt;
}

.nc-security-gate::before {
  content: $nc-security-gate;
}

.nc-security-officer::before {
  content: $nc-security-officer;
}

.nc-security::before {
  content: $nc-security;
}

.nc-segmentation::before {
  content: $nc-segmentation;
}

.nc-select::before {
  content: $nc-select;
}

.nc-selection::before {
  content: $nc-selection;
}

.nc-selfie-2::before {
  content: $nc-selfie-2;
}

.nc-selfie::before {
  content: $nc-selfie;
}

.nc-send-message::before {
  content: $nc-send-message;
}

.nc-send-to-phone::before {
  content: $nc-send-to-phone;
}

.nc-send::before {
  content: $nc-send;
}

.nc-sensor::before {
  content: $nc-sensor;
}

.nc-separate-branch::before {
  content: $nc-separate-branch;
}

.nc-separate-directions::before {
  content: $nc-separate-directions;
}

.nc-separate::before {
  content: $nc-separate;
}

.nc-server-rack::before {
  content: $nc-server-rack;
}

.nc-server::before {
  content: $nc-server;
}

.nc-settings-gear::before {
  content: $nc-settings-gear;
}

.nc-settings::before {
  content: $nc-settings;
}

.nc-setup-options::before {
  content: $nc-setup-options;
}

.nc-setup-preferences::before {
  content: $nc-setup-preferences;
}

.nc-setup-tools::before {
  content: $nc-setup-tools;
}

.nc-seven::before {
  content: $nc-seven;
}

.nc-sf-bridge::before {
  content: $nc-sf-bridge;
}

.nc-shaker::before {
  content: $nc-shaker;
}

.nc-shape-adjust::before {
  content: $nc-shape-adjust;
}

.nc-shape-arrow::before {
  content: $nc-shape-arrow;
}

.nc-shape-circle::before {
  content: $nc-shape-circle;
}

.nc-shape-custom::before {
  content: $nc-shape-custom;
}

.nc-shape-line::before {
  content: $nc-shape-line;
}

.nc-shape-oval::before {
  content: $nc-shape-oval;
}

.nc-shape-polygon-2::before {
  content: $nc-shape-polygon-2;
}

.nc-shape-polygon::before {
  content: $nc-shape-polygon;
}

.nc-shape-rectangle::before {
  content: $nc-shape-rectangle;
}

.nc-shape-square::before {
  content: $nc-shape-square;
}

.nc-shape-star::before {
  content: $nc-shape-star;
}

.nc-shape-triangle-2::before {
  content: $nc-shape-triangle-2;
}

.nc-shape-triangle::before {
  content: $nc-shape-triangle;
}

.nc-shapes::before {
  content: $nc-shapes;
}

.nc-share-2::before {
  content: $nc-share-2;
}

.nc-share-3::before {
  content: $nc-share-3;
}

.nc-share::before {
  content: $nc-share;
}

.nc-sharing::before {
  content: $nc-sharing;
}

.nc-shark-2::before {
  content: $nc-shark-2;
}

.nc-shark::before {
  content: $nc-shark;
}

.nc-sharpen::before {
  content: $nc-sharpen;
}

.nc-sharpener::before {
  content: $nc-sharpener;
}

.nc-sheep::before {
  content: $nc-sheep;
}

.nc-shell::before {
  content: $nc-shell;
}

.nc-shield::before {
  content: $nc-shield;
}

.nc-shinto::before {
  content: $nc-shinto;
}

.nc-shirt-business::before {
  content: $nc-shirt-business;
}

.nc-shirt-buttons::before {
  content: $nc-shirt-buttons;
}

.nc-shirt-neck::before {
  content: $nc-shirt-neck;
}

.nc-shirt::before {
  content: $nc-shirt;
}

.nc-shoe-man::before {
  content: $nc-shoe-man;
}

.nc-shoe-woman::before {
  content: $nc-shoe-woman;
}

.nc-shop-location::before {
  content: $nc-shop-location;
}

.nc-shop::before {
  content: $nc-shop;
}

.nc-shopping-bag::before {
  content: $nc-shopping-bag;
}

.nc-shopping-cart-2::before {
  content: $nc-shopping-cart-2;
}

.nc-shopping-cart::before {
  content: $nc-shopping-cart;
}

.nc-shopping-label::before {
  content: $nc-shopping-label;
}

.nc-shopping-tag::before {
  content: $nc-shopping-tag;
}

.nc-shorts::before {
  content: $nc-shorts;
}

.nc-shotgun::before {
  content: $nc-shotgun;
}

.nc-shovel::before {
  content: $nc-shovel;
}

.nc-show::before {
  content: $nc-show;
}

.nc-shower::before {
  content: $nc-shower;
}

.nc-shrimp::before {
  content: $nc-shrimp;
}

.nc-shuffle-2::before {
  content: $nc-shuffle-2;
}

.nc-shuffle::before {
  content: $nc-shuffle;
}

.nc-shuttle::before {
  content: $nc-shuttle;
}

.nc-shuttlecock::before {
  content: $nc-shuttlecock;
}

.nc-shy::before {
  content: $nc-shy;
}

.nc-sick::before {
  content: $nc-sick;
}

.nc-sickle::before {
  content: $nc-sickle;
}

.nc-sidebar::before {
  content: $nc-sidebar;
}

.nc-sign-board::before {
  content: $nc-sign-board;
}

.nc-sign-down::before {
  content: $nc-sign-down;
}

.nc-sign-left::before {
  content: $nc-sign-left;
}

.nc-sign-right::before {
  content: $nc-sign-right;
}

.nc-sign-up::before {
  content: $nc-sign-up;
}

.nc-sign::before {
  content: $nc-sign;
}

.nc-signal::before {
  content: $nc-signal;
}

.nc-signature::before {
  content: $nc-signature;
}

.nc-silly::before {
  content: $nc-silly;
}

.nc-sim-card::before {
  content: $nc-sim-card;
}

.nc-single-05::before {
  content: $nc-single-05;
}

.nc-single-bed::before {
  content: $nc-single-bed;
}

.nc-single-position::before {
  content: $nc-single-position;
}

.nc-sink-faucet::before {
  content: $nc-sink-faucet;
}

.nc-sink::before {
  content: $nc-sink;
}

.nc-six::before {
  content: $nc-six;
}

.nc-size-large::before {
  content: $nc-size-large;
}

.nc-size-medium::before {
  content: $nc-size-medium;
}

.nc-size-small::before {
  content: $nc-size-small;
}

.nc-skateboard-2::before {
  content: $nc-skateboard-2;
}

.nc-skateboard::before {
  content: $nc-skateboard;
}

.nc-skateboarding::before {
  content: $nc-skateboarding;
}

.nc-skating::before {
  content: $nc-skating;
}

.nc-skiing::before {
  content: $nc-skiing;
}

.nc-skipping-rope::before {
  content: $nc-skipping-rope;
}

.nc-skirt::before {
  content: $nc-skirt;
}

.nc-skull-2::before {
  content: $nc-skull-2;
}

.nc-skull::before {
  content: $nc-skull;
}

.nc-slacks-12::before {
  content: $nc-slacks-12;
}

.nc-slacks-13::before {
  content: $nc-slacks-13;
}

.nc-sleep-2::before {
  content: $nc-sleep-2;
}

.nc-sleep::before {
  content: $nc-sleep;
}

.nc-sleeping-baby::before {
  content: $nc-sleeping-baby;
}

.nc-slice::before {
  content: $nc-slice;
}

.nc-slide-left::before {
  content: $nc-slide-left;
}

.nc-slide-right::before {
  content: $nc-slide-right;
}

.nc-slider::before {
  content: $nc-slider;
}

.nc-slippers::before {
  content: $nc-slippers;
}

.nc-slot-machine::before {
  content: $nc-slot-machine;
}

.nc-sloth::before {
  content: $nc-sloth;
}

.nc-smart-house::before {
  content: $nc-smart-house;
}

.nc-smart::before {
  content: $nc-smart;
}

.nc-smartphone::before {
  content: $nc-smartphone;
}

.nc-smartwatch::before {
  content: $nc-smartwatch;
}

.nc-smile::before {
  content: $nc-smile;
}

.nc-smiling-face-glasses::before {
  content: $nc-smiling-face-glasses;
}

.nc-smiling-face-sunglasses::before {
  content: $nc-smiling-face-sunglasses;
}

.nc-smoking::before {
  content: $nc-smoking;
}

.nc-smoothie::before {
  content: $nc-smoothie;
}

.nc-snack::before {
  content: $nc-snack;
}

.nc-snake::before {
  content: $nc-snake;
}

.nc-sneeze::before {
  content: $nc-sneeze;
}

.nc-sniper-rifle::before {
  content: $nc-sniper-rifle;
}

.nc-snorkel-mask::before {
  content: $nc-snorkel-mask;
}

.nc-snow-ball::before {
  content: $nc-snow-ball;
}

.nc-snow::before {
  content: $nc-snow;
}

.nc-snowboard::before {
  content: $nc-snowboard;
}

.nc-snowboarding::before {
  content: $nc-snowboarding;
}

.nc-snowman-head::before {
  content: $nc-snowman-head;
}

.nc-snowman::before {
  content: $nc-snowman;
}

.nc-soap::before {
  content: $nc-soap;
}

.nc-soccer-ball::before {
  content: $nc-soccer-ball;
}

.nc-soccer-field::before {
  content: $nc-soccer-field;
}

.nc-soccer::before {
  content: $nc-soccer;
}

.nc-social-distancing::before {
  content: $nc-social-distancing;
}

.nc-social-sharing::before {
  content: $nc-social-sharing;
}

.nc-sock::before {
  content: $nc-sock;
}

.nc-socket-europe-1::before {
  content: $nc-socket-europe-1;
}

.nc-socket-europe-2::before {
  content: $nc-socket-europe-2;
}

.nc-socket-uk::before {
  content: $nc-socket-uk;
}

.nc-socket::before {
  content: $nc-socket;
}

.nc-sofa::before {
  content: $nc-sofa;
}

.nc-soft-drink::before {
  content: $nc-soft-drink;
}

.nc-soldier::before {
  content: $nc-soldier;
}

.nc-solider-helmet::before {
  content: $nc-solider-helmet;
}

.nc-sort-tool::before {
  content: $nc-sort-tool;
}

.nc-sound-wave::before {
  content: $nc-sound-wave;
}

.nc-sound::before {
  content: $nc-sound;
}

.nc-soundwave::before {
  content: $nc-soundwave;
}

.nc-soup::before {
  content: $nc-soup;
}

.nc-soy-sauce::before {
  content: $nc-soy-sauce;
}

.nc-spa-rocks::before {
  content: $nc-spa-rocks;
}

.nc-spa::before {
  content: $nc-spa;
}

.nc-spaceship::before {
  content: $nc-spaceship;
}

.nc-spades-suit::before {
  content: $nc-spades-suit;
}

.nc-speaker-2::before {
  content: $nc-speaker-2;
}

.nc-speaker::before {
  content: $nc-speaker;
}

.nc-speechless::before {
  content: $nc-speechless;
}

.nc-speedometer::before {
  content: $nc-speedometer;
}

.nc-sperm::before {
  content: $nc-sperm;
}

.nc-spider::before {
  content: $nc-spider;
}

.nc-spinning-bike::before {
  content: $nc-spinning-bike;
}

.nc-spiteful::before {
  content: $nc-spiteful;
}

.nc-split-branch::before {
  content: $nc-split-branch;
}

.nc-split-horizontal::before {
  content: $nc-split-horizontal;
}

.nc-split-vertical::before {
  content: $nc-split-vertical;
}

.nc-split::before {
  content: $nc-split;
}

.nc-sport-bag::before {
  content: $nc-sport-bag;
}

.nc-sport-mode::before {
  content: $nc-sport-mode;
}

.nc-sports-bra::before {
  content: $nc-sports-bra;
}

.nc-sports-fan::before {
  content: $nc-sports-fan;
}

.nc-sports-tank::before {
  content: $nc-sports-tank;
}

.nc-spray-bottle::before {
  content: $nc-spray-bottle;
}

.nc-spray-can::before {
  content: $nc-spray-can;
}

.nc-square-marker::before {
  content: $nc-square-marker;
}

.nc-square-pin::before {
  content: $nc-square-pin;
}

.nc-squares-anim-2::before {
  content: $nc-squares-anim-2;
}

.nc-squares-anim::before {
  content: $nc-squares-anim;
}

.nc-ssd::before {
  content: $nc-ssd;
}

.nc-stack::before {
  content: $nc-stack;
}

.nc-stadium::before {
  content: $nc-stadium;
}

.nc-stair-climber::before {
  content: $nc-stair-climber;
}

.nc-stairs::before {
  content: $nc-stairs;
}

.nc-stamp::before {
  content: $nc-stamp;
}

.nc-standing-man::before {
  content: $nc-standing-man;
}

.nc-standing-woman::before {
  content: $nc-standing-woman;
}

.nc-star-rate::before {
  content: $nc-star-rate;
}

.nc-star::before {
  content: $nc-star;
}

.nc-statistics::before {
  content: $nc-statistics;
}

.nc-stay-home::before {
  content: $nc-stay-home;
}

.nc-steak-2::before {
  content: $nc-steak-2;
}

.nc-steak::before {
  content: $nc-steak;
}

.nc-steam-iron::before {
  content: $nc-steam-iron;
}

.nc-steering-wheel::before {
  content: $nc-steering-wheel;
}

.nc-steps::before {
  content: $nc-steps;
}

.nc-stethoscope::before {
  content: $nc-stethoscope;
}

.nc-sticker::before {
  content: $nc-sticker;
}

.nc-stock-2::before {
  content: $nc-stock-2;
}

.nc-stock-market::before {
  content: $nc-stock-market;
}

.nc-stopwatch::before {
  content: $nc-stopwatch;
}

.nc-storage-hanger::before {
  content: $nc-storage-hanger;
}

.nc-storage-shelves::before {
  content: $nc-storage-shelves;
}

.nc-storage-unit::before {
  content: $nc-storage-unit;
}

.nc-store::before {
  content: $nc-store;
}

.nc-strawberry::before {
  content: $nc-strawberry;
}

.nc-stretch::before {
  content: $nc-stretch;
}

.nc-stretching::before {
  content: $nc-stretching;
}

.nc-strikethrough::before {
  content: $nc-strikethrough;
}

.nc-style::before {
  content: $nc-style;
}

.nc-submachine-gun::before {
  content: $nc-submachine-gun;
}

.nc-submarine::before {
  content: $nc-submarine;
}

.nc-subscript::before {
  content: $nc-subscript;
}

.nc-subtitles::before {
  content: $nc-subtitles;
}

.nc-sugar::before {
  content: $nc-sugar;
}

.nc-sun-cloud-drop::before {
  content: $nc-sun-cloud-drop;
}

.nc-sun-cloud-fog::before {
  content: $nc-sun-cloud-fog;
}

.nc-sun-cloud-hail::before {
  content: $nc-sun-cloud-hail;
}

.nc-sun-cloud-light::before {
  content: $nc-sun-cloud-light;
}

.nc-sun-cloud-rain::before {
  content: $nc-sun-cloud-rain;
}

.nc-sun-cloud-snow-54::before {
  content: $nc-sun-cloud-snow-54;
}

.nc-sun-cloud-snow-55::before {
  content: $nc-sun-cloud-snow-55;
}

.nc-sun-cloud::before {
  content: $nc-sun-cloud;
}

.nc-sun-fog-29::before {
  content: $nc-sun-fog-29;
}

.nc-sun-fog-30::before {
  content: $nc-sun-fog-30;
}

.nc-sun-fog-43::before {
  content: $nc-sun-fog-43;
}

.nc-sun::before {
  content: $nc-sun;
}

.nc-sunglasses-48::before {
  content: $nc-sunglasses-48;
}

.nc-sunglasses::before {
  content: $nc-sunglasses;
}

.nc-superscript::before {
  content: $nc-superscript;
}

.nc-support::before {
  content: $nc-support;
}

.nc-surfboard::before {
  content: $nc-surfboard;
}

.nc-surprise::before {
  content: $nc-surprise;
}

.nc-survey::before {
  content: $nc-survey;
}

.nc-sushi::before {
  content: $nc-sushi;
}

.nc-swap-horizontal::before {
  content: $nc-swap-horizontal;
}

.nc-swap-vertical::before {
  content: $nc-swap-vertical;
}

.nc-swimming-pool::before {
  content: $nc-swimming-pool;
}

.nc-swimming::before {
  content: $nc-swimming;
}

.nc-swimsuit::before {
  content: $nc-swimsuit;
}

.nc-swipe-bottom::before {
  content: $nc-swipe-bottom;
}

.nc-swipe-left::before {
  content: $nc-swipe-left;
}

.nc-swipe-right::before {
  content: $nc-swipe-right;
}

.nc-swipe-up::before {
  content: $nc-swipe-up;
}

.nc-swiss-knife::before {
  content: $nc-swiss-knife;
}

.nc-switches::before {
  content: $nc-switches;
}

.nc-sword::before {
  content: $nc-sword;
}

.nc-sync-devices::before {
  content: $nc-sync-devices;
}

.nc-syringe::before {
  content: $nc-syringe;
}

.nc-system-configuration::before {
  content: $nc-system-configuration;
}

.nc-system-preferences::before {
  content: $nc-system-preferences;
}

.nc-system-update::before {
  content: $nc-system-update;
}

.nc-t-add::before {
  content: $nc-t-add;
}

.nc-t-delete::before {
  content: $nc-t-delete;
}

.nc-t-remove::before {
  content: $nc-t-remove;
}

.nc-table-lamp::before {
  content: $nc-table-lamp;
}

.nc-table-layout::before {
  content: $nc-table-layout;
}

.nc-table-move::before {
  content: $nc-table-move;
}

.nc-table-slide::before {
  content: $nc-table-slide;
}

.nc-table-tennis-bat::before {
  content: $nc-table-tennis-bat;
}

.nc-table::before {
  content: $nc-table;
}

.nc-tablet-2::before {
  content: $nc-tablet-2;
}

.nc-tablet-charging::before {
  content: $nc-tablet-charging;
}

.nc-tablet-mobile::before {
  content: $nc-tablet-mobile;
}

.nc-tablet-toolbar::before {
  content: $nc-tablet-toolbar;
}

.nc-tablet::before {
  content: $nc-tablet;
}

.nc-tacos::before {
  content: $nc-tacos;
}

.nc-tactic::before {
  content: $nc-tactic;
}

.nc-tag-add::before {
  content: $nc-tag-add;
}

.nc-tag-check::before {
  content: $nc-tag-check;
}

.nc-tag-cut::before {
  content: $nc-tag-cut;
}

.nc-tag-loyalty::before {
  content: $nc-tag-loyalty;
}

.nc-tag-remove::before {
  content: $nc-tag-remove;
}

.nc-tag-sale::before {
  content: $nc-tag-sale;
}

.nc-tag::before {
  content: $nc-tag;
}

.nc-tags-stack::before {
  content: $nc-tags-stack;
}

.nc-take-off::before {
  content: $nc-take-off;
}

.nc-takeaway::before {
  content: $nc-takeaway;
}

.nc-taoism::before {
  content: $nc-taoism;
}

.nc-tap-01::before {
  content: $nc-tap-01;
}

.nc-tap-02::before {
  content: $nc-tap-02;
}

.nc-tape::before {
  content: $nc-tape;
}

.nc-target::before {
  content: $nc-target;
}

.nc-taxi::before {
  content: $nc-taxi;
}

.nc-tea-bag::before {
  content: $nc-tea-bag;
}

.nc-tea::before {
  content: $nc-tea;
}

.nc-teddy-bear::before {
  content: $nc-teddy-bear;
}

.nc-telephone::before {
  content: $nc-telephone;
}

.nc-telescope::before {
  content: $nc-telescope;
}

.nc-temperature-2::before {
  content: $nc-temperature-2;
}

.nc-temperature::before {
  content: $nc-temperature;
}

.nc-temple-25::before {
  content: $nc-temple-25;
}

.nc-temple::before {
  content: $nc-temple;
}

.nc-tennis-ball::before {
  content: $nc-tennis-ball;
}

.nc-tennis-racket::before {
  content: $nc-tennis-racket;
}

.nc-tennis::before {
  content: $nc-tennis;
}

.nc-terrace::before {
  content: $nc-terrace;
}

.nc-text-2::before {
  content: $nc-text-2;
}

.nc-text-size::before {
  content: $nc-text-size;
}

.nc-text::before {
  content: $nc-text;
}

.nc-texture::before {
  content: $nc-texture;
}

.nc-theater-curtains::before {
  content: $nc-theater-curtains;
}

.nc-theater::before {
  content: $nc-theater;
}

.nc-thermometer::before {
  content: $nc-thermometer;
}

.nc-three-dimensional-object::before {
  content: $nc-three-dimensional-object;
}

.nc-three-dimensional-world::before {
  content: $nc-three-dimensional-world;
}

.nc-three-way-direction::before {
  content: $nc-three-way-direction;
}

.nc-three::before {
  content: $nc-three;
}

.nc-thumb-down::before {
  content: $nc-thumb-down;
}

.nc-thumb-up::before {
  content: $nc-thumb-up;
}

.nc-ticket::before {
  content: $nc-ticket;
}

.nc-tie-01::before {
  content: $nc-tie-01;
}

.nc-tie-02::before {
  content: $nc-tie-02;
}

.nc-tie-bow::before {
  content: $nc-tie-bow;
}

.nc-time-alarm::before {
  content: $nc-time-alarm;
}

.nc-time-clock::before {
  content: $nc-time-clock;
}

.nc-time-machine::before {
  content: $nc-time-machine;
}

.nc-timeline::before {
  content: $nc-timeline;
}

.nc-timer::before {
  content: $nc-timer;
}

.nc-tnt-explosives::before {
  content: $nc-tnt-explosives;
}

.nc-toast::before {
  content: $nc-toast;
}

.nc-toaster::before {
  content: $nc-toaster;
}

.nc-todo::before {
  content: $nc-todo;
}

.nc-toggle::before {
  content: $nc-toggle;
}

.nc-toilet-paper::before {
  content: $nc-toilet-paper;
}

.nc-toilet::before {
  content: $nc-toilet;
}

.nc-toilette::before {
  content: $nc-toilette;
}

.nc-tomato::before {
  content: $nc-tomato;
}

.nc-tool-blur::before {
  content: $nc-tool-blur;
}

.nc-tool-hand::before {
  content: $nc-tool-hand;
}

.nc-tool-select::before {
  content: $nc-tool-select;
}

.nc-tooth::before {
  content: $nc-tooth;
}

.nc-towel-hanger::before {
  content: $nc-towel-hanger;
}

.nc-towel::before {
  content: $nc-towel;
}

.nc-track-delivery::before {
  content: $nc-track-delivery;
}

.nc-tracking::before {
  content: $nc-tracking;
}

.nc-tractor::before {
  content: $nc-tractor;
}

.nc-traffic::before {
  content: $nc-traffic;
}

.nc-train-speed::before {
  content: $nc-train-speed;
}

.nc-train::before {
  content: $nc-train;
}

.nc-tram::before {
  content: $nc-tram;
}

.nc-transaction::before {
  content: $nc-transaction;
}

.nc-transactions::before {
  content: $nc-transactions;
}

.nc-transform-2d::before {
  content: $nc-transform-2d;
}

.nc-transform-origin::before {
  content: $nc-transform-origin;
}

.nc-transform::before {
  content: $nc-transform;
}

.nc-translation::before {
  content: $nc-translation;
}

.nc-transparent::before {
  content: $nc-transparent;
}

.nc-trash-can::before {
  content: $nc-trash-can;
}

.nc-trash::before {
  content: $nc-trash;
}

.nc-travel-makeup-mirror::before {
  content: $nc-travel-makeup-mirror;
}

.nc-treadmill::before {
  content: $nc-treadmill;
}

.nc-treasure-map-21::before {
  content: $nc-treasure-map-21;
}

.nc-treasure-map-40::before {
  content: $nc-treasure-map-40;
}

.nc-tree-01::before {
  content: $nc-tree-01;
}

.nc-tree-02::before {
  content: $nc-tree-02;
}

.nc-tree-03::before {
  content: $nc-tree-03;
}

.nc-tree-ball::before {
  content: $nc-tree-ball;
}

.nc-tree::before {
  content: $nc-tree;
}

.nc-trend-down::before {
  content: $nc-trend-down;
}

.nc-trend-up::before {
  content: $nc-trend-up;
}

.nc-triangle-down::before {
  content: $nc-triangle-down;
}

.nc-triangle-left::before {
  content: $nc-triangle-left;
}

.nc-triangle-line-down::before {
  content: $nc-triangle-line-down;
}

.nc-triangle-line-left::before {
  content: $nc-triangle-line-left;
}

.nc-triangle-line-right::before {
  content: $nc-triangle-line-right;
}

.nc-triangle-line-up::before {
  content: $nc-triangle-line-up;
}

.nc-triangle-right::before {
  content: $nc-triangle-right;
}

.nc-triangle-sm-down::before {
  content: $nc-triangle-sm-down;
}

.nc-triangle-sm-left::before {
  content: $nc-triangle-sm-left;
}

.nc-triangle-sm-right::before {
  content: $nc-triangle-sm-right;
}

.nc-triangle-sm-up::before {
  content: $nc-triangle-sm-up;
}

.nc-triangle-up::before {
  content: $nc-triangle-up;
}

.nc-tripod::before {
  content: $nc-tripod;
}

.nc-trophy::before {
  content: $nc-trophy;
}

.nc-truck-front::before {
  content: $nc-truck-front;
}

.nc-trumpet::before {
  content: $nc-trumpet;
}

.nc-trunk::before {
  content: $nc-trunk;
}

.nc-tshirt-53::before {
  content: $nc-tshirt-53;
}

.nc-tshirt-54::before {
  content: $nc-tshirt-54;
}

.nc-tshirt-sport::before {
  content: $nc-tshirt-sport;
}

.nc-tty::before {
  content: $nc-tty;
}

.nc-turn-e::before {
  content: $nc-turn-e;
}

.nc-turn-n::before {
  content: $nc-turn-n;
}

.nc-turn-s::before {
  content: $nc-turn-s;
}

.nc-turn-w::before {
  content: $nc-turn-w;
}

.nc-turtle::before {
  content: $nc-turtle;
}

.nc-tv-stand::before {
  content: $nc-tv-stand;
}

.nc-tv::before {
  content: $nc-tv;
}

.nc-two-way-direction::before {
  content: $nc-two-way-direction;
}

.nc-two::before {
  content: $nc-two;
}

.nc-umbrella-13::before {
  content: $nc-umbrella-13;
}

.nc-umbrella-14::before {
  content: $nc-umbrella-14;
}

.nc-underline::before {
  content: $nc-underline;
}

.nc-underwear-man::before {
  content: $nc-underwear-man;
}

.nc-underwear::before {
  content: $nc-underwear;
}

.nc-undo::before {
  content: $nc-undo;
}

.nc-ungroup::before {
  content: $nc-ungroup;
}

.nc-unite-2::before {
  content: $nc-unite-2;
}

.nc-unite::before {
  content: $nc-unite;
}

.nc-unlink::before {
  content: $nc-unlink;
}

.nc-unlocked::before {
  content: $nc-unlocked;
}

.nc-up-arrow::before {
  content: $nc-up-arrow;
}

.nc-upload-data::before {
  content: $nc-upload-data;
}

.nc-upload-file::before {
  content: $nc-upload-file;
}

.nc-upload::before {
  content: $nc-upload;
}

.nc-upset-13::before {
  content: $nc-upset-13;
}

.nc-upset-14::before {
  content: $nc-upset-14;
}

.nc-url::before {
  content: $nc-url;
}

.nc-usb::before {
  content: $nc-usb;
}

.nc-user-frame-31::before {
  content: $nc-user-frame-31;
}

.nc-user-frame-32::before {
  content: $nc-user-frame-32;
}

.nc-user-frame-33::before {
  content: $nc-user-frame-33;
}

.nc-user::before {
  content: $nc-user;
}

.nc-users-mm::before {
  content: $nc-users-mm;
}

.nc-users-wm::before {
  content: $nc-users-wm;
}

.nc-users-ww::before {
  content: $nc-users-ww;
}

.nc-utility-bench::before {
  content: $nc-utility-bench;
}

.nc-vacuum-cleaner::before {
  content: $nc-vacuum-cleaner;
}

.nc-vampire::before {
  content: $nc-vampire;
}

.nc-vector::before {
  content: $nc-vector;
}

.nc-vegan::before {
  content: $nc-vegan;
}

.nc-ventilation::before {
  content: $nc-ventilation;
}

.nc-verified::before {
  content: $nc-verified;
}

.nc-vespa-front::before {
  content: $nc-vespa-front;
}

.nc-vespa::before {
  content: $nc-vespa;
}

.nc-vest-31::before {
  content: $nc-vest-31;
}

.nc-vest::before {
  content: $nc-vest;
}

.nc-vibrance::before {
  content: $nc-vibrance;
}

.nc-video-camera::before {
  content: $nc-video-camera;
}

.nc-video-gallery-2::before {
  content: $nc-video-gallery-2;
}

.nc-video-gallery::before {
  content: $nc-video-gallery;
}

.nc-video-off::before {
  content: $nc-video-off;
}

.nc-video-player::before {
  content: $nc-video-player;
}

.nc-video-playlist::before {
  content: $nc-video-playlist;
}

.nc-video::before {
  content: $nc-video;
}

.nc-view::before {
  content: $nc-view;
}

.nc-vignette::before {
  content: $nc-vignette;
}

.nc-vintage-computer::before {
  content: $nc-vintage-computer;
}

.nc-vintage-tv::before {
  content: $nc-vintage-tv;
}

.nc-violin::before {
  content: $nc-violin;
}

.nc-virtual-assistant-2::before {
  content: $nc-virtual-assistant-2;
}

.nc-virtual-assistant::before {
  content: $nc-virtual-assistant;
}

.nc-virtual-environment::before {
  content: $nc-virtual-environment;
}

.nc-virtual-reality::before {
  content: $nc-virtual-reality;
}

.nc-virus::before {
  content: $nc-virus;
}

.nc-voice-recognition::before {
  content: $nc-voice-recognition;
}

.nc-voice-record::before {
  content: $nc-voice-record;
}

.nc-volleyball-player::before {
  content: $nc-volleyball-player;
}

.nc-volleyball::before {
  content: $nc-volleyball;
}

.nc-volume-2::before {
  content: $nc-volume-2;
}

.nc-volume-down::before {
  content: $nc-volume-down;
}

.nc-volume-mute::before {
  content: $nc-volume-mute;
}

.nc-volume-off::before {
  content: $nc-volume-off;
}

.nc-volume-up::before {
  content: $nc-volume-up;
}

.nc-volume::before {
  content: $nc-volume;
}

.nc-vpn::before {
  content: $nc-vpn;
}

.nc-vr-controller::before {
  content: $nc-vr-controller;
}

.nc-vr-headset::before {
  content: $nc-vr-headset;
}

.nc-waffle::before {
  content: $nc-waffle;
}

.nc-walk::before {
  content: $nc-walk;
}

.nc-walking-aid::before {
  content: $nc-walking-aid;
}

.nc-walking-support::before {
  content: $nc-walking-support;
}

.nc-wallet-43::before {
  content: $nc-wallet-43;
}

.nc-wallet-44::before {
  content: $nc-wallet-44;
}

.nc-wallet-90::before {
  content: $nc-wallet-90;
}

.nc-wallet::before {
  content: $nc-wallet;
}

.nc-wand-11::before {
  content: $nc-wand-11;
}

.nc-wardrobe-2::before {
  content: $nc-wardrobe-2;
}

.nc-wardrobe-3::before {
  content: $nc-wardrobe-3;
}

.nc-wardrobe-4::before {
  content: $nc-wardrobe-4;
}

.nc-wardrobe::before {
  content: $nc-wardrobe;
}

.nc-warning-sign::before {
  content: $nc-warning-sign;
}

.nc-wash-30::before {
  content: $nc-wash-30;
}

.nc-wash-60::before {
  content: $nc-wash-60;
}

.nc-wash-90::before {
  content: $nc-wash-90;
}

.nc-wash-hand::before {
  content: $nc-wash-hand;
}

.nc-wash-hands::before {
  content: $nc-wash-hands;
}

.nc-washing-fluid::before {
  content: $nc-washing-fluid;
}

.nc-washing-machine::before {
  content: $nc-washing-machine;
}

.nc-waste-danger::before {
  content: $nc-waste-danger;
}

.nc-waste-recycling::before {
  content: $nc-waste-recycling;
}

.nc-waste::before {
  content: $nc-waste;
}

.nc-watch-2::before {
  content: $nc-watch-2;
}

.nc-watch-dev::before {
  content: $nc-watch-dev;
}

.nc-watch-heart::before {
  content: $nc-watch-heart;
}

.nc-watch-heartbeat::before {
  content: $nc-watch-heartbeat;
}

.nc-watch::before {
  content: $nc-watch;
}

.nc-water-aerobics::before {
  content: $nc-water-aerobics;
}

.nc-water-hand::before {
  content: $nc-water-hand;
}

.nc-water-polo-ball::before {
  content: $nc-water-polo-ball;
}

.nc-water-polo::before {
  content: $nc-water-polo;
}

.nc-water-sink::before {
  content: $nc-water-sink;
}

.nc-water-surface::before {
  content: $nc-water-surface;
}

.nc-water-wave::before {
  content: $nc-water-wave;
}

.nc-water::before {
  content: $nc-water;
}

.nc-watermelon::before {
  content: $nc-watermelon;
}

.nc-wc::before {
  content: $nc-wc;
}

.nc-web-design::before {
  content: $nc-web-design;
}

.nc-web-hyperlink::before {
  content: $nc-web-hyperlink;
}

.nc-web-link::before {
  content: $nc-web-link;
}

.nc-web-url::before {
  content: $nc-web-url;
}

.nc-webcam-2::before {
  content: $nc-webcam-2;
}

.nc-webcam::before {
  content: $nc-webcam;
}

.nc-webpage::before {
  content: $nc-webpage;
}

.nc-wedding-arch::before {
  content: $nc-wedding-arch;
}

.nc-wedding-cake::before {
  content: $nc-wedding-cake;
}

.nc-wedding-ring::before {
  content: $nc-wedding-ring;
}

.nc-wedding-rings::before {
  content: $nc-wedding-rings;
}

.nc-weed::before {
  content: $nc-weed;
}

.nc-weight-bench::before {
  content: $nc-weight-bench;
}

.nc-weight-gain::before {
  content: $nc-weight-gain;
}

.nc-weight-loss::before {
  content: $nc-weight-loss;
}

.nc-weight-plate::before {
  content: $nc-weight-plate;
}

.nc-weight-scale::before {
  content: $nc-weight-scale;
}

.nc-what::before {
  content: $nc-what;
}

.nc-wheel-2::before {
  content: $nc-wheel-2;
}

.nc-wheel::before {
  content: $nc-wheel;
}

.nc-wheelchair-2::before {
  content: $nc-wheelchair-2;
}

.nc-wheelchair-ramp::before {
  content: $nc-wheelchair-ramp;
}

.nc-wheelchair::before {
  content: $nc-wheelchair;
}

.nc-whisk::before {
  content: $nc-whisk;
}

.nc-whiskers::before {
  content: $nc-whiskers;
}

.nc-whistle::before {
  content: $nc-whistle;
}

.nc-white-balance::before {
  content: $nc-white-balance;
}

.nc-white-house::before {
  content: $nc-white-house;
}

.nc-widget::before {
  content: $nc-widget;
}

.nc-wifi-2::before {
  content: $nc-wifi-2;
}

.nc-wifi-off::before {
  content: $nc-wifi-off;
}

.nc-wifi-protected::before {
  content: $nc-wifi-protected;
}

.nc-wifi-router::before {
  content: $nc-wifi-router;
}

.nc-wifi::before {
  content: $nc-wifi;
}

.nc-wind-2::before {
  content: $nc-wind-2;
}

.nc-wind::before {
  content: $nc-wind;
}

.nc-window-add::before {
  content: $nc-window-add;
}

.nc-window-code::before {
  content: $nc-window-code;
}

.nc-window-delete::before {
  content: $nc-window-delete;
}

.nc-window-dev::before {
  content: $nc-window-dev;
}

.nc-window-maximize::before {
  content: $nc-window-maximize;
}

.nc-window-minimize::before {
  content: $nc-window-minimize;
}

.nc-window-paragraph::before {
  content: $nc-window-paragraph;
}

.nc-window-responsive::before {
  content: $nc-window-responsive;
}

.nc-window::before {
  content: $nc-window;
}

.nc-windsurfing::before {
  content: $nc-windsurfing;
}

.nc-wine-list::before {
  content: $nc-wine-list;
}

.nc-wink-06::before {
  content: $nc-wink-06;
}

.nc-wink-11::before {
  content: $nc-wink-11;
}

.nc-wink-69::before {
  content: $nc-wink-69;
}

.nc-winner::before {
  content: $nc-winner;
}

.nc-wireframe::before {
  content: $nc-wireframe;
}

.nc-wireless-charging::before {
  content: $nc-wireless-charging;
}

.nc-witch-hat::before {
  content: $nc-witch-hat;
}

.nc-wolf::before {
  content: $nc-wolf;
}

.nc-woman-2::before {
  content: $nc-woman-2;
}

.nc-woman-21::before {
  content: $nc-woman-21;
}

.nc-woman-24::before {
  content: $nc-woman-24;
}

.nc-woman-down::before {
  content: $nc-woman-down;
}

.nc-woman-man::before {
  content: $nc-woman-man;
}

.nc-woman-up-front::before {
  content: $nc-woman-up-front;
}

.nc-woman-up::before {
  content: $nc-woman-up;
}

.nc-wood::before {
  content: $nc-wood;
}

.nc-wool-ball::before {
  content: $nc-wool-ball;
}

.nc-workout-plan::before {
  content: $nc-workout-plan;
}

.nc-world-2::before {
  content: $nc-world-2;
}

.nc-world-marker::before {
  content: $nc-world-marker;
}

.nc-world-pin::before {
  content: $nc-world-pin;
}

.nc-world::before {
  content: $nc-world;
}

.nc-wrench-tool::before {
  content: $nc-wrench-tool;
}

.nc-wrench::before {
  content: $nc-wrench;
}

.nc-xmas-sock::before {
  content: $nc-xmas-sock;
}

.nc-yoga::before {
  content: $nc-yoga;
}

.nc-yogurt::before {
  content: $nc-yogurt;
}

.nc-zero::before {
  content: $nc-zero;
}

.nc-zipped-file::before {
  content: $nc-zipped-file;
}

.nc-zombie::before {
  content: $nc-zombie;
}

.nc-zoom-e::before {
  content: $nc-zoom-e;
}

.nc-zoom-in::before {
  content: $nc-zoom-in;
}

.nc-zoom-out::before {
  content: $nc-zoom-out;
}

.nc-zoom::before {
  content: $nc-zoom;
}

