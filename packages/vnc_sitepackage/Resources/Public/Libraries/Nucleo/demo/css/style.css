/*--------------------------------

Nucleo Web Font
Generated using nucleoapp.com

-------------------------------- */
@font-face {
  font-family: 'Nucleo';
  src: url('../../fonts/Nucleo.eot');
  src: url('../../fonts/Nucleo.eot') format('embedded-opentype'), url('../../fonts/Nucleo.woff2') format('woff2'), url('../../fonts/Nucleo.woff') format('woff'), url('../../fonts/Nucleo.ttf') format('truetype'), url('../../fonts/Nucleo.svg') format('svg');
  font-weight: normal;
  font-style: normal;
}

/* --------------------------------

Reset

-------------------------------- */

*, *::after, *::before {
  box-sizing: inherit;
}

* {
  font: inherit;
}

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video, hr {
  margin: 0;
  padding: 0;
  border: 0;
}

html {
  box-sizing: border-box;
}

body {
  background-color: white;
  font-family: system-ui, sans-serif;
  color: hsl(240, 4%, 20%);
  padding: 1em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section, main, form legend {
  display: block;
}

ol, ul {
  list-style: none;
}

button, input, textarea, select {
  margin: 0;
}

a {
  color: hsl(230, 93%, 66%);
}

/* --------------------------------

Main components

-------------------------------- */
header {
  text-align: center;
  margin: 3em auto;
}

header h1 {
  font-size: 2.6rem;
  font-weight: 600;
}

header p {
  font-size: 1rem;
  margin-top: 1em;
  color: hsla(0, 0%, 0%, 0.5);
}

ul {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

ul li {
  border-radius: .4em;
  transition: background-color .2s;
  user-select: none;
  overflow: hidden;
  text-align: center;
  padding: 1em;
}

ul li:hover {
  background: hsla(0, 0%, 0%, 0.05);
}

ul p, ul em, ul input {
  display: block;
  font-size: 0.75rem;
  color: hsla(0, 0%, 0%, 0.5);
  user-select: auto;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  max-width: 6rem;
}

ul p {
  padding: 8px 0 4px;
}

ul p::selection, ul em::selection {
  background: hsl(230, 93%, 66%);
  color: #fff;
}

ul p::-moz-selection, ul em::-moz-selection {
  background: hsl(230, 93%, 66%);
  color: #fff;
}

ul em {
  margin-bottom: 4px;
}

ul em::before {
  content: '[';
}
ul em::after {
  content: ']';
}

ul input {
  text-align: center;
  background: transparent;
  border: none;
  box-shadow: none;
  outline: none;
  font-family: auto;
}

/* --------------------------------

icons

-------------------------------- */
.icon {
  display: inline-block;
  font: normal normal normal 32px/1 'Nucleo';
  speak: none;
  text-transform: none;
  /* Better Font Rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/*------------------------
  font icons
-------------------------*/

.nc-2x-drag-down::before {
  content: "\ea01";
}

.nc-2x-drag-up::before {
  content: "\ea03";
}

.nc-2x-swipe-down::before {
  content: "\ea05";
}

.nc-2x-swipe-left::before {
  content: "\ea02";
}

.nc-2x-swipe-right::before {
  content: "\ea04";
}

.nc-2x-swipe-up::before {
  content: "\ea06";
}

.nc-2x-tap::before {
  content: "\ea10";
}

.nc-3d-29::before {
  content: "\ea07";
}

.nc-3d-glasses::before {
  content: "\ea08";
}

.nc-3d-model::before {
  content: "\ea09";
}

.nc-3d-printing::before {
  content: "\ea0a";
}

.nc-3x-swipe-left::before {
  content: "\ea0b";
}

.nc-3x-swipe-right::before {
  content: "\ea0c";
}

.nc-3x-swipe-up::before {
  content: "\ea0d";
}

.nc-3x-tap::before {
  content: "\ea0e";
}

.nc-4x-swipe-left::before {
  content: "\ea0f";
}

.nc-4x-swipe-right::before {
  content: "\ea11";
}

.nc-4x-swipe-up::before {
  content: "\ea12";
}

.nc-a-add::before {
  content: "\ea13";
}

.nc-a-chart::before {
  content: "\ea14";
}

.nc-a-chat::before {
  content: "\ea15";
}

.nc-a-check::before {
  content: "\ea16";
}

.nc-a-delete::before {
  content: "\ea17";
}

.nc-a-edit::before {
  content: "\ea18";
}

.nc-a-heart::before {
  content: "\ea19";
}

.nc-a-location::before {
  content: "\ea1a";
}

.nc-a-remove::before {
  content: "\ea1b";
}

.nc-a-search::before {
  content: "\ea1c";
}

.nc-a-security::before {
  content: "\ea1d";
}

.nc-a-share::before {
  content: "\ea1e";
}

.nc-a-star::before {
  content: "\ea1f";
}

.nc-a-sync::before {
  content: "\ea20";
}

.nc-a-tag-add::before {
  content: "\ea21";
}

.nc-a-tag-remove::before {
  content: "\ea22";
}

.nc-a-tag::before {
  content: "\ea24";
}

.nc-a-time::before {
  content: "\ea23";
}

.nc-abc::before {
  content: "\ea25";
}

.nc-access-key::before {
  content: "\ea26";
}

.nc-accessibility-lift::before {
  content: "\ea27";
}

.nc-accessibility::before {
  content: "\ea28";
}

.nc-account::before {
  content: "\ea29";
}

.nc-acorn::before {
  content: "\ea2a";
}

.nc-active-38::before {
  content: "\ea2b";
}

.nc-active-40::before {
  content: "\ea2c";
}

.nc-adaptive-bike::before {
  content: "\ea2d";
}

.nc-add-27::before {
  content: "\ea2e";
}

.nc-add-29::before {
  content: "\ea2f";
}

.nc-add-fav::before {
  content: "\ea30";
}

.nc-add-favorite::before {
  content: "\ea31";
}

.nc-add-like::before {
  content: "\ea32";
}

.nc-add-notification::before {
  content: "\ea33";
}

.nc-add-to-cart-2::before {
  content: "\ea34";
}

.nc-add-to-cart::before {
  content: "\ea35";
}

.nc-add::before {
  content: "\ea36";
}

.nc-adult-content::before {
  content: "\ea37";
}

.nc-agenda-bookmark::before {
  content: "\ea38";
}

.nc-agenda::before {
  content: "\ea39";
}

.nc-ai::before {
  content: "\ea3a";
}

.nc-air-baloon::before {
  content: "\ea3b";
}

.nc-air-bomb::before {
  content: "\ea3c";
}

.nc-air-conditioner::before {
  content: "\ea3d";
}

.nc-airbag::before {
  content: "\ea3e";
}

.nc-airplane::before {
  content: "\ea3f";
}

.nc-airport-trolley::before {
  content: "\ea40";
}

.nc-airport::before {
  content: "\ea41";
}

.nc-alarm-add::before {
  content: "\ea42";
}

.nc-alarm-disable::before {
  content: "\ea43";
}

.nc-alarm::before {
  content: "\ea44";
}

.nc-album::before {
  content: "\ea45";
}

.nc-alcohol::before {
  content: "\ea46";
}

.nc-algorithm::before {
  content: "\ea47";
}

.nc-alien-29::before {
  content: "\ea48";
}

.nc-alien-33::before {
  content: "\ea49";
}

.nc-align-bottom::before {
  content: "\ea4a";
}

.nc-align-center-horizontal::before {
  content: "\ea4b";
}

.nc-align-center-vertical::before {
  content: "\ea4c";
}

.nc-align-center::before {
  content: "\ea4d";
}

.nc-align-justify::before {
  content: "\ea4e";
}

.nc-align-left-2::before {
  content: "\ea4f";
}

.nc-align-left::before {
  content: "\ea50";
}

.nc-align-right-2::before {
  content: "\ea51";
}

.nc-align-right::before {
  content: "\ea52";
}

.nc-align-top::before {
  content: "\ea53";
}

.nc-all-directions::before {
  content: "\ea54";
}

.nc-alpha-order::before {
  content: "\ea55";
}

.nc-ambulance::before {
  content: "\ea56";
}

.nc-ampersand::before {
  content: "\ea57";
}

.nc-analytics::before {
  content: "\ea58";
}

.nc-anchor::before {
  content: "\ea59";
}

.nc-android::before {
  content: "\ea5a";
}

.nc-angle::before {
  content: "\ea5b";
}

.nc-angry-10::before {
  content: "\ea5c";
}

.nc-angry-44::before {
  content: "\ea5d";
}

.nc-animation-14::before {
  content: "\ea5e";
}

.nc-animation-31::before {
  content: "\ea5f";
}

.nc-animation-32::before {
  content: "\ea60";
}

.nc-antenna::before {
  content: "\ea61";
}

.nc-anti-shake::before {
  content: "\ea62";
}

.nc-apartment::before {
  content: "\ea63";
}

.nc-aperture::before {
  content: "\ea64";
}

.nc-api::before {
  content: "\ea65";
}

.nc-app-services::before {
  content: "\ea66";
}

.nc-app-store::before {
  content: "\ea67";
}

.nc-app::before {
  content: "\ea68";
}

.nc-apple-2::before {
  content: "\ea69";
}

.nc-apple::before {
  content: "\ea6a";
}

.nc-appointment::before {
  content: "\ea6b";
}

.nc-apps::before {
  content: "\ea6c";
}

.nc-apron::before {
  content: "\ea6d";
}

.nc-arcade::before {
  content: "\ea6e";
}

.nc-archer::before {
  content: "\ea6f";
}

.nc-archery-target::before {
  content: "\ea70";
}

.nc-archery::before {
  content: "\ea71";
}

.nc-archive-check::before {
  content: "\ea72";
}

.nc-archive-content::before {
  content: "\ea73";
}

.nc-archive-doc-check::before {
  content: "\ea74";
}

.nc-archive-doc::before {
  content: "\ea75";
}

.nc-archive-drawer::before {
  content: "\ea76";
}

.nc-archive-file-check::before {
  content: "\ea77";
}

.nc-archive-file::before {
  content: "\ea78";
}

.nc-archive::before {
  content: "\ea79";
}

.nc-armchair::before {
  content: "\ea7a";
}

.nc-armor::before {
  content: "\ea7b";
}

.nc-army::before {
  content: "\ea7c";
}

.nc-arrow-bottom-left::before {
  content: "\ea7d";
}

.nc-arrow-bottom-right::before {
  content: "\ea7e";
}

.nc-arrow-down-2::before {
  content: "\ea7f";
}

.nc-arrow-down-3::before {
  content: "\ea80";
}

.nc-arrow-down::before {
  content: "\ea81";
}

.nc-arrow-e::before {
  content: "\ea82";
}

.nc-arrow-left-2::before {
  content: "\ea83";
}

.nc-arrow-left-3::before {
  content: "\ea84";
}

.nc-arrow-left::before {
  content: "\ea85";
}

.nc-arrow-n::before {
  content: "\ea86";
}

.nc-arrow-right-2::before {
  content: "\ea87";
}

.nc-arrow-right-3::before {
  content: "\ea88";
}

.nc-arrow-right::before {
  content: "\ea89";
}

.nc-arrow-s::before {
  content: "\ea8a";
}

.nc-arrow-sm-down::before {
  content: "\ea8b";
}

.nc-arrow-sm-left::before {
  content: "\ea8c";
}

.nc-arrow-sm-right::before {
  content: "\ea8d";
}

.nc-arrow-tool::before {
  content: "\ea8e";
}

.nc-arrow-top-left::before {
  content: "\ea8f";
}

.nc-arrow-top-right::before {
  content: "\ea90";
}

.nc-arrow-up-2::before {
  content: "\ea91";
}

.nc-arrow-up-3::before {
  content: "\ea92";
}

.nc-arrow-up::before {
  content: "\ea93";
}

.nc-arrow-w::before {
  content: "\ea94";
}

.nc-arrows-expand-2::before {
  content: "\ea95";
}

.nc-arrows-expand::before {
  content: "\ea96";
}

.nc-arrows-fullscreen-2::before {
  content: "\ea97";
}

.nc-arrows-fullscreen::before {
  content: "\ea98";
}

.nc-arrows-maximize-2::before {
  content: "\ea99";
}

.nc-arrows-maximize::before {
  content: "\ea9a";
}

.nc-arrows-opposite-directions::before {
  content: "\ea9b";
}

.nc-arrows-same-direction::before {
  content: "\ea9c";
}

.nc-artboard::before {
  content: "\ea9d";
}

.nc-artificial-brain::before {
  content: "\ea9e";
}

.nc-artificial-intelligence::before {
  content: "\ea9f";
}

.nc-assault-rifle::before {
  content: "\eaa0";
}

.nc-astronaut::before {
  content: "\eaa1";
}

.nc-astronomy::before {
  content: "\eaa2";
}

.nc-at-sign-2::before {
  content: "\eaa3";
}

.nc-at-sign::before {
  content: "\eaa4";
}

.nc-athletics::before {
  content: "\eaa5";
}

.nc-atm::before {
  content: "\eaa6";
}

.nc-atom::before {
  content: "\eaa7";
}

.nc-attach::before {
  content: "\eaa8";
}

.nc-attachment::before {
  content: "\eaa9";
}

.nc-aubergine::before {
  content: "\eaaa";
}

.nc-audio-description::before {
  content: "\eaab";
}

.nc-audio-jack::before {
  content: "\eaac";
}

.nc-audio-mixer::before {
  content: "\eaad";
}

.nc-augmented-reality::before {
  content: "\eaae";
}

.nc-auto-flash-2::before {
  content: "\eaaf";
}

.nc-auto-flash::before {
  content: "\eab0";
}

.nc-auto-focus::before {
  content: "\eab1";
}

.nc-automated-logistics::before {
  content: "\eab2";
}

.nc-avocado::before {
  content: "\eab3";
}

.nc-award-49::before {
  content: "\eab4";
}

.nc-award::before {
  content: "\eab5";
}

.nc-axe::before {
  content: "\eab6";
}

.nc-b-add::before {
  content: "\eab7";
}

.nc-b-chart::before {
  content: "\eab8";
}

.nc-b-check::before {
  content: "\eab9";
}

.nc-b-comment::before {
  content: "\eaba";
}

.nc-b-eye::before {
  content: "\eabb";
}

.nc-b-location::before {
  content: "\eabc";
}

.nc-b-love::before {
  content: "\eabd";
}

.nc-b-meeting::before {
  content: "\eabe";
}

.nc-b-remove::before {
  content: "\eabf";
}

.nc-b-security::before {
  content: "\eac0";
}

.nc-baby-bottle::before {
  content: "\eac1";
}

.nc-baby-car-seat::before {
  content: "\eac2";
}

.nc-baby-clothes::before {
  content: "\eac3";
}

.nc-baby-monitor::before {
  content: "\eac4";
}

.nc-baby-stroller::before {
  content: "\eac5";
}

.nc-baby::before {
  content: "\eac6";
}

.nc-back-arrow::before {
  content: "\eac7";
}

.nc-background::before {
  content: "\eac8";
}

.nc-backpack-2::before {
  content: "\eac9";
}

.nc-backpack-57::before {
  content: "\eaca";
}

.nc-backpack-58::before {
  content: "\eacb";
}

.nc-backpack::before {
  content: "\eacc";
}

.nc-backup::before {
  content: "\eacd";
}

.nc-backward::before {
  content: "\eace";
}

.nc-bacon::before {
  content: "\eacf";
}

.nc-badge-13::before {
  content: "\ead0";
}

.nc-badge-14::before {
  content: "\ead1";
}

.nc-badge-15::before {
  content: "\ead2";
}

.nc-badge::before {
  content: "\ead3";
}

.nc-bag-16::before {
  content: "\ead4";
}

.nc-bag-17::before {
  content: "\ead5";
}

.nc-bag-20::before {
  content: "\ead6";
}

.nc-bag-21::before {
  content: "\ead7";
}

.nc-bag-22::before {
  content: "\ead8";
}

.nc-bag-49::before {
  content: "\eadb";
}

.nc-bag-50::before {
  content: "\ead9";
}

.nc-bag-add-18::before {
  content: "\eada";
}

.nc-bag-add-21::before {
  content: "\eadc";
}

.nc-bag-delivery::before {
  content: "\eadd";
}

.nc-bag-edit::before {
  content: "\eade";
}

.nc-bag-remove-19::before {
  content: "\eadf";
}

.nc-bag-remove-22::before {
  content: "\eae0";
}

.nc-bag-time::before {
  content: "\eae1";
}

.nc-bag::before {
  content: "\eae2";
}

.nc-baggage-collection::before {
  content: "\eae3";
}

.nc-baggage-scale::before {
  content: "\eae4";
}

.nc-baguette::before {
  content: "\eae5";
}

.nc-bahai::before {
  content: "\eae6";
}

.nc-bakery::before {
  content: "\eae7";
}

.nc-balance::before {
  content: "\eae8";
}

.nc-baloon::before {
  content: "\eae9";
}

.nc-bamboo::before {
  content: "\eaea";
}

.nc-ban::before {
  content: "\eaeb";
}

.nc-banana::before {
  content: "\eaec";
}

.nc-bank-statement::before {
  content: "\eaed";
}

.nc-barbecue-15::before {
  content: "\eaee";
}

.nc-barbecue-tools::before {
  content: "\eaef";
}

.nc-barbecue::before {
  content: "\eaf0";
}

.nc-barbell::before {
  content: "\eaf1";
}

.nc-barbershop::before {
  content: "\eaf2";
}

.nc-barcode-qr::before {
  content: "\eaf3";
}

.nc-barcode-scan::before {
  content: "\eaf4";
}

.nc-barcode::before {
  content: "\eaf7";
}

.nc-bars-anim-3::before {
  content: "\eaf5";
}

.nc-bars-anim::before {
  content: "\eaf6";
}

.nc-baseball-bat::before {
  content: "\eaf8";
}

.nc-baseball-pitch::before {
  content: "\eaf9";
}

.nc-baseball-player::before {
  content: "\eafa";
}

.nc-baseball::before {
  content: "\eb03";
}

.nc-basket-add::before {
  content: "\eafb";
}

.nc-basket-edit::before {
  content: "\eafc";
}

.nc-basket-favorite::before {
  content: "\eafd";
}

.nc-basket-remove::before {
  content: "\eafe";
}

.nc-basket-search::before {
  content: "\eaff";
}

.nc-basket-share::before {
  content: "\eb00";
}

.nc-basket-simple-add::before {
  content: "\eb01";
}

.nc-basket-simple-remove::before {
  content: "\eb02";
}

.nc-basket-simple::before {
  content: "\eb04";
}

.nc-basket-update::before {
  content: "\eb05";
}

.nc-basket::before {
  content: "\eb06";
}

.nc-basketball-board::before {
  content: "\eb09";
}

.nc-basketball-player::before {
  content: "\eb07";
}

.nc-basketball-ring::before {
  content: "\eb08";
}

.nc-basketball::before {
  content: "\eb13";
}

.nc-bat::before {
  content: "\eb0a";
}

.nc-bath-faucet::before {
  content: "\eb0b";
}

.nc-bathroom-cabinet::before {
  content: "\eb0c";
}

.nc-bathtub::before {
  content: "\eb0d";
}

.nc-battery-charging::before {
  content: "\eb0e";
}

.nc-battery-level::before {
  content: "\eb0f";
}

.nc-battery-low::before {
  content: "\eb10";
}

.nc-battery-power::before {
  content: "\eb11";
}

.nc-battery-status::before {
  content: "\eb12";
}

.nc-battery::before {
  content: "\eb14";
}

.nc-beach-bat::before {
  content: "\eb15";
}

.nc-bear-2::before {
  content: "\eb16";
}

.nc-bear::before {
  content: "\eb17";
}

.nc-beard::before {
  content: "\eb18";
}

.nc-bed::before {
  content: "\eb19";
}

.nc-bedroom::before {
  content: "\eb1a";
}

.nc-bee::before {
  content: "\eb1b";
}

.nc-beer-95::before {
  content: "\eb1c";
}

.nc-beer-96::before {
  content: "\eb1d";
}

.nc-bell::before {
  content: "\eb1e";
}

.nc-belt::before {
  content: "\eb1f";
}

.nc-berlin::before {
  content: "\eb20";
}

.nc-beverage::before {
  content: "\eb21";
}

.nc-bicep::before {
  content: "\eb22";
}

.nc-big-eyes::before {
  content: "\eb23";
}

.nc-big-smile::before {
  content: "\eb24";
}

.nc-bigmouth::before {
  content: "\eb25";
}

.nc-bike-bmx::before {
  content: "\eb26";
}

.nc-bike::before {
  content: "\eb27";
}

.nc-bikini::before {
  content: "\eb29";
}

.nc-bill::before {
  content: "\eb28";
}

.nc-billboard::before {
  content: "\eb2a";
}

.nc-billiard-ball::before {
  content: "\eb2b";
}

.nc-bin::before {
  content: "\eb2c";
}

.nc-binoculars::before {
  content: "\eb2d";
}

.nc-biochemistry::before {
  content: "\eb2e";
}

.nc-biology::before {
  content: "\eb2f";
}

.nc-biscuit::before {
  content: "\eb30";
}

.nc-bitcoin::before {
  content: "\eb34";
}

.nc-bleah::before {
  content: "\eb31";
}

.nc-blend::before {
  content: "\eb32";
}

.nc-blender::before {
  content: "\eb33";
}

.nc-blindness::before {
  content: "\eb35";
}

.nc-block-down::before {
  content: "\eb36";
}

.nc-block-left::before {
  content: "\eb37";
}

.nc-block-right::before {
  content: "\eb38";
}

.nc-block-up::before {
  content: "\eb39";
}

.nc-block::before {
  content: "\eb3a";
}

.nc-blockchain::before {
  content: "\eb3b";
}

.nc-blog::before {
  content: "\eb3c";
}

.nc-blueberries::before {
  content: "\eb3d";
}

.nc-blueprint::before {
  content: "\eb3e";
}

.nc-bluetooth::before {
  content: "\eb3f";
}

.nc-board-2::before {
  content: "\eb40";
}

.nc-board-27::before {
  content: "\eb41";
}

.nc-board-28::before {
  content: "\eb42";
}

.nc-board-29::before {
  content: "\eb43";
}

.nc-board-30::before {
  content: "\eb44";
}

.nc-board-51::before {
  content: "\eb45";
}

.nc-board-game::before {
  content: "\eb46";
}

.nc-board::before {
  content: "\eb47";
}

.nc-boat-front::before {
  content: "\eb48";
}

.nc-boat-small-02::before {
  content: "\eb49";
}

.nc-boat-small-03::before {
  content: "\eb4a";
}

.nc-boat::before {
  content: "\eb4b";
}

.nc-body-back::before {
  content: "\eb4c";
}

.nc-body-butt::before {
  content: "\eb4d";
}

.nc-body-cream::before {
  content: "\eb50";
}

.nc-bodybuilder::before {
  content: "\eb4e";
}

.nc-boiling-water::before {
  content: "\eb4f";
}

.nc-bold::before {
  content: "\eb51";
}

.nc-bolt::before {
  content: "\eb52";
}

.nc-bomb::before {
  content: "\eb53";
}

.nc-bones::before {
  content: "\eb54";
}

.nc-book-39::before {
  content: "\eb55";
}

.nc-book-bookmark-2::before {
  content: "\eb56";
}

.nc-book-bookmark::before {
  content: "\eb57";
}

.nc-book-open-2::before {
  content: "\eb58";
}

.nc-book-open::before {
  content: "\eb59";
}

.nc-book::before {
  content: "\eb5a";
}

.nc-bookmark-add-2::before {
  content: "\eb5b";
}

.nc-bookmark-add::before {
  content: "\eb5c";
}

.nc-bookmark-delete-2::before {
  content: "\eb5d";
}

.nc-bookmark-delete::before {
  content: "\eb5e";
}

.nc-bookmark::before {
  content: "\eb5f";
}

.nc-bookmarks::before {
  content: "\eb60";
}

.nc-books-46::before {
  content: "\eb61";
}

.nc-books::before {
  content: "\eb62";
}

.nc-boot-2::before {
  content: "\eb63";
}

.nc-boot-woman::before {
  content: "\eb64";
}

.nc-boot::before {
  content: "\eb65";
}

.nc-border-radius::before {
  content: "\eb66";
}

.nc-border::before {
  content: "\eb6b";
}

.nc-bored::before {
  content: "\eb67";
}

.nc-botany::before {
  content: "\eb6d";
}

.nc-bottle-wine::before {
  content: "\eb68";
}

.nc-bottle::before {
  content: "\eb69";
}

.nc-bouquet::before {
  content: "\eb6a";
}

.nc-bow::before {
  content: "\eb6c";
}

.nc-bowl::before {
  content: "\eb6e";
}

.nc-bowling-ball::before {
  content: "\eb6f";
}

.nc-bowling-pins::before {
  content: "\eb70";
}

.nc-box-2::before {
  content: "\eb71";
}

.nc-box-3d-50::before {
  content: "\eb72";
}

.nc-box-arrow-bottom-left::before {
  content: "\eb73";
}

.nc-box-arrow-bottom-right::before {
  content: "\eb74";
}

.nc-box-arrow-down::before {
  content: "\eb75";
}

.nc-box-arrow-left::before {
  content: "\eb76";
}

.nc-box-arrow-right::before {
  content: "\eb77";
}

.nc-box-arrow-top-left::before {
  content: "\eb78";
}

.nc-box-arrow-top-right::before {
  content: "\eb79";
}

.nc-box-arrow-up::before {
  content: "\eb7a";
}

.nc-box-caret-down::before {
  content: "\eb7b";
}

.nc-box-caret-left::before {
  content: "\eb7c";
}

.nc-box-caret-right::before {
  content: "\eb7d";
}

.nc-box-caret-up::before {
  content: "\eb7e";
}

.nc-box-ctrl-down::before {
  content: "\eb7f";
}

.nc-box-ctrl-left::before {
  content: "\eb80";
}

.nc-box-ctrl-right::before {
  content: "\eb81";
}

.nc-box-ctrl-up::before {
  content: "\eb82";
}

.nc-box-ribbon::before {
  content: "\eb83";
}

.nc-box::before {
  content: "\eb84";
}

.nc-boxing-bag::before {
  content: "\eb85";
}

.nc-boxing-glove::before {
  content: "\eb86";
}

.nc-boxing::before {
  content: "\eb8b";
}

.nc-bra::before {
  content: "\eb87";
}

.nc-braille::before {
  content: "\eb88";
}

.nc-brain::before {
  content: "\eb89";
}

.nc-brakes::before {
  content: "\eb8a";
}

.nc-bread::before {
  content: "\eb91";
}

.nc-bride::before {
  content: "\eb8c";
}

.nc-briefcase-24::before {
  content: "\eb8d";
}

.nc-briefcase-25::before {
  content: "\eb8e";
}

.nc-briefcase-26::before {
  content: "\eb8f";
}

.nc-brightness::before {
  content: "\eb90";
}

.nc-brioche::before {
  content: "\eb92";
}

.nc-broccoli::before {
  content: "\eb93";
}

.nc-broken-heart::before {
  content: "\eb94";
}

.nc-broom::before {
  content: "\eb95";
}

.nc-browse::before {
  content: "\eb96";
}

.nc-browser-chrome::before {
  content: "\eb97";
}

.nc-browser-edge-legacy::before {
  content: "\eb98";
}

.nc-browser-edge::before {
  content: "\eb99";
}

.nc-browser-firefox::before {
  content: "\eb9a";
}

.nc-browser-ie::before {
  content: "\eb9b";
}

.nc-browser-opera::before {
  content: "\eb9c";
}

.nc-browser-safari::before {
  content: "\eb9d";
}

.nc-brush::before {
  content: "\eb9e";
}

.nc-btn-play-2::before {
  content: "\eb9f";
}

.nc-btn-play::before {
  content: "\eba4";
}

.nc-btn-stop::before {
  content: "\eba0";
}

.nc-bucket::before {
  content: "\eba1";
}

.nc-buddhism::before {
  content: "\eba2";
}

.nc-bug::before {
  content: "\eba3";
}

.nc-bulb-61::before {
  content: "\eba5";
}

.nc-bulb-62::before {
  content: "\eba6";
}

.nc-bulb-63::before {
  content: "\eba7";
}

.nc-bulb-saver::before {
  content: "\eba8";
}

.nc-bulb::before {
  content: "\eba9";
}

.nc-bullet-list-67::before {
  content: "\ebaa";
}

.nc-bullet-list-68::before {
  content: "\ebab";
}

.nc-bullet-list-69::before {
  content: "\ebac";
}

.nc-bullet-list-70::before {
  content: "\ebad";
}

.nc-bullet-list::before {
  content: "\ebae";
}

.nc-bullets::before {
  content: "\ebaf";
}

.nc-bureau-dresser::before {
  content: "\ebb5";
}

.nc-bus-front-10::before {
  content: "\ebb0";
}

.nc-bus-front-12::before {
  content: "\ebb1";
}

.nc-bus::before {
  content: "\ebb2";
}

.nc-business-agent::before {
  content: "\ebb4";
}

.nc-business-contact-85::before {
  content: "\ebb3";
}

.nc-businessman-03::before {
  content: "\ebb6";
}

.nc-businessman-04::before {
  content: "\ebb7";
}

.nc-butter::before {
  content: "\ebb8";
}

.nc-butterfly::before {
  content: "\ebb9";
}

.nc-button-2::before {
  content: "\ebba";
}

.nc-button-eject::before {
  content: "\ebbb";
}

.nc-button-next::before {
  content: "\ebbc";
}

.nc-button-pause::before {
  content: "\ebbd";
}

.nc-button-play::before {
  content: "\ebbe";
}

.nc-button-power::before {
  content: "\ebbf";
}

.nc-button-previous::before {
  content: "\ebc0";
}

.nc-button-record::before {
  content: "\ebc1";
}

.nc-button-rewind::before {
  content: "\ebc2";
}

.nc-button-skip::before {
  content: "\ebc3";
}

.nc-button-stop::before {
  content: "\ebc4";
}

.nc-button::before {
  content: "\ebc5";
}

.nc-buzz::before {
  content: "\ebc6";
}

.nc-c-add::before {
  content: "\ebc7";
}

.nc-c-check::before {
  content: "\ebc8";
}

.nc-c-delete::before {
  content: "\ebc9";
}

.nc-c-edit::before {
  content: "\ebca";
}

.nc-c-info::before {
  content: "\ebcb";
}

.nc-c-pulse::before {
  content: "\ebcc";
}

.nc-c-question::before {
  content: "\ebcd";
}

.nc-c-remove::before {
  content: "\ebce";
}

.nc-c-warning::before {
  content: "\ebcf";
}

.nc-cabinet::before {
  content: "\ebd0";
}

.nc-cable::before {
  content: "\ebd2";
}

.nc-cactus::before {
  content: "\ebd1";
}

.nc-cake-13::before {
  content: "\ebd3";
}

.nc-cake-2::before {
  content: "\ebd4";
}

.nc-cake-slice::before {
  content: "\ebd5";
}

.nc-cake::before {
  content: "\ebd6";
}

.nc-calculator::before {
  content: "\ebd7";
}

.nc-calendar-2::before {
  content: "\ebd8";
}

.nc-calendar-date-2::before {
  content: "\ebd9";
}

.nc-calendar-date::before {
  content: "\ebda";
}

.nc-calendar-day-view::before {
  content: "\ebdb";
}

.nc-calendar-event-2::before {
  content: "\ebdc";
}

.nc-calendar-event-create::before {
  content: "\ebdd";
}

.nc-calendar-event::before {
  content: "\ebde";
}

.nc-calendar::before {
  content: "\ebdf";
}

.nc-call-doctor::before {
  content: "\ebe0";
}

.nc-camcorder::before {
  content: "\ebe1";
}

.nc-camera-2::before {
  content: "\ebe2";
}

.nc-camera-3::before {
  content: "\ebe3";
}

.nc-camera-button::before {
  content: "\ebe4";
}

.nc-camera-flash::before {
  content: "\ebe5";
}

.nc-camera-flashlight::before {
  content: "\ebe6";
}

.nc-camera-focus-2::before {
  content: "\ebe7";
}

.nc-camera-focus::before {
  content: "\ebe8";
}

.nc-camera-lens::before {
  content: "\ebe9";
}

.nc-camera-roll::before {
  content: "\ebea";
}

.nc-camera-screen::before {
  content: "\ebeb";
}

.nc-camera-shooting::before {
  content: "\ebec";
}

.nc-camera-timer::before {
  content: "\ebed";
}

.nc-camera::before {
  content: "\ebee";
}

.nc-camper::before {
  content: "\ebef";
}

.nc-camping::before {
  content: "\ebf5";
}

.nc-can::before {
  content: "\ebf0";
}

.nc-candle::before {
  content: "\ebf1";
}

.nc-candlestick-chart::before {
  content: "\ebf2";
}

.nc-candy-2::before {
  content: "\ebf3";
}

.nc-candy::before {
  content: "\ebf4";
}

.nc-canvas::before {
  content: "\ebf6";
}

.nc-cap::before {
  content: "\ebf7";
}

.nc-capitalize::before {
  content: "\ebf8";
}

.nc-caps-all::before {
  content: "\ebf9";
}

.nc-caps-small::before {
  content: "\ebfa";
}

.nc-car-2::before {
  content: "\ebfb";
}

.nc-car-accident::before {
  content: "\ebfc";
}

.nc-car-connect::before {
  content: "\ebfd";
}

.nc-car-door::before {
  content: "\ebfe";
}

.nc-car-front::before {
  content: "\ebff";
}

.nc-car-lights::before {
  content: "\ec00";
}

.nc-car-parking::before {
  content: "\ec01";
}

.nc-car-simple::before {
  content: "\ec02";
}

.nc-car-sport::before {
  content: "\ec03";
}

.nc-car-ventilation::before {
  content: "\ec04";
}

.nc-car-wash::before {
  content: "\ec05";
}

.nc-car::before {
  content: "\ec06";
}

.nc-card-edit::before {
  content: "\ec07";
}

.nc-card-favorite::before {
  content: "\ec08";
}

.nc-card-remove::before {
  content: "\ec09";
}

.nc-card-update::before {
  content: "\ec0a";
}

.nc-cards::before {
  content: "\ec0b";
}

.nc-caret-sm-up::before {
  content: "\ec0c";
}

.nc-carrot::before {
  content: "\ec0d";
}

.nc-cart-add-9::before {
  content: "\ec0e";
}

.nc-cart-add::before {
  content: "\ec0f";
}

.nc-cart-favorite::before {
  content: "\ec10";
}

.nc-cart-full::before {
  content: "\ec11";
}

.nc-cart-refresh::before {
  content: "\ec12";
}

.nc-cart-remove-9::before {
  content: "\ec13";
}

.nc-cart-remove::before {
  content: "\ec14";
}

.nc-cart-return::before {
  content: "\ec15";
}

.nc-cart-simple-add::before {
  content: "\ec16";
}

.nc-cart-simple-remove::before {
  content: "\ec17";
}

.nc-cart-speed::before {
  content: "\ec18";
}

.nc-cart::before {
  content: "\ec19";
}

.nc-cash-register::before {
  content: "\ec1a";
}

.nc-casino-chip::before {
  content: "\ec1b";
}

.nc-casino::before {
  content: "\ec1c";
}

.nc-castle::before {
  content: "\ec1d";
}

.nc-cat::before {
  content: "\ec1e";
}

.nc-catalog::before {
  content: "\ec1f";
}

.nc-cauldron::before {
  content: "\ec20";
}

.nc-cctv::before {
  content: "\ec21";
}

.nc-cd-reader::before {
  content: "\ec22";
}

.nc-celsius::before {
  content: "\ec23";
}

.nc-centralize::before {
  content: "\ec24";
}

.nc-certificate::before {
  content: "\ec25";
}

.nc-chain::before {
  content: "\ec26";
}

.nc-chair::before {
  content: "\ec2b";
}

.nc-chalkboard::before {
  content: "\ec27";
}

.nc-champagne::before {
  content: "\ec28";
}

.nc-chandelier::before {
  content: "\ec29";
}

.nc-change-direction::before {
  content: "\ec2a";
}

.nc-charger-cable::before {
  content: "\ec2c";
}

.nc-chart-bar-32::before {
  content: "\ec2d";
}

.nc-chart-bar-33::before {
  content: "\ec2e";
}

.nc-chart-growth::before {
  content: "\ec2f";
}

.nc-chart-pie-35::before {
  content: "\ec30";
}

.nc-chart-pie-36::before {
  content: "\ec31";
}

.nc-chart::before {
  content: "\ec32";
}

.nc-chat::before {
  content: "\ec33";
}

.nc-check-all::before {
  content: "\ec34";
}

.nc-check-double::before {
  content: "\ec35";
}

.nc-check-in::before {
  content: "\ec36";
}

.nc-check-list::before {
  content: "\ec37";
}

.nc-check-out::before {
  content: "\ec38";
}

.nc-check-single::before {
  content: "\ec39";
}

.nc-check::before {
  content: "\ec3a";
}

.nc-checkbox-btn-checked::before {
  content: "\ec3b";
}

.nc-checkbox-btn::before {
  content: "\ec3c";
}

.nc-cheese-24::before {
  content: "\ec3d";
}

.nc-cheese-87::before {
  content: "\ec3e";
}

.nc-cheeseburger::before {
  content: "\ec3f";
}

.nc-chef-hat::before {
  content: "\ec40";
}

.nc-chef::before {
  content: "\ec41";
}

.nc-chemistry::before {
  content: "\ec42";
}

.nc-cheque-2::before {
  content: "\ec43";
}

.nc-cheque-3::before {
  content: "\ec44";
}

.nc-cheque::before {
  content: "\ec45";
}

.nc-chequered-flag::before {
  content: "\ec46";
}

.nc-cherry::before {
  content: "\ec47";
}

.nc-chess-bishop::before {
  content: "\ec48";
}

.nc-chess-king::before {
  content: "\ec49";
}

.nc-chess-knight::before {
  content: "\ec4a";
}

.nc-chess-pawn::before {
  content: "\ec4b";
}

.nc-chess-queen::before {
  content: "\ec4c";
}

.nc-chess-tower::before {
  content: "\ec4d";
}

.nc-chicken-2::before {
  content: "\ec4e";
}

.nc-chicken::before {
  content: "\ec4f";
}

.nc-child::before {
  content: "\ec50";
}

.nc-chili::before {
  content: "\ec51";
}

.nc-chimney::before {
  content: "\ec52";
}

.nc-china::before {
  content: "\ec53";
}

.nc-chips::before {
  content: "\ec54";
}

.nc-choco-cream::before {
  content: "\ec55";
}

.nc-chocolate-mousse::before {
  content: "\ec56";
}

.nc-chocolate::before {
  content: "\ec57";
}

.nc-christianity::before {
  content: "\ec58";
}

.nc-church::before {
  content: "\ec59";
}

.nc-churros::before {
  content: "\ec5a";
}

.nc-cinema::before {
  content: "\ec5b";
}

.nc-circle-08::before {
  content: "\ec5c";
}

.nc-circle-09::before {
  content: "\ec5e";
}

.nc-circle-10::before {
  content: "\ec5d";
}

.nc-circle-anim-2::before {
  content: "\ec5f";
}

.nc-circle-anim-3::before {
  content: "\ec60";
}

.nc-circle-anim::before {
  content: "\ec61";
}

.nc-circle-arrow-down::before {
  content: "\ec62";
}

.nc-circle-arrow-left::before {
  content: "\ec63";
}

.nc-circle-arrow-right::before {
  content: "\ec64";
}

.nc-circle-arrow-up::before {
  content: "\ec65";
}

.nc-circle-caret-down::before {
  content: "\ec66";
}

.nc-circle-caret-left::before {
  content: "\ec67";
}

.nc-circle-caret-right::before {
  content: "\ec68";
}

.nc-circle-caret-up::before {
  content: "\ec69";
}

.nc-circle-ctrl-down::before {
  content: "\ec6a";
}

.nc-circle-ctrl-left::before {
  content: "\ec6b";
}

.nc-circle-ctrl-right::before {
  content: "\ec6c";
}

.nc-circle-ctrl-up::before {
  content: "\ec6d";
}

.nc-circle-in::before {
  content: "\ec6e";
}

.nc-circle-out::before {
  content: "\ec6f";
}

.nc-circle::before {
  content: "\ec70";
}

.nc-circuit-round::before {
  content: "\ec71";
}

.nc-circuit::before {
  content: "\ec72";
}

.nc-clapperboard-2::before {
  content: "\ec73";
}

.nc-clapperboard::before {
  content: "\ec74";
}

.nc-clarinet::before {
  content: "\ec7c";
}

.nc-clear-data::before {
  content: "\ec75";
}

.nc-climbing::before {
  content: "\ec76";
}

.nc-clock-anim::before {
  content: "\ec77";
}

.nc-clock::before {
  content: "\ec78";
}

.nc-clone::before {
  content: "\ec79";
}

.nc-closed-captioning::before {
  content: "\ec7a";
}

.nc-clothes-hanger::before {
  content: "\ec7b";
}

.nc-clothing-hanger::before {
  content: "\ec7d";
}

.nc-cloud-data-download::before {
  content: "\ec7e";
}

.nc-cloud-download::before {
  content: "\ec7f";
}

.nc-cloud-drop::before {
  content: "\ec80";
}

.nc-cloud-fog-31::before {
  content: "\ec81";
}

.nc-cloud-fog-32::before {
  content: "\ec82";
}

.nc-cloud-forecast::before {
  content: "\ec83";
}

.nc-cloud-hail::before {
  content: "\ec84";
}

.nc-cloud-light::before {
  content: "\ec85";
}

.nc-cloud-mining::before {
  content: "\ec86";
}

.nc-cloud-moon::before {
  content: "\ec87";
}

.nc-cloud-rain::before {
  content: "\ec88";
}

.nc-cloud-rainbow::before {
  content: "\ec89";
}

.nc-cloud-snow-34::before {
  content: "\ec8a";
}

.nc-cloud-snow-42::before {
  content: "\ec8b";
}

.nc-cloud-sun-17::before {
  content: "\ec8c";
}

.nc-cloud-sun-19::before {
  content: "\ec8d";
}

.nc-cloud-upload::before {
  content: "\ec8e";
}

.nc-cloud::before {
  content: "\ec8f";
}

.nc-clover::before {
  content: "\ec90";
}

.nc-clubs-suit::before {
  content: "\ec91";
}

.nc-coat-hanger::before {
  content: "\ec92";
}

.nc-coat::before {
  content: "\ec93";
}

.nc-cockade::before {
  content: "\ec94";
}

.nc-cocktail::before {
  content: "\ec95";
}

.nc-code-editor::before {
  content: "\ec96";
}

.nc-code::before {
  content: "\ec97";
}

.nc-coffe-long::before {
  content: "\ec98";
}

.nc-coffee-bean::before {
  content: "\ec99";
}

.nc-coffee-long::before {
  content: "\ec9a";
}

.nc-coffee-maker::before {
  content: "\ec9b";
}

.nc-coffee::before {
  content: "\ec9c";
}

.nc-coffin::before {
  content: "\ec9d";
}

.nc-cogwheel::before {
  content: "\ec9e";
}

.nc-coins::before {
  content: "\ec9f";
}

.nc-collar::before {
  content: "\eca0";
}

.nc-collection::before {
  content: "\eca1";
}

.nc-color::before {
  content: "\eca2";
}

.nc-comb::before {
  content: "\eca3";
}

.nc-command::before {
  content: "\eca4";
}

.nc-comment-add::before {
  content: "\eca5";
}

.nc-comment::before {
  content: "\eca6";
}

.nc-comments::before {
  content: "\eca7";
}

.nc-compact-camera::before {
  content: "\eca8";
}

.nc-compare::before {
  content: "\eca9";
}

.nc-compass-04::before {
  content: "\ecaa";
}

.nc-compass-05::before {
  content: "\ecab";
}

.nc-compass-06::before {
  content: "\ecac";
}

.nc-compass-2::before {
  content: "\ecad";
}

.nc-compass-3::before {
  content: "\ecae";
}

.nc-compass::before {
  content: "\ecaf";
}

.nc-components::before {
  content: "\ecb0";
}

.nc-compressed-file::before {
  content: "\ecb1";
}

.nc-computer-monitor::before {
  content: "\ecb2";
}

.nc-computer-upload::before {
  content: "\ecb3";
}

.nc-computer::before {
  content: "\ecb4";
}

.nc-concierge::before {
  content: "\ecb5";
}

.nc-condom::before {
  content: "\ecb6";
}

.nc-cone::before {
  content: "\ecb7";
}

.nc-conference-room::before {
  content: "\ecb8";
}

.nc-configuration-tools::before {
  content: "\ecb9";
}

.nc-connect::before {
  content: "\ecba";
}

.nc-connection::before {
  content: "\ecbb";
}

.nc-construction-sign::before {
  content: "\ecbc";
}

.nc-contact-86::before {
  content: "\ecbf";
}

.nc-contact-87::before {
  content: "\ecbd";
}

.nc-contact-88::before {
  content: "\ecbe";
}

.nc-contact::before {
  content: "\ecc0";
}

.nc-contactless-card::before {
  content: "\ecc1";
}

.nc-contactless::before {
  content: "\ecc2";
}

.nc-contacts-2::before {
  content: "\ecc3";
}

.nc-contacts-44::before {
  content: "\ecc4";
}

.nc-contacts-45::before {
  content: "\ecc5";
}

.nc-contacts::before {
  content: "\ecc6";
}

.nc-content-360deg::before {
  content: "\ecc7";
}

.nc-content-delivery::before {
  content: "\ecc8";
}

.nc-contrast-2::before {
  content: "\ecc9";
}

.nc-contrast::before {
  content: "\ecca";
}

.nc-control-panel::before {
  content: "\eccb";
}

.nc-controller-2::before {
  content: "\eccc";
}

.nc-controller::before {
  content: "\eccd";
}

.nc-conversion::before {
  content: "\ecce";
}

.nc-cookies::before {
  content: "\ecd6";
}

.nc-copy-2::before {
  content: "\eccf";
}

.nc-copy::before {
  content: "\ecd0";
}

.nc-copyright::before {
  content: "\ecd1";
}

.nc-corn::before {
  content: "\ecd2";
}

.nc-corner-bottom-left::before {
  content: "\ecd3";
}

.nc-corner-bottom-right::before {
  content: "\ecd4";
}

.nc-corner-down-round::before {
  content: "\ecd5";
}

.nc-corner-left-down::before {
  content: "\ecd7";
}

.nc-corner-left-round::before {
  content: "\ecd8";
}

.nc-corner-right-down::before {
  content: "\ecd9";
}

.nc-corner-right-round::before {
  content: "\ecda";
}

.nc-corner-top-left::before {
  content: "\ecdb";
}

.nc-corner-top-right::before {
  content: "\ecdf";
}

.nc-corner-up-left::before {
  content: "\ecdc";
}

.nc-corner-up-right::before {
  content: "\ecdd";
}

.nc-corner-up-round::before {
  content: "\ecde";
}

.nc-cornucopia::before {
  content: "\ece0";
}

.nc-corset::before {
  content: "\ece1";
}

.nc-coughing::before {
  content: "\ece2";
}

.nc-countdown-2::before {
  content: "\ece3";
}

.nc-countdown::before {
  content: "\ece4";
}

.nc-couple-gay::before {
  content: "\ece5";
}

.nc-couple-lesbian::before {
  content: "\ece6";
}

.nc-coupon::before {
  content: "\ece7";
}

.nc-cow::before {
  content: "\ece8";
}

.nc-cpu::before {
  content: "\ece9";
}

.nc-crab::before {
  content: "\ecea";
}

.nc-cradle::before {
  content: "\eceb";
}

.nc-crane::before {
  content: "\ecec";
}

.nc-creative-commons::before {
  content: "\eced";
}

.nc-credit-card-in::before {
  content: "\ecee";
}

.nc-credit-card::before {
  content: "\ecef";
}

.nc-credit-locked::before {
  content: "\ecf0";
}

.nc-crepe::before {
  content: "\ecf1";
}

.nc-cricket-bat::before {
  content: "\ecf2";
}

.nc-croissant::before {
  content: "\ecf3";
}

.nc-crop::before {
  content: "\ecf4";
}

.nc-cross-down::before {
  content: "\ecf5";
}

.nc-cross-horizontal::before {
  content: "\ecf6";
}

.nc-cross-left::before {
  content: "\ecf7";
}

.nc-cross-right::before {
  content: "\ecf8";
}

.nc-cross-up::before {
  content: "\ecfb";
}

.nc-cross-vertical::before {
  content: "\ecf9";
}

.nc-cross::before {
  content: "\ecfa";
}

.nc-crosshair::before {
  content: "\ecfc";
}

.nc-crossing-directions::before {
  content: "\ecfd";
}

.nc-crossroad::before {
  content: "\ecfe";
}

.nc-croupier::before {
  content: "\ecff";
}

.nc-crown::before {
  content: "\ed00";
}

.nc-crumpet::before {
  content: "\ed01";
}

.nc-crunches::before {
  content: "\ed02";
}

.nc-cry-15::before {
  content: "\ed03";
}

.nc-cry-57::before {
  content: "\ed04";
}

.nc-crying-baby::before {
  content: "\ed05";
}

.nc-crypto-wallet::before {
  content: "\ed06";
}

.nc-cryptography::before {
  content: "\ed07";
}

.nc-css3::before {
  content: "\ed08";
}

.nc-ctrl-backward::before {
  content: "\ed09";
}

.nc-ctrl-down::before {
  content: "\ed0a";
}

.nc-ctrl-forward::before {
  content: "\ed0b";
}

.nc-ctrl-left::before {
  content: "\ed0c";
}

.nc-ctrl-right::before {
  content: "\ed0d";
}

.nc-ctrl-up::before {
  content: "\ed0e";
}

.nc-cubes-anim::before {
  content: "\ed0f";
}

.nc-cupcake::before {
  content: "\ed10";
}

.nc-cure::before {
  content: "\ed11";
}

.nc-curling-stone::before {
  content: "\ed12";
}

.nc-curling::before {
  content: "\ed13";
}

.nc-currency-dollar::before {
  content: "\ed14";
}

.nc-currency-euro::before {
  content: "\ed15";
}

.nc-currency-exchange-2::before {
  content: "\ed16";
}

.nc-currency-exchange::before {
  content: "\ed17";
}

.nc-currency-pound::before {
  content: "\ed18";
}

.nc-currency-yen::before {
  content: "\ed19";
}

.nc-cursor-48::before {
  content: "\ed1a";
}

.nc-cursor-49::before {
  content: "\ed1b";
}

.nc-cursor-add::before {
  content: "\ed24";
}

.nc-cursor-grab::before {
  content: "\ed1c";
}

.nc-cursor-load::before {
  content: "\ed1d";
}

.nc-cursor-menu::before {
  content: "\ed1e";
}

.nc-cursor-not-allowed::before {
  content: "\ed1f";
}

.nc-cursor-pointer::before {
  content: "\ed20";
}

.nc-cursor-text::before {
  content: "\ed21";
}

.nc-curtains::before {
  content: "\ed22";
}

.nc-curved-arrow-down::before {
  content: "\ed23";
}

.nc-curved-arrow-left::before {
  content: "\ed25";
}

.nc-curved-arrow-right::before {
  content: "\ed26";
}

.nc-curved-circuit::before {
  content: "\ed27";
}

.nc-customer-support::before {
  content: "\ed28";
}

.nc-cut::before {
  content: "\ed29";
}

.nc-cute::before {
  content: "\ed2a";
}

.nc-cutlery-75::before {
  content: "\ed2b";
}

.nc-cutlery-76::before {
  content: "\ed2c";
}

.nc-cutlery-77::before {
  content: "\ed2d";
}

.nc-cutlery::before {
  content: "\ed35";
}

.nc-cyborg::before {
  content: "\ed2e";
}

.nc-cycle::before {
  content: "\ed2f";
}

.nc-cycling-track::before {
  content: "\ed30";
}

.nc-cycling::before {
  content: "\ed31";
}

.nc-d-add::before {
  content: "\ed32";
}

.nc-d-chart::before {
  content: "\ed33";
}

.nc-d-check::before {
  content: "\ed34";
}

.nc-d-delete::before {
  content: "\ed36";
}

.nc-d-edit::before {
  content: "\ed37";
}

.nc-d-remove::before {
  content: "\ed38";
}

.nc-dancer::before {
  content: "\ed39";
}

.nc-dart::before {
  content: "\ed3a";
}

.nc-dashboard::before {
  content: "\ed3b";
}

.nc-data-download::before {
  content: "\ed3c";
}

.nc-data-settings::before {
  content: "\ed3d";
}

.nc-data-table::before {
  content: "\ed3e";
}

.nc-data-upload::before {
  content: "\ed3f";
}

.nc-database::before {
  content: "\ed40";
}

.nc-dead-hand::before {
  content: "\ed41";
}

.nc-deadlift::before {
  content: "\ed42";
}

.nc-deaf::before {
  content: "\ed43";
}

.nc-debt::before {
  content: "\ed44";
}

.nc-decentralize::before {
  content: "\ed45";
}

.nc-decision-process::before {
  content: "\ed46";
}

.nc-decoration::before {
  content: "\ed47";
}

.nc-decrease-font-size::before {
  content: "\ed48";
}

.nc-deer::before {
  content: "\ed49";
}

.nc-delete-28::before {
  content: "\ed4a";
}

.nc-delete-30::before {
  content: "\ed4b";
}

.nc-delete-forever::before {
  content: "\ed4c";
}

.nc-delete-key::before {
  content: "\ed4d";
}

.nc-delete-x::before {
  content: "\ed4e";
}

.nc-delete::before {
  content: "\ed4f";
}

.nc-delivery-2::before {
  content: "\ed54";
}

.nc-delivery-3::before {
  content: "\ed50";
}

.nc-delivery-fast::before {
  content: "\ed51";
}

.nc-delivery-time::before {
  content: "\ed52";
}

.nc-delivery-track::before {
  content: "\ed53";
}

.nc-delivery::before {
  content: "\ed55";
}

.nc-design-system::before {
  content: "\ed56";
}

.nc-design::before {
  content: "\ed57";
}

.nc-desk-drawer::before {
  content: "\ed58";
}

.nc-desk-lamp::before {
  content: "\ed59";
}

.nc-desk::before {
  content: "\ed5a";
}

.nc-detached-property::before {
  content: "\ed61";
}

.nc-detox::before {
  content: "\ed5b";
}

.nc-device-connection::before {
  content: "\ed5c";
}

.nc-devil::before {
  content: "\ed5d";
}

.nc-diamond::before {
  content: "\ed5e";
}

.nc-diamonds-suits::before {
  content: "\ed5f";
}

.nc-diaper-changing-area::before {
  content: "\ed60";
}

.nc-diaper::before {
  content: "\ed62";
}

.nc-dice-2::before {
  content: "\ed63";
}

.nc-dice::before {
  content: "\ed64";
}

.nc-diet-food::before {
  content: "\ed65";
}

.nc-diet-plan::before {
  content: "\ed66";
}

.nc-diet::before {
  content: "\ed67";
}

.nc-digital-key::before {
  content: "\ed68";
}

.nc-digital-piano::before {
  content: "\ed69";
}

.nc-direction-down::before {
  content: "\ed6a";
}

.nc-direction-left::before {
  content: "\ed6b";
}

.nc-direction-right::before {
  content: "\ed6c";
}

.nc-direction-up::before {
  content: "\ed6d";
}

.nc-direction::before {
  content: "\ed6e";
}

.nc-directions::before {
  content: "\ed6f";
}

.nc-discount-2::before {
  content: "\ed70";
}

.nc-disgusted::before {
  content: "\ed71";
}

.nc-dish::before {
  content: "\ed72";
}

.nc-dishwasher::before {
  content: "\ed73";
}

.nc-disinfectant::before {
  content: "\ed74";
}

.nc-disk-reader::before {
  content: "\ed75";
}

.nc-disk::before {
  content: "\ed76";
}

.nc-disperse::before {
  content: "\ed77";
}

.nc-distance::before {
  content: "\ed78";
}

.nc-distribute-horizontal::before {
  content: "\ed79";
}

.nc-distribute-vertical::before {
  content: "\ed7a";
}

.nc-divider::before {
  content: "\ed7b";
}

.nc-dizzy-face::before {
  content: "\ed7c";
}

.nc-dna-27::before {
  content: "\ed7d";
}

.nc-dna-38::before {
  content: "\ed7e";
}

.nc-doc-folder::before {
  content: "\ed7f";
}

.nc-dock-bottom::before {
  content: "\ed80";
}

.nc-dock-left::before {
  content: "\ed81";
}

.nc-dock-right::before {
  content: "\ed82";
}

.nc-dock-top::before {
  content: "\ed83";
}

.nc-doctor::before {
  content: "\ed84";
}

.nc-document-2::before {
  content: "\ed85";
}

.nc-document-copy::before {
  content: "\ed86";
}

.nc-document::before {
  content: "\ed87";
}

.nc-dog-house::before {
  content: "\ed88";
}

.nc-dog-leash::before {
  content: "\ed89";
}

.nc-dog::before {
  content: "\ed8a";
}

.nc-dont-touch-eyes::before {
  content: "\ed8b";
}

.nc-dont-touch-mouth::before {
  content: "\ed8c";
}

.nc-donut::before {
  content: "\ed8d";
}

.nc-door-2::before {
  content: "\ed8e";
}

.nc-door-3::before {
  content: "\ed92";
}

.nc-door-handle::before {
  content: "\ed8f";
}

.nc-door::before {
  content: "\ed90";
}

.nc-doorphone::before {
  content: "\ed91";
}

.nc-dots-anim-2::before {
  content: "\ed93";
}

.nc-dots-anim-3::before {
  content: "\ed94";
}

.nc-dots-anim-4::before {
  content: "\ed95";
}

.nc-dots-anim-5::before {
  content: "\ed96";
}

.nc-dots-anim-6::before {
  content: "\ed97";
}

.nc-dots-anim-7::before {
  content: "\ed98";
}

.nc-dots-anim::before {
  content: "\ed99";
}

.nc-dots::before {
  content: "\ed9a";
}

.nc-double-arrow-left::before {
  content: "\ed9b";
}

.nc-double-arrow-right::before {
  content: "\ed9c";
}

.nc-double-bed::before {
  content: "\ed9d";
}

.nc-double-tap::before {
  content: "\ed9e";
}

.nc-down-arrow::before {
  content: "\ed9f";
}

.nc-download-data::before {
  content: "\eda0";
}

.nc-download-file::before {
  content: "\eda1";
}

.nc-download::before {
  content: "\eda2";
}

.nc-drag-21::before {
  content: "\eda3";
}

.nc-drag-31::before {
  content: "\eda4";
}

.nc-drag-down::before {
  content: "\eda5";
}

.nc-drag-left::before {
  content: "\eda6";
}

.nc-drag-right::before {
  content: "\eda7";
}

.nc-drag-up::before {
  content: "\eda8";
}

.nc-drag::before {
  content: "\eda9";
}

.nc-drawer-2::before {
  content: "\edaa";
}

.nc-drawer::before {
  content: "\edab";
}

.nc-dress-man::before {
  content: "\edac";
}

.nc-dress-woman::before {
  content: "\edad";
}

.nc-dresser-2::before {
  content: "\edae";
}

.nc-dresser-3::before {
  content: "\edaf";
}

.nc-dresser::before {
  content: "\edb0";
}

.nc-drill::before {
  content: "\edb1";
}

.nc-drink-2::before {
  content: "\edb2";
}

.nc-drink-list::before {
  content: "\edb3";
}

.nc-drink::before {
  content: "\edb4";
}

.nc-drinking-bottle::before {
  content: "\edb5";
}

.nc-drone-2::before {
  content: "\edb6";
}

.nc-drone::before {
  content: "\edb7";
}

.nc-drop-15::before {
  content: "\edb8";
}

.nc-drop::before {
  content: "\edb9";
}

.nc-drops::before {
  content: "\edba";
}

.nc-druidism::before {
  content: "\edbb";
}

.nc-drums::before {
  content: "\edbc";
}

.nc-duck::before {
  content: "\edbd";
}

.nc-dumbbell::before {
  content: "\edbe";
}

.nc-duplicate::before {
  content: "\edbf";
}

.nc-e-add::before {
  content: "\edc0";
}

.nc-e-delete::before {
  content: "\edc1";
}

.nc-e-reader::before {
  content: "\edc2";
}

.nc-e-remove::before {
  content: "\edc3";
}

.nc-earbuds::before {
  content: "\edc4";
}

.nc-earth-science::before {
  content: "\edc5";
}

.nc-eclipse::before {
  content: "\edc6";
}

.nc-eco-home::before {
  content: "\edc7";
}

.nc-ecology::before {
  content: "\edc8";
}

.nc-edge-razor::before {
  content: "\edc9";
}

.nc-edit-2::before {
  content: "\edca";
}

.nc-edit-brightness::before {
  content: "\edcb";
}

.nc-edit-color::before {
  content: "\edcc";
}

.nc-edit-contrast::before {
  content: "\edcd";
}

.nc-edit-curves::before {
  content: "\edce";
}

.nc-edit-levels::before {
  content: "\edcf";
}

.nc-edit-note::before {
  content: "\edd0";
}

.nc-edit-saturation::before {
  content: "\edd1";
}

.nc-edit::before {
  content: "\edd2";
}

.nc-egg-38::before {
  content: "\edd3";
}

.nc-egg-39::before {
  content: "\edd4";
}

.nc-egg::before {
  content: "\edd5";
}

.nc-eggs::before {
  content: "\edd6";
}

.nc-eight::before {
  content: "\edd7";
}

.nc-eject::before {
  content: "\edd8";
}

.nc-electronic-circuit::before {
  content: "\edd9";
}

.nc-elephant::before {
  content: "\edda";
}

.nc-elliptical-cross-trainer::before {
  content: "\eddb";
}

.nc-email::before {
  content: "\eddc";
}

.nc-embryo::before {
  content: "\eddd";
}

.nc-empty::before {
  content: "\edde";
}

.nc-energy-drink::before {
  content: "\eddf";
}

.nc-energy-shaker::before {
  content: "\ede0";
}

.nc-energy-supplement::before {
  content: "\ede1";
}

.nc-energy::before {
  content: "\ede7";
}

.nc-engine-start::before {
  content: "\ede2";
}

.nc-engine::before {
  content: "\ede3";
}

.nc-enlarge-diagonal-2::before {
  content: "\ede4";
}

.nc-enlarge-diagonal::before {
  content: "\ede5";
}

.nc-enlarge-h::before {
  content: "\ede6";
}

.nc-enlarge-horizontal::before {
  content: "\ede8";
}

.nc-enlarge-vertical::before {
  content: "\ede9";
}

.nc-enlarge::before {
  content: "\edee";
}

.nc-enter::before {
  content: "\edea";
}

.nc-equestrian-helmet::before {
  content: "\edeb";
}

.nc-eraser-32::before {
  content: "\edec";
}

.nc-eraser-33::before {
  content: "\eded";
}

.nc-eraser-46::before {
  content: "\edef";
}

.nc-escalator::before {
  content: "\edf0";
}

.nc-event-confirm::before {
  content: "\edf1";
}

.nc-event-create::before {
  content: "\edf2";
}

.nc-event-ticket::before {
  content: "\edf3";
}

.nc-exchange::before {
  content: "\edf4";
}

.nc-exclamation-mark::before {
  content: "\edf5";
}

.nc-exercise-bike::before {
  content: "\edf6";
}

.nc-exhibition::before {
  content: "\edf7";
}

.nc-exit-right::before {
  content: "\edf8";
}

.nc-expand-h::before {
  content: "\edf9";
}

.nc-expand-window::before {
  content: "\edfa";
}

.nc-expand::before {
  content: "\edfb";
}

.nc-explore-2::before {
  content: "\edfc";
}

.nc-explore-user::before {
  content: "\edfd";
}

.nc-explore::before {
  content: "\edfe";
}

.nc-export::before {
  content: "\edff";
}

.nc-eye-recognition::before {
  content: "\ee00";
}

.nc-eye::before {
  content: "\ee01";
}

.nc-eyelash::before {
  content: "\ee02";
}

.nc-eyeliner::before {
  content: "\ee03";
}

.nc-eyeshadow::before {
  content: "\ee08";
}

.nc-ez-bar::before {
  content: "\ee04";
}

.nc-f-add::before {
  content: "\ee05";
}

.nc-f-chat::before {
  content: "\ee06";
}

.nc-f-check::before {
  content: "\ee07";
}

.nc-f-comment::before {
  content: "\ee09";
}

.nc-f-dashboard::before {
  content: "\ee0a";
}

.nc-f-delete::before {
  content: "\ee0b";
}

.nc-f-remove::before {
  content: "\ee0c";
}

.nc-face-man::before {
  content: "\ee0d";
}

.nc-face-powder::before {
  content: "\ee0e";
}

.nc-face-recognition::before {
  content: "\ee0f";
}

.nc-face-woman::before {
  content: "\ee10";
}

.nc-factory::before {
  content: "\ee16";
}

.nc-fahrenheit::before {
  content: "\ee11";
}

.nc-family-roof::before {
  content: "\ee13";
}

.nc-family::before {
  content: "\ee12";
}

.nc-fan::before {
  content: "\ee14";
}

.nc-fav-list::before {
  content: "\ee15";
}

.nc-fav-property::before {
  content: "\ee17";
}

.nc-fav-remove::before {
  content: "\ee18";
}

.nc-favorite::before {
  content: "\ee19";
}

.nc-feedback::before {
  content: "\ee1a";
}

.nc-feeding-bottle::before {
  content: "\ee1b";
}

.nc-female::before {
  content: "\ee1c";
}

.nc-fence::before {
  content: "\ee1d";
}

.nc-fencing-swords::before {
  content: "\ee1e";
}

.nc-fencing::before {
  content: "\ee1f";
}

.nc-file-2::before {
  content: "\ee20";
}

.nc-file-add::before {
  content: "\ee21";
}

.nc-file-alert::before {
  content: "\ee22";
}

.nc-file-archive::before {
  content: "\ee23";
}

.nc-file-article::before {
  content: "\ee24";
}

.nc-file-audio-2::before {
  content: "\ee25";
}

.nc-file-audio::before {
  content: "\ee2c";
}

.nc-file-bookmark::before {
  content: "\ee26";
}

.nc-file-chart-bar::before {
  content: "\ee27";
}

.nc-file-chart-pie::before {
  content: "\ee28";
}

.nc-file-check::before {
  content: "\ee29";
}

.nc-file-cloud::before {
  content: "\ee2a";
}

.nc-file-copies::before {
  content: "\ee2b";
}

.nc-file-copy::before {
  content: "\ee2d";
}

.nc-file-delete::before {
  content: "\ee2e";
}

.nc-file-dev::before {
  content: "\ee2f";
}

.nc-file-download-3::before {
  content: "\ee30";
}

.nc-file-download::before {
  content: "\ee31";
}

.nc-file-edit::before {
  content: "\ee32";
}

.nc-file-export::before {
  content: "\ee33";
}

.nc-file-favorite::before {
  content: "\ee34";
}

.nc-file-folder::before {
  content: "\ee35";
}

.nc-file-gallery::before {
  content: "\ee36";
}

.nc-file-history::before {
  content: "\ee37";
}

.nc-file-image::before {
  content: "\ee38";
}

.nc-file-import::before {
  content: "\ee39";
}

.nc-file-info::before {
  content: "\ee3a";
}

.nc-file-link::before {
  content: "\ee3b";
}

.nc-file-locked::before {
  content: "\ee3c";
}

.nc-file-money::before {
  content: "\ee3d";
}

.nc-file-new::before {
  content: "\ee3e";
}

.nc-file-no-access::before {
  content: "\ee3f";
}

.nc-file-play::before {
  content: "\ee40";
}

.nc-file-preferences::before {
  content: "\ee41";
}

.nc-file-question::before {
  content: "\ee42";
}

.nc-file-remove::before {
  content: "\ee43";
}

.nc-file-replace::before {
  content: "\ee44";
}

.nc-file-search::before {
  content: "\ee45";
}

.nc-file-settings::before {
  content: "\ee46";
}

.nc-file-shared::before {
  content: "\ee47";
}

.nc-file-starred::before {
  content: "\ee48";
}

.nc-file-sync::before {
  content: "\ee49";
}

.nc-file-text::before {
  content: "\ee4a";
}

.nc-file-upload-2::before {
  content: "\ee4b";
}

.nc-file-upload-3::before {
  content: "\ee4c";
}

.nc-file-upload::before {
  content: "\ee4d";
}

.nc-file-user::before {
  content: "\ee4e";
}

.nc-file-vector::before {
  content: "\ee4f";
}

.nc-file::before {
  content: "\ee50";
}

.nc-film::before {
  content: "\ee51";
}

.nc-filter-check::before {
  content: "\ee52";
}

.nc-filter-organization::before {
  content: "\ee53";
}

.nc-filter-remove::before {
  content: "\ee54";
}

.nc-filter-tool::before {
  content: "\ee55";
}

.nc-filter::before {
  content: "\ee56";
}

.nc-final-score::before {
  content: "\ee57";
}

.nc-find-baggage::before {
  content: "\ee58";
}

.nc-find-replace::before {
  content: "\ee59";
}

.nc-finger-snap::before {
  content: "\ee5a";
}

.nc-fire::before {
  content: "\ee60";
}

.nc-firearm::before {
  content: "\ee5b";
}

.nc-fireplace::before {
  content: "\ee5c";
}

.nc-firewall::before {
  content: "\ee5d";
}

.nc-fireworks::before {
  content: "\ee5e";
}

.nc-fish::before {
  content: "\ee5f";
}

.nc-fishbone::before {
  content: "\ee61";
}

.nc-fist::before {
  content: "\ee62";
}

.nc-fit-horizontal::before {
  content: "\ee63";
}

.nc-fit-vertical::before {
  content: "\ee68";
}

.nc-five::before {
  content: "\ee64";
}

.nc-flag-complex::before {
  content: "\ee65";
}

.nc-flag-diagonal-33::before {
  content: "\ee66";
}

.nc-flag-diagonal-34::before {
  content: "\ee67";
}

.nc-flag-points-31::before {
  content: "\ee69";
}

.nc-flag-points-32::before {
  content: "\ee6a";
}

.nc-flag-simple::before {
  content: "\ee70";
}

.nc-flag::before {
  content: "\ee6b";
}

.nc-flame::before {
  content: "\ee6c";
}

.nc-flash-off-2::before {
  content: "\ee6d";
}

.nc-flash-off::before {
  content: "\ee6e";
}

.nc-flashlight::before {
  content: "\ee6f";
}

.nc-flask-2::before {
  content: "\ee71";
}

.nc-flask::before {
  content: "\ee72";
}

.nc-flick-down::before {
  content: "\ee73";
}

.nc-flick-left::before {
  content: "\ee74";
}

.nc-flick-right::before {
  content: "\ee75";
}

.nc-flick-up::before {
  content: "\ee76";
}

.nc-flight-connection::before {
  content: "\ee77";
}

.nc-flight::before {
  content: "\ee78";
}

.nc-flip-horizontal::before {
  content: "\ee7d";
}

.nc-flip-up::before {
  content: "\ee79";
}

.nc-flip-vertical::before {
  content: "\ee7a";
}

.nc-flip::before {
  content: "\ee7b";
}

.nc-floor-lamp::before {
  content: "\ee7c";
}

.nc-floor::before {
  content: "\ee7e";
}

.nc-floors::before {
  content: "\ee7f";
}

.nc-floppy-disk::before {
  content: "\ee80";
}

.nc-flower-05::before {
  content: "\ee81";
}

.nc-flower-06::before {
  content: "\ee82";
}

.nc-flower-07::before {
  content: "\ee83";
}

.nc-flower-rose::before {
  content: "\ee84";
}

.nc-focus::before {
  content: "\ee85";
}

.nc-fog::before {
  content: "\ee86";
}

.nc-folder-2::before {
  content: "\ee87";
}

.nc-folder-3::before {
  content: "\ee88";
}

.nc-folder-add::before {
  content: "\ee89";
}

.nc-folder-alert::before {
  content: "\ee8a";
}

.nc-folder-audio::before {
  content: "\ee8b";
}

.nc-folder-bookmark::before {
  content: "\ee8c";
}

.nc-folder-chart-bar::before {
  content: "\ee8d";
}

.nc-folder-chart-pie::before {
  content: "\ee8e";
}

.nc-folder-check::before {
  content: "\ee8f";
}

.nc-folder-cloud::before {
  content: "\ee90";
}

.nc-folder-dev::before {
  content: "\ee91";
}

.nc-folder-download::before {
  content: "\ee92";
}

.nc-folder-edit::before {
  content: "\ee93";
}

.nc-folder-favorite::before {
  content: "\ee94";
}

.nc-folder-gallery::before {
  content: "\ee95";
}

.nc-folder-history::before {
  content: "\ee96";
}

.nc-folder-image::before {
  content: "\ee97";
}

.nc-folder-info::before {
  content: "\ee98";
}

.nc-folder-link::before {
  content: "\ee99";
}

.nc-folder-locked::before {
  content: "\ee9a";
}

.nc-folder-money::before {
  content: "\ee9b";
}

.nc-folder-music::before {
  content: "\ee9c";
}

.nc-folder-no-access::before {
  content: "\ee9d";
}

.nc-folder-play::before {
  content: "\ee9e";
}

.nc-folder-preferences::before {
  content: "\ee9f";
}

.nc-folder-question::before {
  content: "\eea0";
}

.nc-folder-remove::before {
  content: "\eea1";
}

.nc-folder-replace::before {
  content: "\eea2";
}

.nc-folder-search::before {
  content: "\eea3";
}

.nc-folder-settings::before {
  content: "\eea4";
}

.nc-folder-shared::before {
  content: "\eea5";
}

.nc-folder-starred::before {
  content: "\eea6";
}

.nc-folder-sync::before {
  content: "\eea7";
}

.nc-folder-upload::before {
  content: "\eea8";
}

.nc-folder-user::before {
  content: "\eea9";
}

.nc-folder-vector::before {
  content: "\eeaa";
}

.nc-folder::before {
  content: "\eeab";
}

.nc-food-course::before {
  content: "\eeac";
}

.nc-food-dog::before {
  content: "\eead";
}

.nc-food-scale::before {
  content: "\eeae";
}

.nc-food-supplement::before {
  content: "\eeaf";
}

.nc-football-headguard::before {
  content: "\eeb0";
}

.nc-forecast::before {
  content: "\eeb1";
}

.nc-forest::before {
  content: "\eeb2";
}

.nc-fork-2::before {
  content: "\eeb3";
}

.nc-fork::before {
  content: "\eeb4";
}

.nc-form::before {
  content: "\eeb5";
}

.nc-format-left::before {
  content: "\eeb6";
}

.nc-format-right::before {
  content: "\eeb7";
}

.nc-forward::before {
  content: "\eebe";
}

.nc-four::before {
  content: "\eeb8";
}

.nc-frame-effect::before {
  content: "\eeb9";
}

.nc-frame::before {
  content: "\eeba";
}

.nc-frankenstein::before {
  content: "\eebb";
}

.nc-fridge::before {
  content: "\eebc";
}

.nc-fuel-2::before {
  content: "\eebd";
}

.nc-fuel-electric::before {
  content: "\eebf";
}

.nc-fuel::before {
  content: "\eec0";
}

.nc-full-screen::before {
  content: "\eec1";
}

.nc-fullscreen-2::before {
  content: "\eec2";
}

.nc-fullscreen::before {
  content: "\eec3";
}

.nc-fullsize::before {
  content: "\eec4";
}

.nc-funnel::before {
  content: "\eec5";
}

.nc-furnished-property::before {
  content: "\eec6";
}

.nc-g-chart::before {
  content: "\eec7";
}

.nc-g-check::before {
  content: "\eec8";
}

.nc-gallery-layout::before {
  content: "\eec9";
}

.nc-gallery-view::before {
  content: "\eeca";
}

.nc-gaming-console::before {
  content: "\eecb";
}

.nc-gaming-controller::before {
  content: "\eecc";
}

.nc-gantt::before {
  content: "\eecd";
}

.nc-garlic::before {
  content: "\eece";
}

.nc-gas-mask::before {
  content: "\eecf";
}

.nc-gathering-restrictions::before {
  content: "\eed0";
}

.nc-gear::before {
  content: "\eed1";
}

.nc-geometry::before {
  content: "\eed2";
}

.nc-ghost-2::before {
  content: "\eed3";
}

.nc-ghost::before {
  content: "\eed4";
}

.nc-gift-exchange::before {
  content: "\eed5";
}

.nc-gift::before {
  content: "\eed6";
}

.nc-git-commit::before {
  content: "\eed7";
}

.nc-git-merge::before {
  content: "\eed8";
}

.nc-glass-water::before {
  content: "\eed9";
}

.nc-glass::before {
  content: "\eeda";
}

.nc-glasses-2::before {
  content: "\eedb";
}

.nc-glasses::before {
  content: "\eedc";
}

.nc-globe-2::before {
  content: "\eedd";
}

.nc-globe::before {
  content: "\eede";
}

.nc-glove::before {
  content: "\eedf";
}

.nc-gloves::before {
  content: "\eee0";
}

.nc-goal-65::before {
  content: "\eee1";
}

.nc-gold-coin::before {
  content: "\eee2";
}

.nc-gold::before {
  content: "\eee3";
}

.nc-golf-ball::before {
  content: "\eee8";
}

.nc-golf-club::before {
  content: "\eee4";
}

.nc-golf-course::before {
  content: "\eee5";
}

.nc-golf-player::before {
  content: "\eee6";
}

.nc-golf-strike::before {
  content: "\eee7";
}

.nc-gps::before {
  content: "\eee9";
}

.nc-grab::before {
  content: "\eeea";
}

.nc-gradient::before {
  content: "\eeeb";
}

.nc-grain-effect::before {
  content: "\eeec";
}

.nc-grain::before {
  content: "\eeed";
}

.nc-grammar-check::before {
  content: "\eef2";
}

.nc-grandparent::before {
  content: "\eeee";
}

.nc-grape::before {
  content: "\eeef";
}

.nc-graphics-tablet::before {
  content: "\eef0";
}

.nc-grave::before {
  content: "\eef1";
}

.nc-grenade::before {
  content: "\eef3";
}

.nc-grid-interface::before {
  content: "\eef4";
}

.nc-grid-layout::before {
  content: "\eef5";
}

.nc-grid-system::before {
  content: "\eef6";
}

.nc-grid-view::before {
  content: "\eef7";
}

.nc-grid::before {
  content: "\eef8";
}

.nc-groom::before {
  content: "\eef9";
}

.nc-group::before {
  content: "\eefa";
}

.nc-guitar::before {
  content: "\eefb";
}

.nc-gym-class::before {
  content: "\eefc";
}

.nc-gym-shoes::before {
  content: "\eefd";
}

.nc-gym::before {
  content: "\eefe";
}

.nc-gymnastics::before {
  content: "\eeff";
}

.nc-hacker::before {
  content: "\ef00";
}

.nc-hair-clipper::before {
  content: "\ef01";
}

.nc-hair-dryer::before {
  content: "\ef02";
}

.nc-hair-gel::before {
  content: "\ef03";
}

.nc-hair-man::before {
  content: "\ef04";
}

.nc-hair-straightener::before {
  content: "\ef05";
}

.nc-hair-towel::before {
  content: "\ef06";
}

.nc-hair-woman::before {
  content: "\ef07";
}

.nc-hairdresser::before {
  content: "\ef08";
}

.nc-halloween-pumpkin::before {
  content: "\ef0b";
}

.nc-hammer::before {
  content: "\ef09";
}

.nc-hand-card::before {
  content: "\ef0a";
}

.nc-hand-heart::before {
  content: "\ef0c";
}

.nc-hand-mixer::before {
  content: "\ef0d";
}

.nc-handball::before {
  content: "\ef0e";
}

.nc-handheld-console::before {
  content: "\ef0f";
}

.nc-handout::before {
  content: "\ef10";
}

.nc-hands-heart::before {
  content: "\ef11";
}

.nc-handshake::before {
  content: "\ef12";
}

.nc-hanging-toys::before {
  content: "\ef13";
}

.nc-happy-baby::before {
  content: "\ef14";
}

.nc-happy-sun::before {
  content: "\ef15";
}

.nc-hash-mark::before {
  content: "\ef16";
}

.nc-hat-2::before {
  content: "\ef17";
}

.nc-hat-3::before {
  content: "\ef18";
}

.nc-hat-top::before {
  content: "\ef19";
}

.nc-hat::before {
  content: "\ef1a";
}

.nc-hazelnut::before {
  content: "\ef1b";
}

.nc-hdmi::before {
  content: "\ef1c";
}

.nc-heading-1::before {
  content: "\ef1d";
}

.nc-heading-2::before {
  content: "\ef1e";
}

.nc-heading-3::before {
  content: "\ef1f";
}

.nc-heading-4::before {
  content: "\ef20";
}

.nc-heading-5::before {
  content: "\ef21";
}

.nc-heading-6::before {
  content: "\ef22";
}

.nc-headphones-2::before {
  content: "\ef23";
}

.nc-headphones-3::before {
  content: "\ef24";
}

.nc-headphones-mic::before {
  content: "\ef25";
}

.nc-headphones::before {
  content: "\ef26";
}

.nc-headset::before {
  content: "\ef27";
}

.nc-heart-anim::before {
  content: "\ef28";
}

.nc-heart-balloons::before {
  content: "\ef29";
}

.nc-heart-lock::before {
  content: "\ef2a";
}

.nc-heart::before {
  content: "\ef2b";
}

.nc-heartbeat::before {
  content: "\ef2c";
}

.nc-hearts-suit::before {
  content: "\ef2d";
}

.nc-heater::before {
  content: "\ef2e";
}

.nc-height::before {
  content: "\ef2f";
}

.nc-helicopter::before {
  content: "\ef30";
}

.nc-helmet::before {
  content: "\ef31";
}

.nc-hide::before {
  content: "\ef32";
}

.nc-hierarchy-53::before {
  content: "\ef33";
}

.nc-hierarchy-54::before {
  content: "\ef34";
}

.nc-hierarchy-55::before {
  content: "\ef35";
}

.nc-hierarchy-56::before {
  content: "\ef36";
}

.nc-high-priority::before {
  content: "\ef37";
}

.nc-hinduism::before {
  content: "\ef38";
}

.nc-hob::before {
  content: "\ef39";
}

.nc-hockey-stick::before {
  content: "\ef3a";
}

.nc-hockey::before {
  content: "\ef3b";
}

.nc-hold::before {
  content: "\ef3c";
}

.nc-home-2::before {
  content: "\ef3d";
}

.nc-home-3::before {
  content: "\ef3e";
}

.nc-home-search::before {
  content: "\ef3f";
}

.nc-home::before {
  content: "\ef40";
}

.nc-honey::before {
  content: "\ef41";
}

.nc-honeymoon::before {
  content: "\ef42";
}

.nc-hoodie::before {
  content: "\ef43";
}

.nc-hook::before {
  content: "\ef44";
}

.nc-horse-2::before {
  content: "\ef45";
}

.nc-horse-hopper::before {
  content: "\ef46";
}

.nc-horse::before {
  content: "\ef47";
}

.nc-horseshoe::before {
  content: "\ef48";
}

.nc-hospital-32::before {
  content: "\ef49";
}

.nc-hospital-33::before {
  content: "\ef4a";
}

.nc-hospital-34::before {
  content: "\ef4b";
}

.nc-hospital-bed::before {
  content: "\ef4c";
}

.nc-hot-dog::before {
  content: "\ef4d";
}

.nc-hot-key::before {
  content: "\ef4e";
}

.nc-hotel-bell::before {
  content: "\ef4f";
}

.nc-hotel-symbol::before {
  content: "\ef50";
}

.nc-hotel::before {
  content: "\ef51";
}

.nc-hotspot::before {
  content: "\ef52";
}

.nc-hourglass::before {
  content: "\ef53";
}

.nc-house-pricing::before {
  content: "\ef54";
}

.nc-house-property::before {
  content: "\ef55";
}

.nc-house-search-engine::before {
  content: "\ef56";
}

.nc-house::before {
  content: "\ef57";
}

.nc-html5::before {
  content: "\ef58";
}

.nc-humidity-26::before {
  content: "\ef59";
}

.nc-humidity-52::before {
  content: "\ef5a";
}

.nc-hurricane-44::before {
  content: "\ef5b";
}

.nc-hurricane-45::before {
  content: "\ef5c";
}

.nc-hut::before {
  content: "\ef5d";
}

.nc-hybrid-car::before {
  content: "\ef5e";
}

.nc-hyperlink::before {
  content: "\ef5f";
}

.nc-i-add::before {
  content: "\ef60";
}

.nc-i-check::before {
  content: "\ef61";
}

.nc-i-delete::before {
  content: "\ef62";
}

.nc-i-edit::before {
  content: "\ef63";
}

.nc-i-remove::before {
  content: "\ef64";
}

.nc-ice-cream-22::before {
  content: "\ef65";
}

.nc-ice-cream-72::before {
  content: "\ef66";
}

.nc-ice-cream::before {
  content: "\ef67";
}

.nc-ice-skates::before {
  content: "\ef68";
}

.nc-igloo::before {
  content: "\ef69";
}

.nc-image-2::before {
  content: "\ef6a";
}

.nc-image-add::before {
  content: "\ef6b";
}

.nc-image-delete::before {
  content: "\ef6c";
}

.nc-image-location::before {
  content: "\ef6d";
}

.nc-image::before {
  content: "\ef6e";
}

.nc-img-rotate-left::before {
  content: "\ef6f";
}

.nc-img-rotate-right::before {
  content: "\ef78";
}

.nc-img-stack::before {
  content: "\ef70";
}

.nc-img::before {
  content: "\ef71";
}

.nc-incense::before {
  content: "\ef72";
}

.nc-incognito::before {
  content: "\ef73";
}

.nc-increase-font-size::before {
  content: "\ef74";
}

.nc-increase::before {
  content: "\ef75";
}

.nc-infinite-loop::before {
  content: "\ef76";
}

.nc-infinite::before {
  content: "\ef77";
}

.nc-info-point::before {
  content: "\ef79";
}

.nc-info::before {
  content: "\ef7a";
}

.nc-infrared-thermometer::before {
  content: "\ef7b";
}

.nc-input::before {
  content: "\ef7c";
}

.nc-instant-camera-2::before {
  content: "\ef7d";
}

.nc-instant-camera::before {
  content: "\ef7e";
}

.nc-interview::before {
  content: "\ef88";
}

.nc-intestine::before {
  content: "\ef7f";
}

.nc-invert-direction::before {
  content: "\ef80";
}

.nc-invert-process::before {
  content: "\ef81";
}

.nc-iron-2::before {
  content: "\ef82";
}

.nc-iron-dont::before {
  content: "\ef83";
}

.nc-iron::before {
  content: "\ef84";
}

.nc-islam::before {
  content: "\ef85";
}

.nc-istanbul::before {
  content: "\ef86";
}

.nc-italic::before {
  content: "\ef87";
}

.nc-jacuzzi::before {
  content: "\ef89";
}

.nc-jam::before {
  content: "\ef8a";
}

.nc-jeans-41::before {
  content: "\ef8b";
}

.nc-jeans-43::before {
  content: "\ef8c";
}

.nc-jeans-pocket::before {
  content: "\ef8d";
}

.nc-jelly::before {
  content: "\ef8e";
}

.nc-jellyfish::before {
  content: "\ef8f";
}

.nc-jewel::before {
  content: "\ef90";
}

.nc-joint-account::before {
  content: "\ef91";
}

.nc-journey-06::before {
  content: "\ef92";
}

.nc-journey-07::before {
  content: "\ef93";
}

.nc-journey-08::before {
  content: "\ef94";
}

.nc-journey::before {
  content: "\ef95";
}

.nc-js-console::before {
  content: "\ef96";
}

.nc-json-logo::before {
  content: "\ef97";
}

.nc-judaism::before {
  content: "\ef98";
}

.nc-juice::before {
  content: "\ef99";
}

.nc-jump-rope::before {
  content: "\ef9a";
}

.nc-karate::before {
  content: "\ef9b";
}

.nc-ketchup::before {
  content: "\ef9c";
}

.nc-kettle::before {
  content: "\ef9d";
}

.nc-kettlebell::before {
  content: "\ef9e";
}

.nc-key::before {
  content: "\ef9f";
}

.nc-keyboard-hide::before {
  content: "\efa0";
}

.nc-keyboard-mouse::before {
  content: "\efa1";
}

.nc-keyboard-wired::before {
  content: "\efa2";
}

.nc-keyboard-wireless::before {
  content: "\efa3";
}

.nc-keyboard::before {
  content: "\efa4";
}

.nc-kid-2::before {
  content: "\efa5";
}

.nc-kid::before {
  content: "\efa6";
}

.nc-kiss::before {
  content: "\efa7";
}

.nc-kitchen-fan::before {
  content: "\efa8";
}

.nc-kiwi::before {
  content: "\efa9";
}

.nc-knife::before {
  content: "\efaa";
}

.nc-knob::before {
  content: "\efab";
}

.nc-l-add::before {
  content: "\efb2";
}

.nc-l-check::before {
  content: "\efac";
}

.nc-l-circle::before {
  content: "\efad";
}

.nc-l-circles::before {
  content: "\efae";
}

.nc-l-location::before {
  content: "\efb6";
}

.nc-l-remove::before {
  content: "\efaf";
}

.nc-l-search::before {
  content: "\efb0";
}

.nc-l-security::before {
  content: "\efb1";
}

.nc-l-settings::before {
  content: "\efb3";
}

.nc-l-sync::before {
  content: "\efb4";
}

.nc-l-system-update::before {
  content: "\efb5";
}

.nc-label::before {
  content: "\efb7";
}

.nc-ladybug::before {
  content: "\efb8";
}

.nc-lamp-3::before {
  content: "\efb9";
}

.nc-land::before {
  content: "\efba";
}

.nc-landing::before {
  content: "\efbb";
}

.nc-landscape-orientation::before {
  content: "\efbc";
}

.nc-language::before {
  content: "\efbd";
}

.nc-laptop-1::before {
  content: "\efbe";
}

.nc-laptop-2::before {
  content: "\efbf";
}

.nc-laptop-71::before {
  content: "\efc0";
}

.nc-laptop-72::before {
  content: "\efc1";
}

.nc-laptop::before {
  content: "\efc2";
}

.nc-lat-station::before {
  content: "\efc3";
}

.nc-laugh-17::before {
  content: "\efc4";
}

.nc-laugh-35::before {
  content: "\efc5";
}

.nc-launch-app::before {
  content: "\efc6";
}

.nc-launch::before {
  content: "\efc7";
}

.nc-laundry::before {
  content: "\efc8";
}

.nc-law::before {
  content: "\efc9";
}

.nc-layers-2::before {
  content: "\efca";
}

.nc-layers::before {
  content: "\efcb";
}

.nc-layout-11::before {
  content: "\efcc";
}

.nc-layout-25::before {
  content: "\efcd";
}

.nc-layout-grid::before {
  content: "\efce";
}

.nc-layout::before {
  content: "\efcf";
}

.nc-leaf-36::before {
  content: "\efd0";
}

.nc-leaf-38::before {
  content: "\efd3";
}

.nc-leaf::before {
  content: "\efd2";
}

.nc-leave::before {
  content: "\efd1";
}

.nc-left-arrow::before {
  content: "\efd4";
}

.nc-leggins::before {
  content: "\efd5";
}

.nc-lemon-slice::before {
  content: "\efd6";
}

.nc-lemon::before {
  content: "\efd7";
}

.nc-letter-a::before {
  content: "\efd8";
}

.nc-letter-b::before {
  content: "\efd9";
}

.nc-letter-c::before {
  content: "\efda";
}

.nc-letter-d::before {
  content: "\efdb";
}

.nc-letter-e::before {
  content: "\efdc";
}

.nc-letter-f::before {
  content: "\efdd";
}

.nc-letter-g::before {
  content: "\efde";
}

.nc-letter-h::before {
  content: "\efdf";
}

.nc-letter-i::before {
  content: "\efe0";
}

.nc-letter-j::before {
  content: "\efe1";
}

.nc-letter-k::before {
  content: "\efe2";
}

.nc-letter-l::before {
  content: "\efe3";
}

.nc-letter-m::before {
  content: "\efe4";
}

.nc-letter-n::before {
  content: "\efe5";
}

.nc-letter-o::before {
  content: "\efe6";
}

.nc-letter-p::before {
  content: "\efe7";
}

.nc-letter-q::before {
  content: "\efe8";
}

.nc-letter-r::before {
  content: "\efe9";
}

.nc-letter-s::before {
  content: "\efea";
}

.nc-letter-t::before {
  content: "\efeb";
}

.nc-letter-u::before {
  content: "\efec";
}

.nc-letter-v::before {
  content: "\efed";
}

.nc-letter-w::before {
  content: "\efee";
}

.nc-letter-x::before {
  content: "\efef";
}

.nc-letter-y::before {
  content: "\eff0";
}

.nc-letter-z::before {
  content: "\eff1";
}

.nc-letter::before {
  content: "\eff2";
}

.nc-library::before {
  content: "\eff3";
}

.nc-license-key::before {
  content: "\eff4";
}

.nc-lifering::before {
  content: "\eff6";
}

.nc-lift::before {
  content: "\eff5";
}

.nc-light-2::before {
  content: "\eff7";
}

.nc-light-control::before {
  content: "\eff8";
}

.nc-light-switch::before {
  content: "\eff9";
}

.nc-light-traffic::before {
  content: "\effa";
}

.nc-lighter::before {
  content: "\effb";
}

.nc-lighthouse::before {
  content: "\effc";
}

.nc-lightning::before {
  content: "\f003";
}

.nc-like::before {
  content: "\effd";
}

.nc-line-chart::before {
  content: "\effe";
}

.nc-line-height::before {
  content: "\efff";
}

.nc-link-broken::before {
  content: "\f000";
}

.nc-link::before {
  content: "\f001";
}

.nc-lip-gloss::before {
  content: "\f002";
}

.nc-lips::before {
  content: "\f006";
}

.nc-lipstick-2::before {
  content: "\f004";
}

.nc-lipstick::before {
  content: "\f005";
}

.nc-liquid-soap-container::before {
  content: "\f007";
}

.nc-list-bullet::before {
  content: "\f008";
}

.nc-list-numbers::before {
  content: "\f009";
}

.nc-list::before {
  content: "\f00a";
}

.nc-live-streaming::before {
  content: "\f00b";
}

.nc-loader-bars::before {
  content: "\f00c";
}

.nc-loan::before {
  content: "\f00d";
}

.nc-lobster::before {
  content: "\f00e";
}

.nc-lock-landscape::before {
  content: "\f00f";
}

.nc-lock-orientation::before {
  content: "\f010";
}

.nc-lock-portrait::before {
  content: "\f011";
}

.nc-lock::before {
  content: "\f012";
}

.nc-log-in::before {
  content: "\f013";
}

.nc-log-out::before {
  content: "\f014";
}

.nc-logic::before {
  content: "\f015";
}

.nc-logout::before {
  content: "\f016";
}

.nc-lollipop::before {
  content: "\f017";
}

.nc-london::before {
  content: "\f018";
}

.nc-long-sleeve::before {
  content: "\f019";
}

.nc-loop-2::before {
  content: "\f01a";
}

.nc-loop::before {
  content: "\f01b";
}

.nc-lotus-flower::before {
  content: "\f01c";
}

.nc-loudspeaker::before {
  content: "\f01d";
}

.nc-love-camera::before {
  content: "\f01e";
}

.nc-love-car::before {
  content: "\f01f";
}

.nc-love-card::before {
  content: "\f020";
}

.nc-love-heart-pin::before {
  content: "\f021";
}

.nc-love-letter::before {
  content: "\f022";
}

.nc-love-message::before {
  content: "\f023";
}

.nc-love-movie::before {
  content: "\f024";
}

.nc-love-song::before {
  content: "\f025";
}

.nc-love::before {
  content: "\f026";
}

.nc-low-priority::before {
  content: "\f027";
}

.nc-low-vision::before {
  content: "\f02b";
}

.nc-lucky-seven::before {
  content: "\f02d";
}

.nc-luggage::before {
  content: "\f028";
}

.nc-lungs-infection::before {
  content: "\f034";
}

.nc-lungs::before {
  content: "\f029";
}

.nc-m-add::before {
  content: "\f030";
}

.nc-m-check::before {
  content: "\f02a";
}

.nc-m-delete::before {
  content: "\f02c";
}

.nc-m-edit::before {
  content: "\f02e";
}

.nc-m-heart::before {
  content: "\f02f";
}

.nc-m-location::before {
  content: "\f031";
}

.nc-m-remove::before {
  content: "\f032";
}

.nc-m-search::before {
  content: "\f033";
}

.nc-m-security::before {
  content: "\f035";
}

.nc-m-settings::before {
  content: "\f036";
}

.nc-m-share::before {
  content: "\f037";
}

.nc-m-star::before {
  content: "\f038";
}

.nc-m-sync::before {
  content: "\f039";
}

.nc-m-time::before {
  content: "\f03a";
}

.nc-m-update::before {
  content: "\f03b";
}

.nc-machine-learning::before {
  content: "\f03c";
}

.nc-macro::before {
  content: "\f03d";
}

.nc-mad-12::before {
  content: "\f03e";
}

.nc-mad-58::before {
  content: "\f03f";
}

.nc-magnet::before {
  content: "\f040";
}

.nc-magnifier-zoom-in::before {
  content: "\f041";
}

.nc-magnifier-zoom-out::before {
  content: "\f042";
}

.nc-magnifier::before {
  content: "\f043";
}

.nc-mail::before {
  content: "\f044";
}

.nc-makeup-blush::before {
  content: "\f045";
}

.nc-makeup-brush::before {
  content: "\f046";
}

.nc-makeup-cream::before {
  content: "\f047";
}

.nc-makeup-foundation::before {
  content: "\f048";
}

.nc-makeup-mirror::before {
  content: "\f049";
}

.nc-makeup-palette::before {
  content: "\f04a";
}

.nc-makeup::before {
  content: "\f04b";
}

.nc-male::before {
  content: "\f04c";
}

.nc-malicious::before {
  content: "\f04d";
}

.nc-man-20::before {
  content: "\f04e";
}

.nc-man-23::before {
  content: "\f04f";
}

.nc-man-down::before {
  content: "\f050";
}

.nc-man-glasses::before {
  content: "\f051";
}

.nc-man-up-front::before {
  content: "\f053";
}

.nc-man-up::before {
  content: "\f052";
}

.nc-manga-62::before {
  content: "\f054";
}

.nc-manga-63::before {
  content: "\f055";
}

.nc-map-big::before {
  content: "\f056";
}

.nc-map-compass::before {
  content: "\f057";
}

.nc-map-gps::before {
  content: "\f058";
}

.nc-map-marker::before {
  content: "\f059";
}

.nc-map-pin::before {
  content: "\f05a";
}

.nc-map::before {
  content: "\f05b";
}

.nc-maple-leaf::before {
  content: "\f05c";
}

.nc-margin-left::before {
  content: "\f05d";
}

.nc-margin-right::before {
  content: "\f05e";
}

.nc-mario-mushroom::before {
  content: "\f05f";
}

.nc-markdown::before {
  content: "\f060";
}

.nc-marker-2::before {
  content: "\f061";
}

.nc-marker-3::before {
  content: "\f062";
}

.nc-marker::before {
  content: "\f063";
}

.nc-market-music::before {
  content: "\f064";
}

.nc-market-play::before {
  content: "\f065";
}

.nc-mascara::before {
  content: "\f066";
}

.nc-mask-face::before {
  content: "\f067";
}

.nc-mask-oval::before {
  content: "\f068";
}

.nc-mask-rect::before {
  content: "\f069";
}

.nc-massage::before {
  content: "\f06a";
}

.nc-mat::before {
  content: "\f06b";
}

.nc-matches::before {
  content: "\f06c";
}

.nc-math::before {
  content: "\f06d";
}

.nc-maximize-area::before {
  content: "\f06e";
}

.nc-maximize::before {
  content: "\f06f";
}

.nc-mayo::before {
  content: "\f070";
}

.nc-measure-02::before {
  content: "\f071";
}

.nc-measure-17::before {
  content: "\f072";
}

.nc-measure-big::before {
  content: "\f073";
}

.nc-measurement::before {
  content: "\f074";
}

.nc-measuring-cup::before {
  content: "\f075";
}

.nc-meat-spit::before {
  content: "\f076";
}

.nc-medal::before {
  content: "\f077";
}

.nc-media-player::before {
  content: "\f078";
}

.nc-media-stream::before {
  content: "\f079";
}

.nc-medical-clipboard::before {
  content: "\f07a";
}

.nc-medical-mask::before {
  content: "\f07b";
}

.nc-medication::before {
  content: "\f07c";
}

.nc-medicine-ball::before {
  content: "\f07d";
}

.nc-medicine::before {
  content: "\f07e";
}

.nc-meeting::before {
  content: "\f07f";
}

.nc-megaphone::before {
  content: "\f080";
}

.nc-menu-2::before {
  content: "\f081";
}

.nc-menu-3::before {
  content: "\f082";
}

.nc-menu-4::before {
  content: "\f083";
}

.nc-menu-5::before {
  content: "\f084";
}

.nc-menu-6::before {
  content: "\f085";
}

.nc-menu-7::before {
  content: "\f086";
}

.nc-menu-8::before {
  content: "\f087";
}

.nc-menu::before {
  content: "\f088";
}

.nc-merge-2::before {
  content: "\f089";
}

.nc-merge::before {
  content: "\f08a";
}

.nc-messaging::before {
  content: "\f08b";
}

.nc-metrics::before {
  content: "\f08c";
}

.nc-mic-2::before {
  content: "\f08d";
}

.nc-mic::before {
  content: "\f08e";
}

.nc-mickey-mouse::before {
  content: "\f08f";
}

.nc-microbiology::before {
  content: "\f090";
}

.nc-microphone-2::before {
  content: "\f091";
}

.nc-microphone-off::before {
  content: "\f092";
}

.nc-microphone::before {
  content: "\f093";
}

.nc-microscope::before {
  content: "\f094";
}

.nc-microsoft::before {
  content: "\f095";
}

.nc-microwave::before {
  content: "\f096";
}

.nc-migration::before {
  content: "\f097";
}

.nc-military-camp::before {
  content: "\f098";
}

.nc-military-knife::before {
  content: "\f099";
}

.nc-military-medal::before {
  content: "\f09a";
}

.nc-military-tag::before {
  content: "\f09b";
}

.nc-military-tank::before {
  content: "\f09c";
}

.nc-military-vest::before {
  content: "\f09d";
}

.nc-milk::before {
  content: "\f09e";
}

.nc-miner::before {
  content: "\f09f";
}

.nc-mirror-2::before {
  content: "\f0a0";
}

.nc-mirror-display::before {
  content: "\f0a1";
}

.nc-mirror-tablet-phone::before {
  content: "\f0a6";
}

.nc-mirror::before {
  content: "\f0a2";
}

.nc-missile::before {
  content: "\f0a3";
}

.nc-mistletoe::before {
  content: "\f0a4";
}

.nc-mobile-banking::before {
  content: "\f0a5";
}

.nc-mobile-card::before {
  content: "\f0a7";
}

.nc-mobile-chat::before {
  content: "\f0a8";
}

.nc-mobile-contact::before {
  content: "\f0a9";
}

.nc-mobile-design::before {
  content: "\f0aa";
}

.nc-mobile-dev::before {
  content: "\f0ab";
}

.nc-mobile-phone::before {
  content: "\f0ac";
}

.nc-moka::before {
  content: "\f0ad";
}

.nc-molecule-39::before {
  content: "\f0ae";
}

.nc-molecule-40::before {
  content: "\f0af";
}

.nc-molecule::before {
  content: "\f0b0";
}

.nc-money-11::before {
  content: "\f0b1";
}

.nc-money-12::before {
  content: "\f0b2";
}

.nc-money-13::before {
  content: "\f0b3";
}

.nc-money-bag::before {
  content: "\f0b4";
}

.nc-money-coins::before {
  content: "\f0b5";
}

.nc-money-growth::before {
  content: "\f0b6";
}

.nc-money-time::before {
  content: "\f0b7";
}

.nc-money-transfer::before {
  content: "\f0b8";
}

.nc-monster::before {
  content: "\f0b9";
}

.nc-moon-cloud-drop::before {
  content: "\f0ba";
}

.nc-moon-cloud-fog::before {
  content: "\f0bb";
}

.nc-moon-cloud-hail::before {
  content: "\f0bc";
}

.nc-moon-cloud-light::before {
  content: "\f0bd";
}

.nc-moon-cloud-rain::before {
  content: "\f0be";
}

.nc-moon-cloud-snow-61::before {
  content: "\f0bf";
}

.nc-moon-cloud-snow-62::before {
  content: "\f0c0";
}

.nc-moon-fog::before {
  content: "\f0c1";
}

.nc-moon-full::before {
  content: "\f0c2";
}

.nc-moon-stars::before {
  content: "\f0c3";
}

.nc-moon::before {
  content: "\f0c4";
}

.nc-mortar::before {
  content: "\f0c5";
}

.nc-mortgage::before {
  content: "\f0c6";
}

.nc-mosque::before {
  content: "\f0c7";
}

.nc-moto::before {
  content: "\f0c8";
}

.nc-mountain::before {
  content: "\f0c9";
}

.nc-mouse-2::before {
  content: "\f0ca";
}

.nc-mouse-anim::before {
  content: "\f0cb";
}

.nc-mouse::before {
  content: "\f0cc";
}

.nc-move-2::before {
  content: "\f0cd";
}

.nc-move-3::before {
  content: "\f0ce";
}

.nc-move-down-2::before {
  content: "\f0cf";
}

.nc-move-down-right::before {
  content: "\f0d0";
}

.nc-move-down::before {
  content: "\f0d1";
}

.nc-move-layer-down::before {
  content: "\f0d2";
}

.nc-move-layer-left::before {
  content: "\f0d3";
}

.nc-move-layer-right::before {
  content: "\f0d4";
}

.nc-move-layer-up::before {
  content: "\f0d5";
}

.nc-move-left::before {
  content: "\f0d6";
}

.nc-move-right::before {
  content: "\f0d7";
}

.nc-move-up-2::before {
  content: "\f0d8";
}

.nc-move-up-left::before {
  content: "\f0d9";
}

.nc-move-up::before {
  content: "\f0da";
}

.nc-move::before {
  content: "\f0db";
}

.nc-movie-2::before {
  content: "\f0dc";
}

.nc-movie-3::before {
  content: "\f0dd";
}

.nc-movie-reel::before {
  content: "\f0de";
}

.nc-movie::before {
  content: "\f0df";
}

.nc-mower::before {
  content: "\f0e0";
}

.nc-muffin::before {
  content: "\f0e1";
}

.nc-mug::before {
  content: "\f0e2";
}

.nc-multiple-11::before {
  content: "\f0e3";
}

.nc-multiple-19::before {
  content: "\f0e4";
}

.nc-multiple::before {
  content: "\f0e5";
}

.nc-mushroom::before {
  content: "\f0e6";
}

.nc-music-album::before {
  content: "\f0e7";
}

.nc-music-cloud::before {
  content: "\f0e8";
}

.nc-music-note::before {
  content: "\f0e9";
}

.nc-music-player::before {
  content: "\f0ea";
}

.nc-music-playlist::before {
  content: "\f0eb";
}

.nc-music::before {
  content: "\f0ec";
}

.nc-mustache::before {
  content: "\f0ed";
}

.nc-n-check::before {
  content: "\f0ee";
}

.nc-n-edit::before {
  content: "\f0ef";
}

.nc-nail-file::before {
  content: "\f0f0";
}

.nc-nail-polish-2::before {
  content: "\f0f1";
}

.nc-nail-polish::before {
  content: "\f0f2";
}

.nc-name-card::before {
  content: "\f0f3";
}

.nc-nav-down::before {
  content: "\f0f4";
}

.nc-nav-left::before {
  content: "\f0f5";
}

.nc-nav-right::before {
  content: "\f0f6";
}

.nc-nav-up::before {
  content: "\f0f7";
}

.nc-navigation::before {
  content: "\f0f8";
}

.nc-neck-duster::before {
  content: "\f0f9";
}

.nc-needle::before {
  content: "\f0fa";
}

.nc-negative-judgement::before {
  content: "\f0fb";
}

.nc-nerd::before {
  content: "\f0fc";
}

.nc-net::before {
  content: "\f0fd";
}

.nc-network-communication::before {
  content: "\f0fe";
}

.nc-network-connection::before {
  content: "\f0ff";
}

.nc-network::before {
  content: "\f100";
}

.nc-networking::before {
  content: "\f101";
}

.nc-new-construction::before {
  content: "\f102";
}

.nc-new-notification::before {
  content: "\f103";
}

.nc-new::before {
  content: "\f104";
}

.nc-news::before {
  content: "\f10a";
}

.nc-newsletter-dev::before {
  content: "\f105";
}

.nc-newsletter::before {
  content: "\f106";
}

.nc-night-table::before {
  content: "\f107";
}

.nc-night::before {
  content: "\f108";
}

.nc-nine::before {
  content: "\f109";
}

.nc-ninja::before {
  content: "\f10b";
}

.nc-no-contact::before {
  content: "\f10c";
}

.nc-no-guns::before {
  content: "\f10d";
}

.nc-no-photo::before {
  content: "\f10e";
}

.nc-no-results::before {
  content: "\f10f";
}

.nc-no-smoking::before {
  content: "\f110";
}

.nc-no-words::before {
  content: "\f111";
}

.nc-nodes::before {
  content: "\f112";
}

.nc-noodles::before {
  content: "\f113";
}

.nc-note-code::before {
  content: "\f114";
}

.nc-note::before {
  content: "\f115";
}

.nc-notebook::before {
  content: "\f116";
}

.nc-notepad::before {
  content: "\f117";
}

.nc-notes::before {
  content: "\f118";
}

.nc-notification-2::before {
  content: "\f119";
}

.nc-notification::before {
  content: "\f11a";
}

.nc-nurse::before {
  content: "\f11b";
}

.nc-nutrition::before {
  content: "\f11c";
}

.nc-ny::before {
  content: "\f11d";
}

.nc-o-check::before {
  content: "\f11e";
}

.nc-o-warning::before {
  content: "\f11f";
}

.nc-octagon-m::before {
  content: "\f120";
}

.nc-octagon::before {
  content: "\f121";
}

.nc-octopus::before {
  content: "\f122";
}

.nc-office-chair::before {
  content: "\f123";
}

.nc-office::before {
  content: "\f124";
}

.nc-offline::before {
  content: "\f125";
}

.nc-oil-2::before {
  content: "\f126";
}

.nc-oil::before {
  content: "\f127";
}

.nc-olympic-flame::before {
  content: "\f128";
}

.nc-one::before {
  content: "\f129";
}

.nc-onion::before {
  content: "\f12a";
}

.nc-online-banking::before {
  content: "\f12b";
}

.nc-open-book::before {
  content: "\f12c";
}

.nc-open-folder::before {
  content: "\f12d";
}

.nc-open-in-browser::before {
  content: "\f12e";
}

.nc-opening-times::before {
  content: "\f12f";
}

.nc-opposite-directions-2::before {
  content: "\f130";
}

.nc-opposite-directions::before {
  content: "\f131";
}

.nc-options::before {
  content: "\f132";
}

.nc-orange::before {
  content: "\f133";
}

.nc-organic-2::before {
  content: "\f134";
}

.nc-organic::before {
  content: "\f135";
}

.nc-orientation::before {
  content: "\f136";
}

.nc-oven::before {
  content: "\f137";
}

.nc-ovum-sperm::before {
  content: "\f138";
}

.nc-owl::before {
  content: "\f139";
}

.nc-p-add::before {
  content: "\f13a";
}

.nc-p-chart::before {
  content: "\f13b";
}

.nc-p-check::before {
  content: "\f13c";
}

.nc-p-edit::before {
  content: "\f13d";
}

.nc-p-heart::before {
  content: "\f13e";
}

.nc-p-location::before {
  content: "\f13f";
}

.nc-p-remove::before {
  content: "\f144";
}

.nc-p-search::before {
  content: "\f140";
}

.nc-p-settings::before {
  content: "\f141";
}

.nc-p-share::before {
  content: "\f142";
}

.nc-p-sync::before {
  content: "\f143";
}

.nc-p-system-update::before {
  content: "\f148";
}

.nc-p-time::before {
  content: "\f145";
}

.nc-pacifier::before {
  content: "\f146";
}

.nc-pacman::before {
  content: "\f14d";
}

.nc-padlock-unlocked::before {
  content: "\f147";
}

.nc-padlock::before {
  content: "\f149";
}

.nc-paint-16::before {
  content: "\f14a";
}

.nc-paint-37::before {
  content: "\f14b";
}

.nc-paint-38::before {
  content: "\f14c";
}

.nc-paint-brush::before {
  content: "\f14e";
}

.nc-paint-bucket-39::before {
  content: "\f14f";
}

.nc-paint-bucket-40::before {
  content: "\f150";
}

.nc-pajamas::before {
  content: "\f151";
}

.nc-palette::before {
  content: "\f152";
}

.nc-palm-tree::before {
  content: "\f153";
}

.nc-pan::before {
  content: "\f157";
}

.nc-pancake::before {
  content: "\f154";
}

.nc-panda::before {
  content: "\f155";
}

.nc-panel::before {
  content: "\f156";
}

.nc-pantone::before {
  content: "\f158";
}

.nc-paper-design::before {
  content: "\f159";
}

.nc-paper-dev::before {
  content: "\f15a";
}

.nc-paper-diploma::before {
  content: "\f15b";
}

.nc-paper::before {
  content: "\f15c";
}

.nc-parachute::before {
  content: "\f15d";
}

.nc-paragraph-2::before {
  content: "\f15e";
}

.nc-paragraph::before {
  content: "\f15f";
}

.nc-paralympic-games::before {
  content: "\f160";
}

.nc-parent::before {
  content: "\f161";
}

.nc-paris-tower::before {
  content: "\f162";
}

.nc-park::before {
  content: "\f163";
}

.nc-parking-sensors::before {
  content: "\f164";
}

.nc-parking::before {
  content: "\f165";
}

.nc-parrot::before {
  content: "\f166";
}

.nc-party::before {
  content: "\f167";
}

.nc-passenger::before {
  content: "\f168";
}

.nc-passport::before {
  content: "\f169";
}

.nc-password::before {
  content: "\f16a";
}

.nc-pasta::before {
  content: "\f16b";
}

.nc-patch-19::before {
  content: "\f16c";
}

.nc-patch-34::before {
  content: "\f16d";
}

.nc-patch::before {
  content: "\f172";
}

.nc-path-exclude::before {
  content: "\f16e";
}

.nc-path-intersect::before {
  content: "\f16f";
}

.nc-path-minus::before {
  content: "\f170";
}

.nc-path-unite::before {
  content: "\f171";
}

.nc-pattern-recognition::before {
  content: "\f173";
}

.nc-paw::before {
  content: "\f174";
}

.nc-payee::before {
  content: "\f175";
}

.nc-payment-method::before {
  content: "\f176";
}

.nc-payment::before {
  content: "\f177";
}

.nc-payor::before {
  content: "\f178";
}

.nc-pc-monitor::before {
  content: "\f179";
}

.nc-pc-mouse::before {
  content: "\f17a";
}

.nc-pc-play-media::before {
  content: "\f17b";
}

.nc-pc::before {
  content: "\f17c";
}

.nc-pci-card::before {
  content: "\f17d";
}

.nc-peanut::before {
  content: "\f17e";
}

.nc-pear::before {
  content: "\f17f";
}

.nc-peas::before {
  content: "\f180";
}

.nc-pectoral-machine::before {
  content: "\f181";
}

.nc-pen-01::before {
  content: "\f182";
}

.nc-pen-2::before {
  content: "\f183";
}

.nc-pen-23::before {
  content: "\f184";
}

.nc-pen-tool::before {
  content: "\f185";
}

.nc-pen::before {
  content: "\f186";
}

.nc-pencil-47::before {
  content: "\f187";
}

.nc-pencil::before {
  content: "\f188";
}

.nc-pendant-lighting::before {
  content: "\f189";
}

.nc-pendulum::before {
  content: "\f18a";
}

.nc-penguin::before {
  content: "\f18b";
}

.nc-pennant::before {
  content: "\f190";
}

.nc-pepper::before {
  content: "\f18c";
}

.nc-percent-sign::before {
  content: "\f18d";
}

.nc-percentage-38::before {
  content: "\f18e";
}

.nc-percentage-39::before {
  content: "\f18f";
}

.nc-perfume::before {
  content: "\f191";
}

.nc-personal-trainer::before {
  content: "\f199";
}

.nc-pet-food::before {
  content: "\f192";
}

.nc-pharmacy::before {
  content: "\f193";
}

.nc-phone-button::before {
  content: "\f194";
}

.nc-phone-call-end::before {
  content: "\f195";
}

.nc-phone-call::before {
  content: "\f196";
}

.nc-phone-camera-back::before {
  content: "\f197";
}

.nc-phone-camera-front::before {
  content: "\f198";
}

.nc-phone-charging-2::before {
  content: "\f19a";
}

.nc-phone-charging-3::before {
  content: "\f19b";
}

.nc-phone-charging::before {
  content: "\f19c";
}

.nc-phone-dock::before {
  content: "\f19d";
}

.nc-phone-heart::before {
  content: "\f19e";
}

.nc-phone-heartbeat::before {
  content: "\f1a2";
}

.nc-phone-music::before {
  content: "\f19f";
}

.nc-phone-toolbar::before {
  content: "\f1a0";
}

.nc-phone::before {
  content: "\f1a8";
}

.nc-photo-album::before {
  content: "\f1a1";
}

.nc-photo-editor::before {
  content: "\f1aa";
}

.nc-photo-frame::before {
  content: "\f1a3";
}

.nc-photo-not-allowed::before {
  content: "\f1a4";
}

.nc-photo::before {
  content: "\f1a5";
}

.nc-piano-2::before {
  content: "\f1a6";
}

.nc-piano::before {
  content: "\f1a7";
}

.nc-pickaxe::before {
  content: "\f1a9";
}

.nc-pickle::before {
  content: "\f1ab";
}

.nc-picnic-basket::before {
  content: "\f1ac";
}

.nc-picture::before {
  content: "\f1ad";
}

.nc-pie::before {
  content: "\f1ae";
}

.nc-pig-2::before {
  content: "\f1af";
}

.nc-pig::before {
  content: "\f1b0";
}

.nc-pilcrow::before {
  content: "\f1b1";
}

.nc-pilgrim-hat::before {
  content: "\f1b2";
}

.nc-pill-42::before {
  content: "\f1b3";
}

.nc-pill-43::before {
  content: "\f1b4";
}

.nc-pill-bottle::before {
  content: "\f1b5";
}

.nc-pin-2::before {
  content: "\f1b6";
}

.nc-pin-3::before {
  content: "\f1bb";
}

.nc-pin-4::before {
  content: "\f1b7";
}

.nc-pin-add-2::before {
  content: "\f1b8";
}

.nc-pin-add::before {
  content: "\f1b9";
}

.nc-pin-check::before {
  content: "\f1ba";
}

.nc-pin-copy::before {
  content: "\f1bc";
}

.nc-pin-delete::before {
  content: "\f1bd";
}

.nc-pin-edit::before {
  content: "\f1be";
}

.nc-pin-heart::before {
  content: "\f1bf";
}

.nc-pin-remove-2::before {
  content: "\f1c0";
}

.nc-pin-remove::before {
  content: "\f1c1";
}

.nc-pin-search::before {
  content: "\f1c2";
}

.nc-pin-security::before {
  content: "\f1c3";
}

.nc-pin-settings::before {
  content: "\f1c4";
}

.nc-pin-share::before {
  content: "\f1c5";
}

.nc-pin-star::before {
  content: "\f1c6";
}

.nc-pin-sync::before {
  content: "\f1c7";
}

.nc-pin-time::before {
  content: "\f1c8";
}

.nc-pin-user::before {
  content: "\f1c9";
}

.nc-pin::before {
  content: "\f1ca";
}

.nc-pinch::before {
  content: "\f1cb";
}

.nc-pineapple::before {
  content: "\f1cc";
}

.nc-pins::before {
  content: "\f1cd";
}

.nc-pipe::before {
  content: "\f1ce";
}

.nc-pirate::before {
  content: "\f1cf";
}

.nc-pizza-slice::before {
  content: "\f1d0";
}

.nc-pizza::before {
  content: "\f1d1";
}

.nc-plane::before {
  content: "\f1d2";
}

.nc-planet::before {
  content: "\f1d3";
}

.nc-plant-ground::before {
  content: "\f1d4";
}

.nc-plant-leaf::before {
  content: "\f1d5";
}

.nc-plant-vase::before {
  content: "\f1d6";
}

.nc-plate::before {
  content: "\f1d7";
}

.nc-play-media::before {
  content: "\f1d8";
}

.nc-play-movie::before {
  content: "\f1d9";
}

.nc-player::before {
  content: "\f1da";
}

.nc-playground::before {
  content: "\f1db";
}

.nc-playing-cards::before {
  content: "\f1dc";
}

.nc-playlist::before {
  content: "\f1dd";
}

.nc-plug-2::before {
  content: "\f1de";
}

.nc-plug::before {
  content: "\f1df";
}

.nc-podcast-mic::before {
  content: "\f1e0";
}

.nc-podcast::before {
  content: "\f1e1";
}

.nc-podium-trophy::before {
  content: "\f1e2";
}

.nc-podium::before {
  content: "\f1e3";
}

.nc-point-a::before {
  content: "\f1e4";
}

.nc-point-b::before {
  content: "\f1e5";
}

.nc-pointing-down::before {
  content: "\f1e6";
}

.nc-pointing-left::before {
  content: "\f1e7";
}

.nc-pointing-right::before {
  content: "\f1e8";
}

.nc-pointing-up::before {
  content: "\f1e9";
}

.nc-polaroid-photo::before {
  content: "\f1ea";
}

.nc-polaroid-portrait::before {
  content: "\f1eb";
}

.nc-polaroid-shot-delete::before {
  content: "\f1ec";
}

.nc-polaroid-shot-new::before {
  content: "\f1ed";
}

.nc-polaroid-shots::before {
  content: "\f1ee";
}

.nc-polaroid::before {
  content: "\f1ef";
}

.nc-police::before {
  content: "\f1f0";
}

.nc-poop::before {
  content: "\f1f1";
}

.nc-popcorn::before {
  content: "\f1f2";
}

.nc-pos::before {
  content: "\f1f3";
}

.nc-position-marker::before {
  content: "\f1f4";
}

.nc-position-pin::before {
  content: "\f1f5";
}

.nc-position-user::before {
  content: "\f1f6";
}

.nc-position::before {
  content: "\f1f7";
}

.nc-positive-judgement::before {
  content: "\f1f8";
}

.nc-pot::before {
  content: "\f1f9";
}

.nc-potato::before {
  content: "\f1fa";
}

.nc-potion::before {
  content: "\f1fb";
}

.nc-power-level::before {
  content: "\f1fc";
}

.nc-power-lifting::before {
  content: "\f1fd";
}

.nc-power-rack::before {
  content: "\f1fe";
}

.nc-pram::before {
  content: "\f1ff";
}

.nc-preferences::before {
  content: "\f200";
}

.nc-pregnancy-test::before {
  content: "\f201";
}

.nc-pregnant-woman::before {
  content: "\f202";
}

.nc-present::before {
  content: "\f203";
}

.nc-presentation::before {
  content: "\f204";
}

.nc-print::before {
  content: "\f205";
}

.nc-printer::before {
  content: "\f206";
}

.nc-priority-high::before {
  content: "\f207";
}

.nc-priority-highest::before {
  content: "\f208";
}

.nc-priority-low::before {
  content: "\f209";
}

.nc-priority-lowest::before {
  content: "\f20a";
}

.nc-priority-normal::before {
  content: "\f20b";
}

.nc-privacy-policy::before {
  content: "\f20c";
}

.nc-privacy-settings::before {
  content: "\f20d";
}

.nc-privacy::before {
  content: "\f20e";
}

.nc-profile::before {
  content: "\f20f";
}

.nc-progress-2::before {
  content: "\f210";
}

.nc-progress-indicator::before {
  content: "\f211";
}

.nc-progress::before {
  content: "\f212";
}

.nc-projector::before {
  content: "\f213";
}

.nc-property-agreement::before {
  content: "\f214";
}

.nc-property-app::before {
  content: "\f215";
}

.nc-property-for-sale::before {
  content: "\f216";
}

.nc-property-location::before {
  content: "\f217";
}

.nc-property-sold::before {
  content: "\f218";
}

.nc-property-to-rent::before {
  content: "\f219";
}

.nc-property::before {
  content: "\f21a";
}

.nc-prosciutto::before {
  content: "\f21b";
}

.nc-prototype::before {
  content: "\f21c";
}

.nc-pulse-chart::before {
  content: "\f21d";
}

.nc-pulse-sleep::before {
  content: "\f21e";
}

.nc-pulse::before {
  content: "\f21f";
}

.nc-pumpkin::before {
  content: "\f220";
}

.nc-puzzle-09::before {
  content: "\f221";
}

.nc-puzzle-10::before {
  content: "\f222";
}

.nc-puzzle-toy::before {
  content: "\f223";
}

.nc-puzzled::before {
  content: "\f224";
}

.nc-pyramid::before {
  content: "\f225";
}

.nc-question-mark::before {
  content: "\f226";
}

.nc-questionnaire::before {
  content: "\f227";
}

.nc-quite-happy::before {
  content: "\f228";
}

.nc-quote::before {
  content: "\f229";
}

.nc-r-chat::before {
  content: "\f22a";
}

.nc-r-down-left-arrows::before {
  content: "\f22b";
}

.nc-r-down-right-arrows::before {
  content: "\f22c";
}

.nc-r-up-left-arrows::before {
  content: "\f22d";
}

.nc-r-up-right-arrows::before {
  content: "\f22e";
}

.nc-rabbit::before {
  content: "\f22f";
}

.nc-radar::before {
  content: "\f230";
}

.nc-radiation::before {
  content: "\f231";
}

.nc-radio-btn-checked::before {
  content: "\f232";
}

.nc-radio-btn::before {
  content: "\f233";
}

.nc-radio::before {
  content: "\f234";
}

.nc-rain-hail::before {
  content: "\f235";
}

.nc-rain::before {
  content: "\f236";
}

.nc-rainbow::before {
  content: "\f237";
}

.nc-ram-2::before {
  content: "\f238";
}

.nc-ram::before {
  content: "\f239";
}

.nc-random::before {
  content: "\f23a";
}

.nc-ranking::before {
  content: "\f23b";
}

.nc-rat-head::before {
  content: "\f23c";
}

.nc-rat::before {
  content: "\f23d";
}

.nc-rate-down::before {
  content: "\f23e";
}

.nc-rate-up::before {
  content: "\f240";
}

.nc-raw-image::before {
  content: "\f23f";
}

.nc-razor::before {
  content: "\f241";
}

.nc-read::before {
  content: "\f242";
}

.nc-reading-tablet::before {
  content: "\f243";
}

.nc-reading::before {
  content: "\f244";
}

.nc-real-estate::before {
  content: "\f245";
}

.nc-receipt-list-42::before {
  content: "\f246";
}

.nc-receipt-list-43::before {
  content: "\f247";
}

.nc-receipt::before {
  content: "\f248";
}

.nc-recipe-book-46::before {
  content: "\f249";
}

.nc-recipe-book-47::before {
  content: "\f24a";
}

.nc-recipe-create::before {
  content: "\f24b";
}

.nc-recipe::before {
  content: "\f24c";
}

.nc-record-player::before {
  content: "\f24d";
}

.nc-recycling::before {
  content: "\f24e";
}

.nc-redo::before {
  content: "\f24f";
}

.nc-referee::before {
  content: "\f250";
}

.nc-refresh-01::before {
  content: "\f251";
}

.nc-refresh-02::before {
  content: "\f252";
}

.nc-refresh::before {
  content: "\f253";
}

.nc-refund::before {
  content: "\f254";
}

.nc-reload::before {
  content: "\f255";
}

.nc-remote-control::before {
  content: "\f256";
}

.nc-remove-fav::before {
  content: "\f257";
}

.nc-remove-favorite::before {
  content: "\f258";
}

.nc-remove-like::before {
  content: "\f259";
}

.nc-remove::before {
  content: "\f25a";
}

.nc-repeat-cycle::before {
  content: "\f25b";
}

.nc-repeat::before {
  content: "\f25f";
}

.nc-replay::before {
  content: "\f25c";
}

.nc-reply-all::before {
  content: "\f25d";
}

.nc-reply-arrow::before {
  content: "\f25e";
}

.nc-reply::before {
  content: "\f260";
}

.nc-research::before {
  content: "\f261";
}

.nc-reservation::before {
  content: "\f262";
}

.nc-resistance-band::before {
  content: "\f263";
}

.nc-resize-h::before {
  content: "\f264";
}

.nc-resize-v::before {
  content: "\f265";
}

.nc-respond-arrow::before {
  content: "\f266";
}

.nc-restaurant-menu::before {
  content: "\f267";
}

.nc-restore::before {
  content: "\f268";
}

.nc-rice::before {
  content: "\f269";
}

.nc-right-arrow::before {
  content: "\f26a";
}

.nc-rim::before {
  content: "\f26b";
}

.nc-ring::before {
  content: "\f26c";
}

.nc-rings::before {
  content: "\f26d";
}

.nc-rio::before {
  content: "\f26e";
}

.nc-ripple-anim::before {
  content: "\f26f";
}

.nc-road-2::before {
  content: "\f270";
}

.nc-road-sign-left::before {
  content: "\f271";
}

.nc-road-sign-right::before {
  content: "\f272";
}

.nc-road::before {
  content: "\f273";
}

.nc-roadmap::before {
  content: "\f274";
}

.nc-roast-chicken::before {
  content: "\f275";
}

.nc-roast-turkey::before {
  content: "\f276";
}

.nc-robot-cleaner::before {
  content: "\f277";
}

.nc-robot::before {
  content: "\f278";
}

.nc-robotic-arm::before {
  content: "\f279";
}

.nc-rock::before {
  content: "\f27a";
}

.nc-rolling-pin::before {
  content: "\f27b";
}

.nc-romantic-dinner::before {
  content: "\f27c";
}

.nc-romantic-restaurant::before {
  content: "\f27d";
}

.nc-rome::before {
  content: "\f27e";
}

.nc-rotate-22::before {
  content: "\f27f";
}

.nc-rotate-23::before {
  content: "\f280";
}

.nc-rotate-camera::before {
  content: "\f281";
}

.nc-rotate-left::before {
  content: "\f282";
}

.nc-rotate-right::before {
  content: "\f283";
}

.nc-rotating-bars-anim::before {
  content: "\f284";
}

.nc-roulette::before {
  content: "\f289";
}

.nc-round-dollar::before {
  content: "\f285";
}

.nc-round-euro::before {
  content: "\f286";
}

.nc-round-pound::before {
  content: "\f287";
}

.nc-round-yen::before {
  content: "\f288";
}

.nc-route-alert::before {
  content: "\f28a";
}

.nc-route-close::before {
  content: "\f28b";
}

.nc-route-open::before {
  content: "\f28c";
}

.nc-route::before {
  content: "\f28d";
}

.nc-router::before {
  content: "\f28e";
}

.nc-row-machine::before {
  content: "\f28f";
}

.nc-row-table::before {
  content: "\f290";
}

.nc-rowing-oars::before {
  content: "\f291";
}

.nc-rowing::before {
  content: "\f292";
}

.nc-rugby-ball::before {
  content: "\f293";
}

.nc-rugby::before {
  content: "\f294";
}

.nc-ruler-pencil::before {
  content: "\f295";
}

.nc-run-shoes::before {
  content: "\f296";
}

.nc-runny-nose::before {
  content: "\f297";
}

.nc-s-add::before {
  content: "\f298";
}

.nc-s-ban::before {
  content: "\f299";
}

.nc-s-check::before {
  content: "\f29a";
}

.nc-s-delete::before {
  content: "\f29b";
}

.nc-s-edit::before {
  content: "\f29c";
}

.nc-s-info::before {
  content: "\f29d";
}

.nc-s-pulse::before {
  content: "\f29e";
}

.nc-s-question::before {
  content: "\f29f";
}

.nc-s-remove::before {
  content: "\f2a0";
}

.nc-s-warning::before {
  content: "\f2a1";
}

.nc-sad::before {
  content: "\f2a2";
}

.nc-safe::before {
  content: "\f2a3";
}

.nc-salad::before {
  content: "\f2a4";
}

.nc-sale::before {
  content: "\f2a5";
}

.nc-salt::before {
  content: "\f2a6";
}

.nc-santa-hat::before {
  content: "\f2a7";
}

.nc-satellite-dish::before {
  content: "\f2a8";
}

.nc-satellite::before {
  content: "\f2a9";
}

.nc-satisfied::before {
  content: "\f2aa";
}

.nc-sauna::before {
  content: "\f2ab";
}

.nc-sausage::before {
  content: "\f2ac";
}

.nc-save-for-later::before {
  content: "\f2ad";
}

.nc-save-planet::before {
  content: "\f2ae";
}

.nc-save-the-date::before {
  content: "\f2af";
}

.nc-save-to-list::before {
  content: "\f2b0";
}

.nc-saved-items::before {
  content: "\f2b1";
}

.nc-savings::before {
  content: "\f2b2";
}

.nc-saxophone::before {
  content: "\f2b3";
}

.nc-scale-2::before {
  content: "\f2b4";
}

.nc-scale-3::before {
  content: "\f2b5";
}

.nc-scale-4::before {
  content: "\f2b6";
}

.nc-scale-down::before {
  content: "\f2b7";
}

.nc-scale-horizontal::before {
  content: "\f2b8";
}

.nc-scale-up::before {
  content: "\f2b9";
}

.nc-scale-vertical::before {
  content: "\f2ba";
}

.nc-scale::before {
  content: "\f2bb";
}

.nc-scan::before {
  content: "\f2bc";
}

.nc-scarf::before {
  content: "\f2bd";
}

.nc-scented-candle::before {
  content: "\f2c1";
}

.nc-school::before {
  content: "\f2be";
}

.nc-scissors-2::before {
  content: "\f2bf";
}

.nc-scissors-dashed::before {
  content: "\f2c0";
}

.nc-scissors::before {
  content: "\f2c7";
}

.nc-scooter::before {
  content: "\f2c2";
}

.nc-scotch::before {
  content: "\f2c3";
}

.nc-screen-enlarge::before {
  content: "\f2c4";
}

.nc-screen-expand::before {
  content: "\f2c5";
}

.nc-screen-maximize::before {
  content: "\f2c6";
}

.nc-screen-reader::before {
  content: "\f2c8";
}

.nc-screen-rotation::before {
  content: "\f2c9";
}

.nc-screen-sharing-2::before {
  content: "\f2ca";
}

.nc-screen-sharing-off-2::before {
  content: "\f2cb";
}

.nc-screen-touch::before {
  content: "\f2cc";
}

.nc-scroll-horizontal::before {
  content: "\f2cd";
}

.nc-scroll-vertical::before {
  content: "\f2ce";
}

.nc-sd-card::before {
  content: "\f2cf";
}

.nc-search-3::before {
  content: "\f2d0";
}

.nc-search-content::before {
  content: "\f2d1";
}

.nc-search-property::before {
  content: "\f2d2";
}

.nc-search-zoom-in::before {
  content: "\f2d3";
}

.nc-search-zoom-out::before {
  content: "\f2d4";
}

.nc-search::before {
  content: "\f2d5";
}

.nc-seat::before {
  content: "\f2d6";
}

.nc-seatbelt::before {
  content: "\f2d7";
}

.nc-security-gate::before {
  content: "\f2d8";
}

.nc-security-officer::before {
  content: "\f2d9";
}

.nc-security::before {
  content: "\f2da";
}

.nc-segmentation::before {
  content: "\f2db";
}

.nc-select::before {
  content: "\f2dc";
}

.nc-selection::before {
  content: "\f2dd";
}

.nc-selfie-2::before {
  content: "\f2de";
}

.nc-selfie::before {
  content: "\f2df";
}

.nc-send-message::before {
  content: "\f2e0";
}

.nc-send-to-phone::before {
  content: "\f2e1";
}

.nc-send::before {
  content: "\f2e2";
}

.nc-sensor::before {
  content: "\f2e3";
}

.nc-separate-branch::before {
  content: "\f2e4";
}

.nc-separate-directions::before {
  content: "\f2e5";
}

.nc-separate::before {
  content: "\f2e6";
}

.nc-server-rack::before {
  content: "\f2e7";
}

.nc-server::before {
  content: "\f2eb";
}

.nc-settings-gear::before {
  content: "\f2e8";
}

.nc-settings::before {
  content: "\f2e9";
}

.nc-setup-options::before {
  content: "\f2ea";
}

.nc-setup-preferences::before {
  content: "\f2ec";
}

.nc-setup-tools::before {
  content: "\f2ed";
}

.nc-seven::before {
  content: "\f2ee";
}

.nc-sf-bridge::before {
  content: "\f2ef";
}

.nc-shaker::before {
  content: "\f2f0";
}

.nc-shape-adjust::before {
  content: "\f2f1";
}

.nc-shape-arrow::before {
  content: "\f2f2";
}

.nc-shape-circle::before {
  content: "\f2f3";
}

.nc-shape-custom::before {
  content: "\f2f4";
}

.nc-shape-line::before {
  content: "\f2f5";
}

.nc-shape-oval::before {
  content: "\f2f6";
}

.nc-shape-polygon-2::before {
  content: "\f2f7";
}

.nc-shape-polygon::before {
  content: "\f2fc";
}

.nc-shape-rectangle::before {
  content: "\f2f8";
}

.nc-shape-square::before {
  content: "\f2f9";
}

.nc-shape-star::before {
  content: "\f2fa";
}

.nc-shape-triangle-2::before {
  content: "\f2fb";
}

.nc-shape-triangle::before {
  content: "\f2fd";
}

.nc-shapes::before {
  content: "\f2fe";
}

.nc-share-2::before {
  content: "\f2ff";
}

.nc-share-3::before {
  content: "\f300";
}

.nc-share::before {
  content: "\f306";
}

.nc-sharing::before {
  content: "\f301";
}

.nc-shark-2::before {
  content: "\f302";
}

.nc-shark::before {
  content: "\f303";
}

.nc-sharpen::before {
  content: "\f304";
}

.nc-sharpener::before {
  content: "\f305";
}

.nc-sheep::before {
  content: "\f307";
}

.nc-shell::before {
  content: "\f30e";
}

.nc-shield::before {
  content: "\f308";
}

.nc-shinto::before {
  content: "\f311";
}

.nc-shirt-business::before {
  content: "\f309";
}

.nc-shirt-buttons::before {
  content: "\f30a";
}

.nc-shirt-neck::before {
  content: "\f30b";
}

.nc-shirt::before {
  content: "\f30c";
}

.nc-shoe-man::before {
  content: "\f30d";
}

.nc-shoe-woman::before {
  content: "\f30f";
}

.nc-shop-location::before {
  content: "\f310";
}

.nc-shop::before {
  content: "\f315";
}

.nc-shopping-bag::before {
  content: "\f312";
}

.nc-shopping-cart-2::before {
  content: "\f313";
}

.nc-shopping-cart::before {
  content: "\f314";
}

.nc-shopping-label::before {
  content: "\f316";
}

.nc-shopping-tag::before {
  content: "\f317";
}

.nc-shorts::before {
  content: "\f318";
}

.nc-shotgun::before {
  content: "\f319";
}

.nc-shovel::before {
  content: "\f31a";
}

.nc-show::before {
  content: "\f31b";
}

.nc-shower::before {
  content: "\f31c";
}

.nc-shrimp::before {
  content: "\f31d";
}

.nc-shuffle-2::before {
  content: "\f31e";
}

.nc-shuffle::before {
  content: "\f31f";
}

.nc-shuttle::before {
  content: "\f320";
}

.nc-shuttlecock::before {
  content: "\f321";
}

.nc-shy::before {
  content: "\f322";
}

.nc-sick::before {
  content: "\f323";
}

.nc-sickle::before {
  content: "\f324";
}

.nc-sidebar::before {
  content: "\f328";
}

.nc-sign-board::before {
  content: "\f325";
}

.nc-sign-down::before {
  content: "\f326";
}

.nc-sign-left::before {
  content: "\f327";
}

.nc-sign-right::before {
  content: "\f329";
}

.nc-sign-up::before {
  content: "\f32a";
}

.nc-sign::before {
  content: "\f32b";
}

.nc-signal::before {
  content: "\f32c";
}

.nc-signature::before {
  content: "\f32d";
}

.nc-silly::before {
  content: "\f32e";
}

.nc-sim-card::before {
  content: "\f32f";
}

.nc-single-05::before {
  content: "\f330";
}

.nc-single-bed::before {
  content: "\f331";
}

.nc-single-position::before {
  content: "\f332";
}

.nc-sink-faucet::before {
  content: "\f333";
}

.nc-sink::before {
  content: "\f334";
}

.nc-six::before {
  content: "\f335";
}

.nc-size-large::before {
  content: "\f336";
}

.nc-size-medium::before {
  content: "\f337";
}

.nc-size-small::before {
  content: "\f338";
}

.nc-skateboard-2::before {
  content: "\f339";
}

.nc-skateboard::before {
  content: "\f33a";
}

.nc-skateboarding::before {
  content: "\f33b";
}

.nc-skating::before {
  content: "\f33c";
}

.nc-skiing::before {
  content: "\f33d";
}

.nc-skipping-rope::before {
  content: "\f33e";
}

.nc-skirt::before {
  content: "\f33f";
}

.nc-skull-2::before {
  content: "\f340";
}

.nc-skull::before {
  content: "\f341";
}

.nc-slacks-12::before {
  content: "\f342";
}

.nc-slacks-13::before {
  content: "\f343";
}

.nc-sleep-2::before {
  content: "\f344";
}

.nc-sleep::before {
  content: "\f345";
}

.nc-sleeping-baby::before {
  content: "\f34d";
}

.nc-slice::before {
  content: "\f346";
}

.nc-slide-left::before {
  content: "\f347";
}

.nc-slide-right::before {
  content: "\f348";
}

.nc-slider::before {
  content: "\f349";
}

.nc-slippers::before {
  content: "\f34a";
}

.nc-slot-machine::before {
  content: "\f34b";
}

.nc-sloth::before {
  content: "\f34c";
}

.nc-smart-house::before {
  content: "\f34e";
}

.nc-smart::before {
  content: "\f34f";
}

.nc-smartphone::before {
  content: "\f350";
}

.nc-smartwatch::before {
  content: "\f351";
}

.nc-smile::before {
  content: "\f352";
}

.nc-smiling-face-glasses::before {
  content: "\f353";
}

.nc-smiling-face-sunglasses::before {
  content: "\f354";
}

.nc-smoking::before {
  content: "\f355";
}

.nc-smoothie::before {
  content: "\f356";
}

.nc-snack::before {
  content: "\f357";
}

.nc-snake::before {
  content: "\f358";
}

.nc-sneeze::before {
  content: "\f359";
}

.nc-sniper-rifle::before {
  content: "\f35a";
}

.nc-snorkel-mask::before {
  content: "\f35b";
}

.nc-snow-ball::before {
  content: "\f35c";
}

.nc-snow::before {
  content: "\f35d";
}

.nc-snowboard::before {
  content: "\f35e";
}

.nc-snowboarding::before {
  content: "\f35f";
}

.nc-snowman-head::before {
  content: "\f360";
}

.nc-snowman::before {
  content: "\f361";
}

.nc-soap::before {
  content: "\f362";
}

.nc-soccer-ball::before {
  content: "\f363";
}

.nc-soccer-field::before {
  content: "\f364";
}

.nc-soccer::before {
  content: "\f365";
}

.nc-social-distancing::before {
  content: "\f366";
}

.nc-social-sharing::before {
  content: "\f367";
}

.nc-sock::before {
  content: "\f368";
}

.nc-socket-europe-1::before {
  content: "\f369";
}

.nc-socket-europe-2::before {
  content: "\f36a";
}

.nc-socket-uk::before {
  content: "\f36b";
}

.nc-socket::before {
  content: "\f36c";
}

.nc-sofa::before {
  content: "\f36d";
}

.nc-soft-drink::before {
  content: "\f36e";
}

.nc-soldier::before {
  content: "\f36f";
}

.nc-solider-helmet::before {
  content: "\f370";
}

.nc-sort-tool::before {
  content: "\f371";
}

.nc-sound-wave::before {
  content: "\f372";
}

.nc-sound::before {
  content: "\f373";
}

.nc-soundwave::before {
  content: "\f374";
}

.nc-soup::before {
  content: "\f375";
}

.nc-soy-sauce::before {
  content: "\f376";
}

.nc-spa-rocks::before {
  content: "\f377";
}

.nc-spa::before {
  content: "\f378";
}

.nc-spaceship::before {
  content: "\f379";
}

.nc-spades-suit::before {
  content: "\f37a";
}

.nc-speaker-2::before {
  content: "\f37b";
}

.nc-speaker::before {
  content: "\f37c";
}

.nc-speechless::before {
  content: "\f37d";
}

.nc-speedometer::before {
  content: "\f37e";
}

.nc-sperm::before {
  content: "\f37f";
}

.nc-spider::before {
  content: "\f380";
}

.nc-spinning-bike::before {
  content: "\f381";
}

.nc-spiteful::before {
  content: "\f382";
}

.nc-split-branch::before {
  content: "\f383";
}

.nc-split-horizontal::before {
  content: "\f384";
}

.nc-split-vertical::before {
  content: "\f385";
}

.nc-split::before {
  content: "\f386";
}

.nc-sport-bag::before {
  content: "\f387";
}

.nc-sport-mode::before {
  content: "\f388";
}

.nc-sports-bra::before {
  content: "\f389";
}

.nc-sports-fan::before {
  content: "\f38a";
}

.nc-sports-tank::before {
  content: "\f38b";
}

.nc-spray-bottle::before {
  content: "\f38c";
}

.nc-spray-can::before {
  content: "\f38d";
}

.nc-square-marker::before {
  content: "\f38e";
}

.nc-square-pin::before {
  content: "\f38f";
}

.nc-squares-anim-2::before {
  content: "\f390";
}

.nc-squares-anim::before {
  content: "\f391";
}

.nc-ssd::before {
  content: "\f392";
}

.nc-stack::before {
  content: "\f393";
}

.nc-stadium::before {
  content: "\f394";
}

.nc-stair-climber::before {
  content: "\f395";
}

.nc-stairs::before {
  content: "\f396";
}

.nc-stamp::before {
  content: "\f397";
}

.nc-standing-man::before {
  content: "\f398";
}

.nc-standing-woman::before {
  content: "\f399";
}

.nc-star-rate::before {
  content: "\f39a";
}

.nc-star::before {
  content: "\f39b";
}

.nc-statistics::before {
  content: "\f39c";
}

.nc-stay-home::before {
  content: "\f39d";
}

.nc-steak-2::before {
  content: "\f39e";
}

.nc-steak::before {
  content: "\f39f";
}

.nc-steam-iron::before {
  content: "\f3a0";
}

.nc-steering-wheel::before {
  content: "\f3a1";
}

.nc-steps::before {
  content: "\f3a2";
}

.nc-stethoscope::before {
  content: "\f3a3";
}

.nc-sticker::before {
  content: "\f3a4";
}

.nc-stock-2::before {
  content: "\f3a5";
}

.nc-stock-market::before {
  content: "\f3a6";
}

.nc-stopwatch::before {
  content: "\f3a7";
}

.nc-storage-hanger::before {
  content: "\f3a8";
}

.nc-storage-shelves::before {
  content: "\f3a9";
}

.nc-storage-unit::before {
  content: "\f3aa";
}

.nc-store::before {
  content: "\f3ab";
}

.nc-strawberry::before {
  content: "\f3ac";
}

.nc-stretch::before {
  content: "\f3ad";
}

.nc-stretching::before {
  content: "\f3ae";
}

.nc-strikethrough::before {
  content: "\f3af";
}

.nc-style::before {
  content: "\f3b0";
}

.nc-submachine-gun::before {
  content: "\f3b1";
}

.nc-submarine::before {
  content: "\f3b2";
}

.nc-subscript::before {
  content: "\f3b3";
}

.nc-subtitles::before {
  content: "\f3b4";
}

.nc-sugar::before {
  content: "\f3b5";
}

.nc-sun-cloud-drop::before {
  content: "\f3b6";
}

.nc-sun-cloud-fog::before {
  content: "\f3b7";
}

.nc-sun-cloud-hail::before {
  content: "\f3b8";
}

.nc-sun-cloud-light::before {
  content: "\f3b9";
}

.nc-sun-cloud-rain::before {
  content: "\f3ba";
}

.nc-sun-cloud-snow-54::before {
  content: "\f3bb";
}

.nc-sun-cloud-snow-55::before {
  content: "\f3bc";
}

.nc-sun-cloud::before {
  content: "\f3bd";
}

.nc-sun-fog-29::before {
  content: "\f3be";
}

.nc-sun-fog-30::before {
  content: "\f3bf";
}

.nc-sun-fog-43::before {
  content: "\f3c0";
}

.nc-sun::before {
  content: "\f3c1";
}

.nc-sunglasses-48::before {
  content: "\f3c2";
}

.nc-sunglasses::before {
  content: "\f3c3";
}

.nc-superscript::before {
  content: "\f3c4";
}

.nc-support::before {
  content: "\f3c5";
}

.nc-surfboard::before {
  content: "\f3c6";
}

.nc-surprise::before {
  content: "\f3c7";
}

.nc-survey::before {
  content: "\f3c8";
}

.nc-sushi::before {
  content: "\f3c9";
}

.nc-swap-horizontal::before {
  content: "\f3ca";
}

.nc-swap-vertical::before {
  content: "\f3cb";
}

.nc-swimming-pool::before {
  content: "\f3cc";
}

.nc-swimming::before {
  content: "\f3cd";
}

.nc-swimsuit::before {
  content: "\f3ce";
}

.nc-swipe-bottom::before {
  content: "\f3cf";
}

.nc-swipe-left::before {
  content: "\f3d0";
}

.nc-swipe-right::before {
  content: "\f3d1";
}

.nc-swipe-up::before {
  content: "\f3d2";
}

.nc-swiss-knife::before {
  content: "\f3d3";
}

.nc-switches::before {
  content: "\f3d4";
}

.nc-sword::before {
  content: "\f3d5";
}

.nc-sync-devices::before {
  content: "\f3d6";
}

.nc-syringe::before {
  content: "\f3d7";
}

.nc-system-configuration::before {
  content: "\f3d8";
}

.nc-system-preferences::before {
  content: "\f3d9";
}

.nc-system-update::before {
  content: "\f3da";
}

.nc-t-add::before {
  content: "\f3db";
}

.nc-t-delete::before {
  content: "\f3dc";
}

.nc-t-remove::before {
  content: "\f3dd";
}

.nc-table-lamp::before {
  content: "\f3de";
}

.nc-table-layout::before {
  content: "\f3df";
}

.nc-table-move::before {
  content: "\f3e0";
}

.nc-table-slide::before {
  content: "\f3e1";
}

.nc-table-tennis-bat::before {
  content: "\f3e2";
}

.nc-table::before {
  content: "\f3e3";
}

.nc-tablet-2::before {
  content: "\f3e4";
}

.nc-tablet-charging::before {
  content: "\f3e5";
}

.nc-tablet-mobile::before {
  content: "\f3e6";
}

.nc-tablet-toolbar::before {
  content: "\f3e7";
}

.nc-tablet::before {
  content: "\f3e8";
}

.nc-tacos::before {
  content: "\f3e9";
}

.nc-tactic::before {
  content: "\f3ea";
}

.nc-tag-add::before {
  content: "\f3eb";
}

.nc-tag-check::before {
  content: "\f3ec";
}

.nc-tag-cut::before {
  content: "\f3ed";
}

.nc-tag-loyalty::before {
  content: "\f3ee";
}

.nc-tag-remove::before {
  content: "\f3ef";
}

.nc-tag-sale::before {
  content: "\f3f0";
}

.nc-tag::before {
  content: "\f3f1";
}

.nc-tags-stack::before {
  content: "\f3f2";
}

.nc-take-off::before {
  content: "\f3f3";
}

.nc-takeaway::before {
  content: "\f3f4";
}

.nc-taoism::before {
  content: "\f3f5";
}

.nc-tap-01::before {
  content: "\f3f6";
}

.nc-tap-02::before {
  content: "\f3f7";
}

.nc-tape::before {
  content: "\f3f8";
}

.nc-target::before {
  content: "\f3f9";
}

.nc-taxi::before {
  content: "\f3fa";
}

.nc-tea-bag::before {
  content: "\f3fb";
}

.nc-tea::before {
  content: "\f3fc";
}

.nc-teddy-bear::before {
  content: "\f3fd";
}

.nc-telephone::before {
  content: "\f3fe";
}

.nc-telescope::before {
  content: "\f3ff";
}

.nc-temperature-2::before {
  content: "\f400";
}

.nc-temperature::before {
  content: "\f401";
}

.nc-temple-25::before {
  content: "\f402";
}

.nc-temple::before {
  content: "\f403";
}

.nc-tennis-ball::before {
  content: "\f404";
}

.nc-tennis-racket::before {
  content: "\f405";
}

.nc-tennis::before {
  content: "\f406";
}

.nc-terrace::before {
  content: "\f407";
}

.nc-text-2::before {
  content: "\f408";
}

.nc-text-size::before {
  content: "\f409";
}

.nc-text::before {
  content: "\f40a";
}

.nc-texture::before {
  content: "\f40b";
}

.nc-theater-curtains::before {
  content: "\f40c";
}

.nc-theater::before {
  content: "\f40d";
}

.nc-thermometer::before {
  content: "\f40e";
}

.nc-three-dimensional-object::before {
  content: "\f40f";
}

.nc-three-dimensional-world::before {
  content: "\f410";
}

.nc-three-way-direction::before {
  content: "\f411";
}

.nc-three::before {
  content: "\f412";
}

.nc-thumb-down::before {
  content: "\f413";
}

.nc-thumb-up::before {
  content: "\f414";
}

.nc-ticket::before {
  content: "\f415";
}

.nc-tie-01::before {
  content: "\f416";
}

.nc-tie-02::before {
  content: "\f417";
}

.nc-tie-bow::before {
  content: "\f418";
}

.nc-time-alarm::before {
  content: "\f419";
}

.nc-time-clock::before {
  content: "\f41a";
}

.nc-time-machine::before {
  content: "\f41b";
}

.nc-timeline::before {
  content: "\f41c";
}

.nc-timer::before {
  content: "\f41d";
}

.nc-tnt-explosives::before {
  content: "\f41e";
}

.nc-toast::before {
  content: "\f41f";
}

.nc-toaster::before {
  content: "\f420";
}

.nc-todo::before {
  content: "\f421";
}

.nc-toggle::before {
  content: "\f422";
}

.nc-toilet-paper::before {
  content: "\f423";
}

.nc-toilet::before {
  content: "\f424";
}

.nc-toilette::before {
  content: "\f425";
}

.nc-tomato::before {
  content: "\f426";
}

.nc-tool-blur::before {
  content: "\f427";
}

.nc-tool-hand::before {
  content: "\f428";
}

.nc-tool-select::before {
  content: "\f429";
}

.nc-tooth::before {
  content: "\f42a";
}

.nc-towel-hanger::before {
  content: "\f42b";
}

.nc-towel::before {
  content: "\f42c";
}

.nc-track-delivery::before {
  content: "\f42d";
}

.nc-tracking::before {
  content: "\f42e";
}

.nc-tractor::before {
  content: "\f42f";
}

.nc-traffic::before {
  content: "\f430";
}

.nc-train-speed::before {
  content: "\f431";
}

.nc-train::before {
  content: "\f432";
}

.nc-tram::before {
  content: "\f433";
}

.nc-transaction::before {
  content: "\f434";
}

.nc-transactions::before {
  content: "\f435";
}

.nc-transform-2d::before {
  content: "\f436";
}

.nc-transform-origin::before {
  content: "\f437";
}

.nc-transform::before {
  content: "\f438";
}

.nc-translation::before {
  content: "\f439";
}

.nc-transparent::before {
  content: "\f43a";
}

.nc-trash-can::before {
  content: "\f43b";
}

.nc-trash::before {
  content: "\f43c";
}

.nc-travel-makeup-mirror::before {
  content: "\f442";
}

.nc-treadmill::before {
  content: "\f43d";
}

.nc-treasure-map-21::before {
  content: "\f43e";
}

.nc-treasure-map-40::before {
  content: "\f43f";
}

.nc-tree-01::before {
  content: "\f440";
}

.nc-tree-02::before {
  content: "\f441";
}

.nc-tree-03::before {
  content: "\f443";
}

.nc-tree-ball::before {
  content: "\f444";
}

.nc-tree::before {
  content: "\f445";
}

.nc-trend-down::before {
  content: "\f446";
}

.nc-trend-up::before {
  content: "\f447";
}

.nc-triangle-down::before {
  content: "\f448";
}

.nc-triangle-left::before {
  content: "\f449";
}

.nc-triangle-line-down::before {
  content: "\f44a";
}

.nc-triangle-line-left::before {
  content: "\f44b";
}

.nc-triangle-line-right::before {
  content: "\f44c";
}

.nc-triangle-line-up::before {
  content: "\f44d";
}

.nc-triangle-right::before {
  content: "\f44e";
}

.nc-triangle-sm-down::before {
  content: "\f44f";
}

.nc-triangle-sm-left::before {
  content: "\f450";
}

.nc-triangle-sm-right::before {
  content: "\f451";
}

.nc-triangle-sm-up::before {
  content: "\f452";
}

.nc-triangle-up::before {
  content: "\f453";
}

.nc-tripod::before {
  content: "\f454";
}

.nc-trophy::before {
  content: "\f455";
}

.nc-truck-front::before {
  content: "\f456";
}

.nc-trumpet::before {
  content: "\f45d";
}

.nc-trunk::before {
  content: "\f457";
}

.nc-tshirt-53::before {
  content: "\f458";
}

.nc-tshirt-54::before {
  content: "\f459";
}

.nc-tshirt-sport::before {
  content: "\f45a";
}

.nc-tty::before {
  content: "\f45b";
}

.nc-turn-e::before {
  content: "\f45c";
}

.nc-turn-n::before {
  content: "\f45e";
}

.nc-turn-s::before {
  content: "\f45f";
}

.nc-turn-w::before {
  content: "\f460";
}

.nc-turtle::before {
  content: "\f461";
}

.nc-tv-stand::before {
  content: "\f462";
}

.nc-tv::before {
  content: "\f463";
}

.nc-two-way-direction::before {
  content: "\f464";
}

.nc-two::before {
  content: "\f465";
}

.nc-umbrella-13::before {
  content: "\f46c";
}

.nc-umbrella-14::before {
  content: "\f466";
}

.nc-underline::before {
  content: "\f467";
}

.nc-underwear-man::before {
  content: "\f468";
}

.nc-underwear::before {
  content: "\f469";
}

.nc-undo::before {
  content: "\f46a";
}

.nc-ungroup::before {
  content: "\f46b";
}

.nc-unite-2::before {
  content: "\f46d";
}

.nc-unite::before {
  content: "\f46e";
}

.nc-unlink::before {
  content: "\f46f";
}

.nc-unlocked::before {
  content: "\f470";
}

.nc-up-arrow::before {
  content: "\f471";
}

.nc-upload-data::before {
  content: "\f472";
}

.nc-upload-file::before {
  content: "\f473";
}

.nc-upload::before {
  content: "\f474";
}

.nc-upset-13::before {
  content: "\f475";
}

.nc-upset-14::before {
  content: "\f476";
}

.nc-url::before {
  content: "\f477";
}

.nc-usb::before {
  content: "\f478";
}

.nc-user-frame-31::before {
  content: "\f47e";
}

.nc-user-frame-32::before {
  content: "\f479";
}

.nc-user-frame-33::before {
  content: "\f47a";
}

.nc-user::before {
  content: "\f47b";
}

.nc-users-mm::before {
  content: "\f47c";
}

.nc-users-wm::before {
  content: "\f47d";
}

.nc-users-ww::before {
  content: "\f47f";
}

.nc-utility-bench::before {
  content: "\f480";
}

.nc-vacuum-cleaner::before {
  content: "\f481";
}

.nc-vampire::before {
  content: "\f482";
}

.nc-vector::before {
  content: "\f483";
}

.nc-vegan::before {
  content: "\f484";
}

.nc-ventilation::before {
  content: "\f485";
}

.nc-verified::before {
  content: "\f486";
}

.nc-vespa-front::before {
  content: "\f48a";
}

.nc-vespa::before {
  content: "\f487";
}

.nc-vest-31::before {
  content: "\f48e";
}

.nc-vest::before {
  content: "\f488";
}

.nc-vibrance::before {
  content: "\f489";
}

.nc-video-camera::before {
  content: "\f48b";
}

.nc-video-gallery-2::before {
  content: "\f48c";
}

.nc-video-gallery::before {
  content: "\f48d";
}

.nc-video-off::before {
  content: "\f48f";
}

.nc-video-player::before {
  content: "\f490";
}

.nc-video-playlist::before {
  content: "\f494";
}

.nc-video::before {
  content: "\f491";
}

.nc-view::before {
  content: "\f492";
}

.nc-vignette::before {
  content: "\f493";
}

.nc-vintage-computer::before {
  content: "\f495";
}

.nc-vintage-tv::before {
  content: "\f49b";
}

.nc-violin::before {
  content: "\f496";
}

.nc-virtual-assistant-2::before {
  content: "\f497";
}

.nc-virtual-assistant::before {
  content: "\f498";
}

.nc-virtual-environment::before {
  content: "\f499";
}

.nc-virtual-reality::before {
  content: "\f49a";
}

.nc-virus::before {
  content: "\f4a0";
}

.nc-voice-recognition::before {
  content: "\f49c";
}

.nc-voice-record::before {
  content: "\f49d";
}

.nc-volleyball-player::before {
  content: "\f49e";
}

.nc-volleyball::before {
  content: "\f49f";
}

.nc-volume-2::before {
  content: "\f4a1";
}

.nc-volume-down::before {
  content: "\f4a2";
}

.nc-volume-mute::before {
  content: "\f4a3";
}

.nc-volume-off::before {
  content: "\f4a4";
}

.nc-volume-up::before {
  content: "\f4a5";
}

.nc-volume::before {
  content: "\f4a6";
}

.nc-vpn::before {
  content: "\f4a7";
}

.nc-vr-controller::before {
  content: "\f4a8";
}

.nc-vr-headset::before {
  content: "\f4a9";
}

.nc-waffle::before {
  content: "\f4aa";
}

.nc-walk::before {
  content: "\f4ab";
}

.nc-walking-aid::before {
  content: "\f4ae";
}

.nc-walking-support::before {
  content: "\f4af";
}

.nc-wallet-43::before {
  content: "\f4ac";
}

.nc-wallet-44::before {
  content: "\f4ad";
}

.nc-wallet-90::before {
  content: "\f4b0";
}

.nc-wallet::before {
  content: "\f4b1";
}

.nc-wand-11::before {
  content: "\f4b2";
}

.nc-wardrobe-2::before {
  content: "\f4b3";
}

.nc-wardrobe-3::before {
  content: "\f4b4";
}

.nc-wardrobe-4::before {
  content: "\f4b5";
}

.nc-wardrobe::before {
  content: "\f4b6";
}

.nc-warning-sign::before {
  content: "\f4b7";
}

.nc-wash-30::before {
  content: "\f4b8";
}

.nc-wash-60::before {
  content: "\f4b9";
}

.nc-wash-90::before {
  content: "\f4ba";
}

.nc-wash-hand::before {
  content: "\f4bb";
}

.nc-wash-hands::before {
  content: "\f4bc";
}

.nc-washing-fluid::before {
  content: "\f4bd";
}

.nc-washing-machine::before {
  content: "\f4be";
}

.nc-waste-danger::before {
  content: "\f4bf";
}

.nc-waste-recycling::before {
  content: "\f4c0";
}

.nc-waste::before {
  content: "\f4c1";
}

.nc-watch-2::before {
  content: "\f4c4";
}

.nc-watch-dev::before {
  content: "\f4c2";
}

.nc-watch-heart::before {
  content: "\f4c3";
}

.nc-watch-heartbeat::before {
  content: "\f4ca";
}

.nc-watch::before {
  content: "\f4c5";
}

.nc-water-aerobics::before {
  content: "\f4c6";
}

.nc-water-hand::before {
  content: "\f4c7";
}

.nc-water-polo-ball::before {
  content: "\f4c8";
}

.nc-water-polo::before {
  content: "\f4c9";
}

.nc-water-sink::before {
  content: "\f4cb";
}

.nc-water-surface::before {
  content: "\f4cc";
}

.nc-water-wave::before {
  content: "\f4cd";
}

.nc-water::before {
  content: "\f4ce";
}

.nc-watermelon::before {
  content: "\f4cf";
}

.nc-wc::before {
  content: "\f4d0";
}

.nc-web-design::before {
  content: "\f4d1";
}

.nc-web-hyperlink::before {
  content: "\f4d2";
}

.nc-web-link::before {
  content: "\f4d3";
}

.nc-web-url::before {
  content: "\f4d4";
}

.nc-webcam-2::before {
  content: "\f4d5";
}

.nc-webcam::before {
  content: "\f4d6";
}

.nc-webpage::before {
  content: "\f4d7";
}

.nc-wedding-arch::before {
  content: "\f4d8";
}

.nc-wedding-cake::before {
  content: "\f4d9";
}

.nc-wedding-ring::before {
  content: "\f4da";
}

.nc-wedding-rings::before {
  content: "\f4db";
}

.nc-weed::before {
  content: "\f4e0";
}

.nc-weight-bench::before {
  content: "\f4dc";
}

.nc-weight-gain::before {
  content: "\f4dd";
}

.nc-weight-loss::before {
  content: "\f4de";
}

.nc-weight-plate::before {
  content: "\f4df";
}

.nc-weight-scale::before {
  content: "\f4e1";
}

.nc-what::before {
  content: "\f4e2";
}

.nc-wheel-2::before {
  content: "\f4e3";
}

.nc-wheel::before {
  content: "\f4e4";
}

.nc-wheelchair-2::before {
  content: "\f4e5";
}

.nc-wheelchair-ramp::before {
  content: "\f4e6";
}

.nc-wheelchair::before {
  content: "\f4e7";
}

.nc-whisk::before {
  content: "\f4e8";
}

.nc-whiskers::before {
  content: "\f4e9";
}

.nc-whistle::before {
  content: "\f4ea";
}

.nc-white-balance::before {
  content: "\f4eb";
}

.nc-white-house::before {
  content: "\f4ec";
}

.nc-widget::before {
  content: "\f4ed";
}

.nc-wifi-2::before {
  content: "\f4ee";
}

.nc-wifi-off::before {
  content: "\f4ef";
}

.nc-wifi-protected::before {
  content: "\f4f0";
}

.nc-wifi-router::before {
  content: "\f4f1";
}

.nc-wifi::before {
  content: "\f4f2";
}

.nc-wind-2::before {
  content: "\f4f3";
}

.nc-wind::before {
  content: "\f4f4";
}

.nc-window-add::before {
  content: "\f4f5";
}

.nc-window-code::before {
  content: "\f4f6";
}

.nc-window-delete::before {
  content: "\f4f7";
}

.nc-window-dev::before {
  content: "\f4f8";
}

.nc-window-maximize::before {
  content: "\f4f9";
}

.nc-window-minimize::before {
  content: "\f4fa";
}

.nc-window-paragraph::before {
  content: "\f4fb";
}

.nc-window-responsive::before {
  content: "\f4fc";
}

.nc-window::before {
  content: "\f4fd";
}

.nc-windsurfing::before {
  content: "\f4fe";
}

.nc-wine-list::before {
  content: "\f4ff";
}

.nc-wink-06::before {
  content: "\f500";
}

.nc-wink-11::before {
  content: "\f501";
}

.nc-wink-69::before {
  content: "\f502";
}

.nc-winner::before {
  content: "\f503";
}

.nc-wireframe::before {
  content: "\f504";
}

.nc-wireless-charging::before {
  content: "\f505";
}

.nc-witch-hat::before {
  content: "\f506";
}

.nc-wolf::before {
  content: "\f507";
}

.nc-woman-2::before {
  content: "\f508";
}

.nc-woman-21::before {
  content: "\f509";
}

.nc-woman-24::before {
  content: "\f50a";
}

.nc-woman-down::before {
  content: "\f50b";
}

.nc-woman-man::before {
  content: "\f50c";
}

.nc-woman-up-front::before {
  content: "\f50d";
}

.nc-woman-up::before {
  content: "\f50e";
}

.nc-wood::before {
  content: "\f50f";
}

.nc-wool-ball::before {
  content: "\f510";
}

.nc-workout-plan::before {
  content: "\f511";
}

.nc-world-2::before {
  content: "\f512";
}

.nc-world-marker::before {
  content: "\f513";
}

.nc-world-pin::before {
  content: "\f514";
}

.nc-world::before {
  content: "\f515";
}

.nc-wrench-tool::before {
  content: "\f516";
}

.nc-wrench::before {
  content: "\f517";
}

.nc-xmas-sock::before {
  content: "\f518";
}

.nc-yoga::before {
  content: "\f519";
}

.nc-yogurt::before {
  content: "\f51a";
}

.nc-zero::before {
  content: "\f51b";
}

.nc-zipped-file::before {
  content: "\f51c";
}

.nc-zombie::before {
  content: "\f51d";
}

.nc-zoom-e::before {
  content: "\f51e";
}

.nc-zoom-in::before {
  content: "\f51f";
}

.nc-zoom-out::before {
  content: "\f520";
}

.nc-zoom::before {
  content: "\f521";
}

