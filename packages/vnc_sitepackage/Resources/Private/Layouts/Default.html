<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:vite="http://typo3.org/ns/Praetorius/ViteAssetCollector/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <vite:asset entry="frontend/js/global.js" />
    



    <f:spaceless>
        <f:render partial="Header" arguments="{_all}" />
        <f:render section="Main" arguments="{_all}" />
        <f:render partial="Footer" arguments="{_all}" />
    </f:spaceless>

<f:comment>
    <div class="container md:grid-cols-6">
      <button class="button button--primary">Button Primary</button>
      <button class="button button--secondary">Button Secondary</button>
      <button class="button button--outline">Button Outline</button>
      <button class="button button--primary"> 
          Button
          <svg class="icon" preserveAspectRatio="xMaxYMin">
              <use
                  xlink:href="{f:uri.resource(path: 'EXT:vnc_sitepackage/Resources/Public/dist/icons.svg#icon-plus')}"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  x="0"
                  y="0"
              ></use>
          </svg>
      </button>

      <button class="button button--primary button--icon">
          <svg class="icon" preserveAspectRatio="xMaxYMin">
              <use
                  xlink:href="{f:uri.resource(path: 'EXT:vnc_sitepackage/Resources/Public/dist/icons.svg#icon-plus')}"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  x="0"
                  y="0"
              ></use>
          </svg>
      </button>
      

      <button class="button button--primary button--icon button--large">
        <svg class="icon" preserveAspectRatio="xMaxYMin">
            <use
                xlink:href="{f:uri.resource(path: 'EXT:vnc_sitepackage/Resources/Public/dist/icons.svg#icon-plus')}"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                x="0"
                y="0"
            ></use>
        </svg>
    </button>
      
  </div>
</f:comment>
</html>
