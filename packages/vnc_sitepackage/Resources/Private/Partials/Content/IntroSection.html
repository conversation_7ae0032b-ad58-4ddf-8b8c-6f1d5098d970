{namespace f=TYPO3\CMS\Fluid\ViewHelpers}

<f:comment>
    Content Intro Section Partial
    
    Usage:
    <f:render partial="Content/IntroSection" arguments="{
        data: data,
        containerClass: 'col-12 col-md-8',
        showAllLink: {
            pid: data.listPid.0.uid,
            text: 'Show all events',
            translationKey: 'LLL:EXT:vnc_events/ContentBlocks/ContentElements/events/language/frontend.xlf:show_all_events'
        }
    }" />
    
    Parameters:
    - data: Content element data (required)
    - containerClass: CSS classes for container div (optional, default: empty)
    - showAllLink: Object with pid, text, translationKey (optional)
    - gapClass: Gap class for article (optional, default: gap-(--global-spacer-components-m,16px))
</f:comment>

<f:variable name="containerClasses" value="{f:if(condition: containerClass, then: containerClass, else: '')}" />
<f:variable name="articleGap" value="{f:if(condition: gapClass, then: gapClass, else: 'gap-(--global-spacer-components-m,16px)')}" />

<div class="{containerClasses}">
    <f:if condition="{data.header || data.bodytext}">
        <article class="flex flex-col items-start {articleGap}">
            <f:if condition="{data.header}">
                <f:render partial="Header" arguments="{_all}"/>
            </f:if>
            <f:if condition="{data.subheader}">
                <p><strong>{data.subheader}</strong></p>
            </f:if>
            <f:if condition="{data.bodytext}">
                <f:format.html>{data.bodytext}</f:format.html>
            </f:if>
            <f:if condition="{showAllLink.pid}">
                <f:link.typolink parameter="{showAllLink.pid}"
                                 class="flex items-center justify-center h-[48px] py-[20px] px-[32px] gap-[10px] border-(--styling-cta-button-border-radius,0px) bg-(--styling-cta-button-color-primary-default-fill) text-(--styling-cta-button-color-primary-default-text)"
                >
                    <f:if condition="{showAllLink.translationKey}">
                        <f:then>
                            <f:translate key="{showAllLink.translationKey}" />
                        </f:then>
                        <f:else>
                            {showAllLink.text}
                        </f:else>
                    </f:if>
                </f:link.typolink>
            </f:if>
        </article>
    </f:if>
</div>
