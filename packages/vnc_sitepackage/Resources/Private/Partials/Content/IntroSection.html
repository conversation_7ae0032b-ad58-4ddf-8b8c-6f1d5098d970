{namespace f=TYPO3\CMS\Fluid\ViewHelpers}

<f:comment>
    Events Intro Section Partial

    Usage:
    <f:render partial="Content/IntroSection" arguments="{
        data: data,
        classContainer: classContainer
    }" />

    Parameters:
    - data: Content element data (required)
    - classContainer: CSS classes for container div (required)
</f:comment>

<div class="{classContainer}">
    <f:if condition="{data.header || data.bodytext}">
        <article class="flex flex-col items-start gap-(--global-spacer-components-m,16px)">
            <f:if condition="{data.header}">
                <f:render partial="Header" arguments="{_all}"/>
            </f:if>
            <f:if condition="{data.subheader}">
                <p><strong>{data.subheader}</strong></p>
            </f:if>
            <f:if condition="{data.bodytext}">
                <f:format.html>{data.bodytext}</f:format.html>
            </f:if>
            <f:if condition="{data.listPid.0}">
                <f:link.typolink parameter="{data.listPid.0.uid}"
                                 class="flex items-center justify-center h-[48px] py-[20px] px-[32px] gap-[10px] border-(--styling-cta-button-border-radius,0px) bg-(--styling-cta-button-color-primary-default-fill) text-(--styling-cta-button-color-primary-default-text)"
                >
                    <f:translate key="LLL:EXT:vnc_events/ContentBlocks/ContentElements/events/language/frontend.xlf:show_all_events" />
                </f:link.typolink>
            </f:if>
        </article>
    </f:if>
</div>
