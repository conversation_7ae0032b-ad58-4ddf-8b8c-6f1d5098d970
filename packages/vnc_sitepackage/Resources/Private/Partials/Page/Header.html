<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <header
        class="fixed top-0 left-0 w-full z-50 shadow-lg bg-white h-(--header-height)"
        role="banner"
    >
        <div class="container mx-auto px-4 h-full flex items-center gap-6 justify-between lg:justify-start">
            <div class="max-w-[200px]">
                <a
                    href="{f:uri.page(pageUid: settings.startPagePid)}"
                    style="max-width: 100%"
                    aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:home')}"
                >
                    <f:cObject
                        typoscriptObjectPath="lib.inlineSvg"
                        data="{src: '{settings.siteLogo}'}"
                    />
                </a>
            </div>
            <div class="flex items-center">
                <a
                    href="{f:uri.page(pageUid: settings.startPagePid)}"
                    aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:home')}"
                    class="max-w-64"
                >
                    <f:cObject
                        typoscriptObjectPath="lib.inlineSvg"
                        data="{src: '{settings.siteLogo}'}"
                    />
                </a>

                <f:if condition="{mainNavigation}">
                    <f:render
                        partial="Navigation/MainNavigation"
                        arguments="{_all}"
                    />
                </f:if>

                <f:if condition="{metaNavigation}">
                    <f:render
                        partial="Navigation/MetaNavigation"
                        arguments="{_all}"
                    />
                </f:if>
            </div>
        </div>
    </header>
</html>
