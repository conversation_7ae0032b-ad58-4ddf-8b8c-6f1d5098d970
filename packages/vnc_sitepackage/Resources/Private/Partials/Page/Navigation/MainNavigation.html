<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:if condition="{mainNavigation}">
        <nav
            class="w-full flex-none ml-auto flex items-center justify-end lg:justify-start"
            role="navigation"
            aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:main_navigation')}"
        >
            <!-- Hamburger Menu -->
            <button
                aria-label="Öffne mobile Navigation"
                aria-expanded="false"
                aria-controls="main-menu"
                aria-haspopup="true"
                class="hamburger-button flex h-12 w-12 items-center rounded-md space-x-2 my-3 lg:hidden cursor-pointer hover:opacity-60 transition-opacity duration-300"
            >
                <span class="sr-only">Öffne mobile Navigation</span>
                <div class="w-8 flex items-center justify-center relative">
                    <span
                        class="line-1 transform transition -translate-y-2 w-full h-0.5 bg-current absolute in-aria-expanded:translate-y-0 in-aria-expanded:rotate-45"
                    ></span>
                    <span
                        class="line-2 transform transition w-full opacity-100 h-0.5 bg-current absolute in-aria-expanded:opacity-0 in-aria-expanded:translate-x-3"
                    ></span>
                    <span
                        class="line-3 transform transition translate-y-2 w-full h-0.5 bg-current absolute in-aria-expanded:translate-y-0 in-aria-expanded:-rotate-45"
                    ></span>
                </div>
            </button>
            <!-- / Hamburger Menu -->

            <!-- Menu -->
            <ul
                role="menubar"
                aria-label="Menu"
                class="main-menu bg-white absolute hidden left-0 top-(--header-height) w-full list-none m-0 p-2 lg:p-0 lg:static lg:w-auto lg:bg-transparent lg:flex [&.menu-is-open]:bg-gray-100 [&.menu-is-open]:block"
            >
                <f:for
                    each="{mainNavigation}"
                    as="page"
                    iteration="mainCounter"
                >
                    <li
                        class="relative group {f:if(condition: '{page.children}', then: ' HAS-CHILDREN')} {f:if(condition: '{page.active}', then: ' ACTIVE')}"
                        tabindex="0"
                    >
                        <f:variable
                            name="currentAttribute"
                            value="{f:if(condition: '{page.active}', then:'aria-current=page')}"
                        ></f:variable>
                        <button
                            aria-expanded="true"
                            aria-haspopup="true"
                            aria-controls="id_menu_1"
                            class="flex items-center px-4 py-2 cursor-pointer text-(--nav-main-level1-text) hover:text-(--nav-main-level1-text-hover) {f:if(condition: '{page.children}', then: ' HAS-CHILDREN')} {f:if(condition: '{page.active}', then: ' text-(--nav-main-level1-text-active)')}"
                            {currentAttribute}
                        >
                            {page.title}
                        </button>
                        <f:if condition="{page.children}">
                            <ul
                                id="id_menu_1"
                                role="menu"
                                class="main-menu__sub-menu lg:absolute hidden group-hover:block lg:top-[calc(100%+1rem)] lg:left-0 lg:min-w-[800px] lg:gap-4 lg:p-6 lg:bg-gray-100 lg:shadow-lg"
                            >
                                <li class="" tabindex="0">
                                    <ul class="flex flex-col lg:flex-row lg:flex-wrap gap-6 p-4 lg:p-0">
                                        <f:for
                                            each="{page.children}"
                                            as="childPage"
                                            iteration="childCounter"
                                        >
                                            <li
                                                class="{f:if(condition: '{childPage.active}', then: '"
                                            >
                                                <f:variable
                                                    name="currentChildAttribute"
                                                    value="{f:if(condition: '{childPage.active}', then:'aria-current=page')}"
                                                ></f:variable>
                                                <a
                                                    href="{childPage.link}"
                                                    class="text-(--nav-main-level2-text) hover:text-(--nav-main-level2-text-hover) {f:if(condition: '{childPage.active}', then: ' text-(--nav-main-level2-text-active)')}"
                                                    role="menuitem"
                                                    {currentChildAttribute}
                                                >
                                                    {childPage.title}
                                                </a>
                                            </li>
                                        </f:for>
                                    </ul>
                                </li>
                            </ul>
                        </f:if>
                    </li>
                </f:for>
            </ul>
            <!-- / Menu -->
        </nav>
    </f:if>
</html>
