<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <footer class="py-12">
      <div class="container">
        <div class="grid grid-cols-12 gap-4">
          <div class="col-span-12">
            <h1 class="headline-xxs text-yellow-500 md:text-red-500 hover:text-blue-500 cursor-pointer">Test</h1>
          </div>
        </div>
      </div>
        <div class="container">
            <nav
                class=""
                aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:socialmedia_links')}"
            >
                <ul class="flex gap-2 justify-center">
                    <f:if condition="{settings.linkFacebook}">
                        <li class="w-12">
                            <a
                                class="group"
                                href="{settings.linkFacebook}"
                                target="_blank"
                                aria-label="Facebook"
                                tabindex="0"
                            >
                                <i class="svg-icon svg-icon--xl svg-btn ">
                                    <svg
                                        class="icon  
                                        "
                                        preserveAspectRatio="xMaxYMin"
                                    >
                                        <use
                                            xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-logo-facebook"
                                        ></use>
                                    </svg>
                                </i>
                            </a>
                        </li>
                    </f:if>

                    <f:if condition="{settings.linkInstagram}">
                        <li class="w-12">
                            <a
                                class="group"
                                href="{settings.linkInstagram}"
                                target="_blank"
                                aria-label="Instagram"
                                tabindex="0"
                            >
                                <i class="svg-icon svg-icon--xl svg-btn ">
                                    <svg
                                        class="icon"
                                        preserveAspectRatio="xMaxYMin"
                                    >
                                        <use
                                            xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-logo-instagram"
                                        ></use>
                                    </svg>
                                </i>
                            </a>
                        </li>
                    </f:if>

                    <f:if condition="{settings.linkLinkedin}">
                        <li class="w-12">
                            <a
                                class="group"
                                href="{settings.linkLinkedin}"
                                target="_blank"
                                aria-label="Linkedin"
                                tabindex="0"
                            >
                                <i class="svg-icon svg-icon--xl svg-btn ">
                                    <svg
                                        class="icon"
                                        preserveAspectRatio="xMaxYMin"
                                    >
                                        <use
                                            xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-logo-linkedin"
                                        ></use>
                                    </svg>
                                </i>
                            </a>
                        </li>
                    </f:if>

                    <f:if condition="{settings.linkYoutube}">
                        <li class="w-12">
                            <a
                                class="group"
                                href="{settings.linkyoutube}"
                                target="_blank"
                                aria-label="Youtube"
                                tabindex="0"
                            >
                                <i class="svg-icon svg-icon--xl svg-btn ">
                                    <svg
                                        class="icon"
                                        preserveAspectRatio="xMaxYMin"
                                    >
                                        <use
                                            xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-logo-youtube"
                                        ></use>
                                    </svg>
                                </i>
                            </a>
                        </li>
                    </f:if>

                    <f:if condition="{settings.linkX}">
                        <li class="nav__item">
                            <a
                                class="nav__item"
                                href="{settings.linkX}"
                                target="_blank"
                                aria-label="Twitter"
                                tabindex="0"

                            >
                                <i class="svg-icon svg-icon--large">
                                    <svg
                                        class="icon"
                                        preserveAspectRatio="xMaxYMin"
                                    >
                                        <use
                                            xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-logo-twitter"
                                        ></use>
                                    </svg>
                                </i>
                            </a>
                        </li>
                    </f:if>
                </ul>
            </nav>

            <div class="grid grid-cols-3 gap-4">
                <div class="col-span-1">
                    <a
                        href="{f:uri.page(pageUid: settings.startPagePid)}"
                        style="max-width: 100%"
                        aria-label="Home"
                        tabindex="0"
                    >
                        <f:cObject
                            typoscriptObjectPath="lib.inlineSvg"
                            data="{src: '{settings.siteLogo}'}"
                        />
                    </a>
                </div>
                <!-- <div class="col-3">
                    <f:cObject
                        typoscriptObjectPath="lib.dynamicContent"
                        data="{pageUid: '{settings.footerNavigationPid}', colPos: '2'}"
                    />
                </div>
                <div class="col-3">
                    <f:cObject
                        typoscriptObjectPath="lib.dynamicContent"
                        data="{pageUid: '{settings.footerNavigationPid}', colPos: '3'}"
                    />
                </div>
                <div class="col-3">
                    <f:cObject
                        typoscriptObjectPath="lib.dynamicContent"
                        data="{pageUid: '{settings.footerNavigationPid}', colPos: '4'}"
                    />
                </div> -->
            </div>

            <nav
                class="flex justify-center"
                aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:footer_navigation')}"
            >
                <ul class="flex gap-4">
                    <f:for each="{footerNavigation}" as="page" iteration="i">
                        <li class="">
                            <f:variable
                                name="currentAttribute"
                                value="{f:if(condition: '{page.active}', then:'aria-current=page')}"
                            ></f:variable>
                            <a
                                href="{page.link}"
                                {f:if(condition: page.target, then: 'target="{page.target}"')}
                                class="link text-s"
                                {currentAttribute}
                                >{page.title}</a
                            >
                        </li>
                    </f:for>
                </ul>
            </nav>
        </div>
    </footer>
</html>
