<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:v="http://typo3.org/ns/Vancado/VncSitepackage/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
<f:if condition="{metaNavigation}">
    <nav
        aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:meta_navigation')}"
        class="hidden nav nav--meta">
        <div class="container-lg">
            <ul class="nav__list d-lg-flex">
                <f:cObject typoscriptObjectPath="lib.searchBox"/>
                <f:comment><!--
                <li class="nav__item nav__item--icon">
                    <button
                        class="nav__link d-none d-sm-block"
                        role="button"
                        type="button"
                    >
                        <span class="svg-icon">
                          <svg class="icon" preserveAspectRatio="xMaxYMin">
                            <use
                                x="0"
                                xlink:href="./symbol-defs.svg#icon-magnifier"
                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                y="0"
                            ></use>
                          </svg>
                        </span>
                    </button>
                    <span class="nav__link d-block d-sm-none mx-3 position-relative">
                        <span class="input input--text input--search">
                          <button class="input__button" role="button" type="button">
                            <span class="svg-icon">
                              <svg class="icon" preserveAspectRatio="xMaxYMin">
                                <use
                                    x="0"
                                    xlink:href="./symbol-defs.svg#icon-magnifier"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    y="0"
                                ></use>
                              </svg>
                            </span>
                          </button>
                          <input placeholder="Search" type="text"/>
                        </span>
                      </span>
                </li>
                    ### Sprachswitcher Link ###
                    <f:cObject typoscriptObjectPath="lib.languageSelect"/>

                    <li class="nav__item nav__item--icon">
                        <button type="button" role="button" class="nav__link">
                <span class="lang-switch">
                  <span class="caption d-sm-none">EN</span>
                  <span class="svg-icon">
                    <svg class="icon" preserveAspectRatio="xMaxYMin">
                      <use
                          xlink:href="./symbol-defs.svg#icon-globe"
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          x="0"
                          y="0"
                      ></use>
                    </svg>
                  </span>
                </span>
                        </button>
                    </li>-->
                </f:comment>
                <f:for as="metaItem" each="{metaNavigation}" iteration="i">
                    <li class="nav__item nav__item{f:if(condition: '{metaItem.active}', then: ' active')}">
                        <f:variable name="currentAttribute"
                                    value="{f:if(condition: '{metaItem.active}', then:'aria-current=page')}"></f:variable>
                        <a class="nav__link py-2 px-lg-3 small" href="{metaItem.link}"
                           target="{f:if(condition: metaItem.target, then: metaItem.target, else: '_self')}"
                           {currentAttribute}>
                            <f:if condition="{metaItem.files.0}">
                                <v:svg file="{metaItem.files.0}"/>
                            </f:if>
                            <f:format.raw>{metaItem.title}</f:format.raw>
                        </a>
                    </li>
                </f:for>
            </ul>
        </div>
    </nav>
</f:if>
</html>
