<?php

defined('TYPO3') or die();

(function () {
    $EXTKEY = 'vnc_sitepackage';

    // Add default RTE configuration for CElements (incl. Tables)
    $GLOBALS['TYPO3_CONF_VARS']['RTE']['Presets']['VncDefault'] = 'EXT:' . $EXTKEY . '/Configuration/RTE/Default.yaml';
    $GLOBALS['TYPO3_CONF_VARS']['RTE']['Presets']['VncContentBasic'] =
        'EXT:' . $EXTKEY . '/Configuration/RTE/ContentBasic.yaml';

    /***************
     * Register Icons SVG
     */
    $svgIcons = [
        'vnctext-icon' => 'vnctext-icon.svg',
        'vnctextmedia-icon' => 'vnctextmedia-icon.svg',
        'vnctextimage-icon' => 'vnctextimage-icon.svg',
        'vncimage-icon' => 'vncimage-icon.svg',
        'vncgallery-icon' => 'vncgallery-icon.svg',
        'vnctexttiles-icon' => 'vnctexttiles-icon.svg',
        'vnctextimagetiles-icon' => 'vnctextimagetiles-icon.svg',
        'vncstage-icon' => 'vncstage-icon.svg',
        'vncstageslider-icon' => 'vncstageslider-icon.svg',
        'vncstagesplit-icon' => 'vncstagesplit-icon.svg',
        'vncimageslider-icon' => 'vncimageslider-icon.svg',
        'vncintrotext-icon' => 'vncintrotext-icon.svg'
    ];

    $iconRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Imaging\IconRegistry::class);
    foreach ($svgIcons as $identifier => $fileName) {
        $iconRegistry->registerIcon(
            $identifier,
            \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
            ['source' => 'EXT:' . $EXTKEY . '/Resources/Public/Icons/' . $fileName]
        );
    }
})();
