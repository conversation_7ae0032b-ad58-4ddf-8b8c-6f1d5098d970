import { defineConfig } from "vite";
import { dirname, resolve } from "node:path";
import { fileURLToPath } from "node:url";
import autoOrigin from "vite-plugin-auto-origin";
import tailwindcss from "@tailwindcss/vite";

const VITE_TYPO3_ROOT = "./";
const VITE_ENTRYPOINTS = ["frontend/js/global.js"];
const VITE_OUTPUT_PATH = "packages/vnc_sitepackage/Resources/Public/dist";

const currentDir = dirname(fileURLToPath(import.meta.url));
const rootPath = resolve(currentDir, VITE_TYPO3_ROOT);

export default defineConfig(({ command, mode }) => {
    const isProduction = mode === 'production';
    
    return {
        mode: `${mode}`,
        base: "",
        build: {
            minify: isProduction,
            sourcemap: !isProduction,
            manifest: true,
            copyPublicDir: true,
            rollupOptions: {
                input: VITE_ENTRYPOINTS.map((entry) =>
                    resolve(rootPath, entry)
                ),
                output: {
                    assetFileNames: (assetInfo) => {
                        let extType = assetInfo.name.split(".").at(1);
                        if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType)) {
                            extType = "img";
                        }
                        return `[name]-[hash][extname]`;
                    },
                    chunkFileNames: "[name]-[hash].js",
                    entryFileNames: "[name]-[hash].js",
                    manualChunks: (path) =>
                        path.split("/").reverse()[
                            path.split("/").reverse().indexOf("node_modules") -
                                1
                        ],
                },
            },
            outDir: resolve(rootPath, VITE_OUTPUT_PATH),
        },
        publicDir: "frontend/static",

        // Adjust Vites dev server for DDEV
        // https://vitejs.dev/config/server-options.html
        server: {
            host: "0.0.0.0",
            port: 5173,
            strictPort: true,
            origin: `${process.env.DDEV_PRIMARY_URL?.replace(/:\d+$/, "") || 'localhost'}:5173`,
            cors: {
                origin: /https?:\/\/([A-Za-z0-9\-\.]+)?(\.ddev\.site|\.vancado\.de)(?::\d+)?$/,
            },
            allowedHosts: ["typo3-boilerplate.ddev.site", "stage.vancado.de"],
        },
        plugins: [tailwindcss(), autoOrigin()],
        define: {
            __VUE_OPTIONS_API__: true,
            __VUE_PROD_DEVTOOLS__: false,
        },
    };
});
