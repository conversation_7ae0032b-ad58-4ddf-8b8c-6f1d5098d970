<style>
    h2,
    h3 {
        break-before: page;
    }
    @media print {
      div.divFooter {
        position: fixed;
        bottom: 0;
        right: 0;
      }
    }
</style>
# Typo3 Boilerplate

<!-- TOC -->
* [Typo3 Boilerplate](#typo3-boilerplate)
  * [Content Elements (Stage Bereich)](#content-elements-stage-bereich)
    * [Stage - vncstage](#stage---vncstage)
    * [Stage-Slider - vncstageslider](#stage-slider---vncstageslider)
    * [Stage-Split - vncstagesplit](#stage-split---vncstagesplit)
    * [Introtext - vncintrotext](#introtext---vncintrotext)
  * [Content Elements (Content Bereich)](#content-elements-content-bereich)
    * [Text - vnctext](#text---vnctext)
    * [Text/Bild - vnctextimage](#textbild---vnctextimage)
    * [Text/Media - vnctextmedia](#textmedia---vnctextmedia)
    * [Akkordeons - accordions](#akkordeons---accordions)
    * [Bild - vncimage](#bild---vncimage)
    * [Galerie - vncgallery](#galerie---vncgallery)
    * [Bild Slider - vncimageslider](#bild-slider---vncimageslider)
    * [Fullsize Teaser - vncfullsizeteaser](#fullsize-teaser---vncfullsizeteaser)
    * [Fullsize Teaser Split - vncfullsizeteasersplit](#fullsize-teaser-split---vncfullsizeteasersplit)
    * [Text/Bild Kacheln - vnctextimagetiles](#textbild-kacheln---vnctextimagetiles)
    * [Text-Kacheln - vnctexttiles](#text-kacheln---vnctexttiles)
    * [Zitat - vncquote](#zitat---vncquote)
  * [Special Elements](#special-elements)
    * [Warnung - vncwarnings](#warnung---vncwarnings)
    * [Fomulare - powermail](#fomulare---powermail)
    * [Ansprechpartner - tt_address](#ansprechpartner---tt_address)
    * [News - news](#news---news)
    * [Suche - indexed_search](#suche---indexed_search)
<!-- TOC -->

## Content Elements (Stage Bereich)

### Stage - vncstage

| **Wireframe**    | Stage Fullscreen Bild + Text-Overlay          |
|------------------|-----------------------------------------------|
| **Beschreibung** | Fullsize Bild oder Video mit Text und Subline |
| Einsatz          | Hero                                          |


| Feldname           | Feld                   | Beschreibung          |
|--------------------|------------------------|-----------------------|
| Header             | `header`               | Input                 |
| Text               | `bodytext`             | Textfield             |
| Link               | `vnc_content_link`     | link                  |
| Link-Bezeichnung   | `vnc_content_linktext` | input                 |
| Bild               | `image`                | Bild (jpg, jpeg, png) |
| Abstand nach unten | `space_after_class`    | Normal / Klein / None |


Ausführung: Als Einzelnes Element. Slider siehe vncstageslider
![](./wireframes/Stages_1920px/Stage_Fullscreen_Bild_%2B_Text-Overlay_white.png)

### Stage-Slider - vncstageslider

| **Wireframe**    | Stage Fullscreen (Bild + Text) als Slider |
|------------------|-------------------------------------------|
| **Beschreibung** | Mehrere Stage Elemente als Slider         |
| Einsatz          | Hero                                      |


| Feldname             | Feld                           | Beschreibung          |
|----------------------|--------------------------------|-----------------------|
| Bezeichnung          | `header`                       | Input                 |
| Stage Slider Element | `vnc_content_stageslider_item` |                       |
| Abstand nach unten   | `space_after_class`            | Normal / Klein / None |

IRRE-Element `vnc_content_textimagetiles_item` (min 2, max 99 Elemente)

| Feldname    | Feld                   | Beschreibung          |
|-------------|------------------------|-----------------------|
| Überschrift | `header`               | input                 |
| Text        | `bodytext`             | RTEDefault            |
| Link        | `vnc_content_link`     | link                  |
| Linktext    | `vnc_content_linktext` | input                 |
| Bild        | `image`                | Bild (jpg, jpeg, png) |

![Stage_Fullscreen_Bild_+_Text-Overlay_white.png](wireframes/Stages_1920px/Stage_Fullscreen_Bild_%2B_Text-Overlay_white.png)

### Stage-Split - vncstagesplit

| **Wireframe**    | Stage 50-50 Split inkl. Text                     |
|------------------|--------------------------------------------------|
| **Beschreibung** | Ein Stage Element in zwei Hälften: Text und Bild |
| Einsatz          | Hero                                             |


| Feldname           | Feld                   | Beschreibung            |
|--------------------|------------------------|-------------------------|
| Header             | `header`               | Input                   |
| Type               | `header_layout`        | H1, H2, H3, versteckt   |
| Text               | `bodytext`             | RTE Basic               |
| Link               | `vnc_content_link`     | link                    |
| Link-Bezeichnung   | `vnc_content_linktext` | input                   |
| Bild               | `assets`               | Bild (jpg, jpeg, png)   |
| Ansicht            | `vnc_content_layout`   | Bild links, Bild rechts |
| Abstand nach unten | `space_after_class`    | Normal / Klein / None   |

![](./wireframes/Stages_1920px/Stage_50-50_Split_inkl._Text_white.png)

### Introtext - vncintrotext

| **Wireframe**    | Hinweise/Hinweis 1 Nachricht                |
|------------------|---------------------------------------------|
| **Beschreibung** | Ein Hinweistext im Stage Bereich der Seite. |
| Einsatz          | Stage                                       |


| Feldname    | Feld            | Beschreibung          |
|-------------|-----------------|-----------------------|
| Überschrift | `header`        |                       |
| Typ         | `header_layout` | H1, H2, H3, verborgen |
| Text        | `bodytext`      | RTE Basic             |

* Hier kein schließen Button (Vgl. vnc_warnings)
* Iconselector, versch. Hintergrundfarben etc. nachrüstbar

![](./img/Hinweis1Nachricht.png.white.png)

## Content Elements (Content Bereich)

### Text - vnctext

| **Wireframe**    | Text                                        |
|------------------|---------------------------------------------|
| **Beschreibung** | Standard Textelement, ein- oder zweispaltig |
| Einsatz          | Content                                     |


| Feldname                                              | Feld                            | Beschreibung                       |
|-------------------------------------------------------|---------------------------------|------------------------------------|
| Header                                                | `header`                        | Input                              |
| Type                                                  | `header_layout`                 | H1, H2, H3, versteckt              |
| Subheader                                             | `subheader`                     | Input                              |
| Introtext                                             | `vnc_content_introtext`         | Textfield                          |
| Ansicht                                               | `vnc_content_layout`            | 1-spaltiger Text, 2-spaltiger Text |
| Text                                                  | `bodytext`                      | RTE Advanced                       |
| Text rechte Spalte<br/>(bei Ansicht 2-spaltiger Text) | `vnc_content_bodytextsecondcol` | RTE Advanced                       |
| Link                                                  | `vnc_content_link`              | link                               |
| Link-Bezeichnung                                      | `vnc_content_linktext`          | input                              |
| Abstand nach unten                                    | `space_after_class`             | Normal / Klein / None              |

Zwei Spalten
![Text_2-spaltig_white.png](wireframes/Texte_1920px/Text_2-spaltig_white.png)
Eine Spalte
![Text_white.png](wireframes/Texte_1920px/Text_white.png)

### Text/Bild - vnctextimage

| **Wireframe**    | Bild-Text                     |
|------------------|-------------------------------|
| **Beschreibung** | Ein Element mit Text und Bild |
| Einsatz          | Content                       |


| Feldname           | Feld                   | Beschreibung            |
|--------------------|------------------------|-------------------------|
| Header             | `header`               | Input                   |
| Type               | `header_layout`        | H1, H2, H3, versteckt   |
| Text               | `bodytext`             | RTE Advanced            |
| Bild               | `assets`               | Bild                    |
| Link               | `vnc_content_link`     | link                    |
| Link-Bezeichnung   | `vnc_content_linktext` | input                   |
| Ansicht            | `vnc_content_layout`   | Bild links, Bild rechts |
| Abstand nach unten | `space_after_class`    | Normal / Klein / None   |

![](wireframes/Bild-Media_1920px/Bild-Text_white.png)

### Text/Media - vnctextmedia

| **Wireframe**    | Bild-Text                                                                                  |
|------------------|--------------------------------------------------------------------------------------------|
| **Beschreibung** | Ein Element mit Text und Bild oder Video. Ein Video ist klassisch im Format 16:9 angelegt. |
| Einsatz          | Content                                                                                    |


| Feldname           | Feld                   | Beschreibung            |
|--------------------|------------------------|-------------------------|
| Header             | `header`               | Input                   |
| Type               | `header_layout`        | H1, H2, H3, versteckt   |
| Text               | `bodytext`             | RTE Advanced            |
| Bild/Video         | `assets`               | Bild                    |
| Link               | `vnc_content_link`     | link                    |
| Link-Bezeichnung   | `vnc_content_linktext` | input                   |
| Ansicht            | `vnc_content_layout`   | Bild links, Bild rechts |
| Abstand nach unten | `space_after_class`    | Normal / Klein / None   |

![](wireframes/Bild-Media_1920px/Media-Text_white.png)

### Akkordeons - accordions

| **Wireframe**    | Text/Akkordeon                                                  |
|------------------|-----------------------------------------------------------------|
| **Beschreibung** | Akkordeon mit optionalem Bild links in square oder hochformat** |


| Feldname           | Feld                    | Beschreibung                                                        |
|--------------------|-------------------------|---------------------------------------------------------------------|
| Überschrift        | `header`                |                                                                     |
| Typ                | `header_layout`         | H1, H2, H3, verborgen                                               |
| Introtext          | `vnc_content_introtext` | Text, der über dem Element angezeigt wird                           |
| Ansicht            | `vnc_content_layout`    | ohne Bild / mit Bild links quadratisch  / mit Bild links hochformat |
| Bild               | `image`                 | Bild (nur in der Ansicht "mit Bild")                                |
| Abstand nach unten | `space_after_class`     | Normal / Klein / None                                               |

IRRE-Element `tx_vnc_content_tabs_item` (min 1, max 99 Elemente)

| Feldname    | Feld       | Beschreibung   |
|-------------|------------|----------------|
| Überschrift | `header`   |                |
| Text        | `bodytext` | RTEDefault     |
| Medien      | `media`    | einzelnes Bild |

![](./img/Akkordeon_inkl_Bild.png.white.png)

![](./img/Akkordeon.png.white.png)

NN: Zweispaltige Ansicht mit Header/Introtext links (statt Bild). Leicht implementierbar durch zusätzliche Ansichts-Option
![](./img/Akkordeon_2-spaltig.png.white.png)

### Bild - vncimage

| **Wireframe**    | Bild-Media/Bild 1-spaltig Zoom & Download |
|------------------|-------------------------------------------|
| **Beschreibung** | Zeigt ein einfaches Bild an.              |
| Einsatz          | Content                                   |

NN: Zweispaltig (Vgl. auch vncgallery für mehrere Bilder)

| Feldname           | Feld                | Beschreibung          |
|--------------------|---------------------|-----------------------|
| Bild               | `image`             | Bild (jpg, jpeg, png) |
| Abstand nach unten | `space_after_class` | Normal / Klein / None |

![](./img/Bild_1-spaltig_Zoom_Download.png.white.png)

![](./img/Bild_2-spaltig_Zoom_Download.png.white.png)

### Galerie - vncgallery

| **Wireframe**    | Bild-Media/Bild 2-spaltig Zoom & Download                                         |
|------------------|-----------------------------------------------------------------------------------|
| **Beschreibung** | Zeigt mehrere Bilder in einer Galerie mit optionalem Vergrößern und Download-Icon |
| Einsatz          | Content                                                                           |


| Feldname           | Feld                        | Beschreibung            |
|--------------------|-----------------------------|-------------------------|
| Header             | `header`                    | Input                   |
| Text               | `bodytext`                  | RTE Basic               |
| Bilder             | `image`                     | Bilder (jpg, jpeg, png) |
| Vergrößern-Icon    | `vnc_content_enlarge_icon`  | Checkbox                |
| Download-Icon      | `vnc_content_download_icon` | Checkbox                |
| Abstand nach unten | `space_after_class`         | Normal / Klein / None   |

![](./img/Bild_2-spaltig_Zoom_&_Download_white.png)

### Bild Slider - vncimageslider

| **Wireframe**    | NN                                           |
|------------------|----------------------------------------------|
| **Beschreibung** | Bilderleiste eals Slider, Bilder verlinkbar. |
| Einsatz          | Content                                      |


| Feldname           | Feld                | Beschreibung                              |
|--------------------|---------------------|-------------------------------------------|
| Überschrift        | `header`            |                                           |
| Typ                | `image`             | H1, H2, H3, verborgen                     |
| Untertitel         | `subheader`         |                                           |
| Frontend-Layout    | `layout`            | Fix, Flexibel, Static                     |
| Bilder             | `image`             | Bilder 0-20 (jpg, jpeg, png) mit Linkfeld |
| Abstand nach unten | `space_after_class` | Normal / Klein / None                     |

Frontend-Layout: Fix
![](./img/Pasted20231220162516.png)

Frontend-Layout: Flexibel
![](./img/Pasted20231220162314.png)

Frontend-Layout: Static
![](./img/Pasted20231220162437.png)

<div style="break-after: page;"></div>

### Fullsize Teaser - vncfullsizeteaser

| **Wireframe**    | Fullsize/Fullsize Teaser 100                      |
|------------------|---------------------------------------------------|
| **Beschreibung** | Ein Teaserelement über die gesamte Contentbreite. |


| Feldname           | Feld                   | Beschreibung                                                       |
|--------------------|------------------------|--------------------------------------------------------------------|
| Überschrift        | `header`               |                                                                    |
| Text               | `bodytext`             | Textfield                                                          |
| Link               | `vnc_content_link`     | Text, der über dem Element angezeigt wird                          |
| Link-Bezeichnung   | `vnc_content_linktext` | Optional - Wenn leer, wird der Seitenname bzw. die URI ausgespielt |
| Bild-Element       | `image`                | Bild (jpg, jpeg, png)                                              |
| Abstand nach unten | `space_after_class`    | Normal / Klein / None                                              |

![](./img/Fullsize Teaser 100.png.white.png)

<div style="break-after: page;"></div>

### Fullsize Teaser Split - vncfullsizeteasersplit

| **Wireframe**    | Fullsize/Fullsize Teaser 50-50                                                   |
|------------------|----------------------------------------------------------------------------------|
| **Beschreibung** | Ein Teaserelement in zwei Hälften: Text und Bild über die gesamte Contentbreite. |
| Einsatz          | Content                                                                          |


| Feldname           | Feld                   | Beschreibung                                                       |
|--------------------|------------------------|--------------------------------------------------------------------|
| Überschrift        | `header`               |                                                                    |
| Typ                | `header_layout`        | H1, H2, H3, verborgen                                              |
| Text               | `bodytext`             | RTEBasic                                                           |
| Link               | `vnc_content_link`     | Text, der über dem Element angezeigt wird                          |
| Link-Bezeichnung   | `vnc_content_linktext` | Optional - Wenn leer, wird der Seitenname bzw. die URI ausgespielt |
| Bild               | `image`                | Bild (jpg, jpeg, png)                                              |
| Ansicht            | `vnc_content_layout`   | Bild links, Bild rechts                                            |
| Abstand nach unten | `space_after_class`    | Normal / Klein / None                                              |

![](./img/Fullsize Teaser 50-50.png.white.png)

<div style="break-after: page;"></div>

### Text/Bild Kacheln - vnctextimagetiles

| **Wireframe**    | Bildkachel 2-/3-/4-spaltig                       |
|------------------|--------------------------------------------------|
| **Beschreibung** | Textkacheln mit Bild, Überschrift, Text und Link |
| Einsatz          | Content                                          |


| Feldname           | Feld                              | Beschreibung          |
|--------------------|-----------------------------------|-----------------------|
| Header             | `header`                          | Input                 |
| Type               | `header_layout`                   | H1, H2, H3, versteckt |
| Introtext          | `bodytext`                        | Textfield             |
| Spalten            | `vnc_content_columns`             | 1,2,3,4               |
| Text-Bild-Kacheln  | `vnc_content_textimagetiles_item` |                       |
| Abstand nach unten | `space_after_class`               | Normal / Klein / None |

IRRE-Element `vnc_content_textimagetiles_item` (min 2, max 99 Elemente)

| Feldname    | Feld                   | Beschreibung          |
|-------------|------------------------|-----------------------|
| Überschrift | `header`               | input                 |
| Text        | `bodytext`             | RTEDefault            |
| Link        | `vnc_content_link`     | link                  |
| Linktext    | `vnc_content_linktext` | input                 |
| Bild        | `image`                | Bild (jpg, jpeg, png) |

![](wireframes/Kacheln_1920px/Bildkachel_2-spaltig_white.png)
![](wireframes/Kacheln_1920px/Bildkachel_3-spaltig_white.png)
![](wireframes/Kacheln_1920px/Bildkachel_4-spaltig_white.png)

<div style="break-after: page;"></div>

### Text-Kacheln - vnctexttiles

| **Wireframe**    | Bildkachel 2-/3-/4-spaltig |
|------------------|----------------------------|
| **Beschreibung** | Textkachel 3-/4-spaltig    |
| Einsatz          | Content                    |


| Feldname           | Feld                              | Beschreibung          |
|--------------------|-----------------------------------|-----------------------|
| Header             | `header`                          | Input                 |
| Type               | `header_layout`                   | H1, H2, H3, versteckt |
| Introtext          | `vnc_content_introtext`           | Textfield             |
| Text-Kacheln       | `vnc_content_textimagetiles_item` | s.u.                  |
| Abstand nach unten | `space_after_class`               | Normal / Klein / None |

IRRE-Element `vnc_content_texttiles_item` (min 2, max 99 Elemente)

| Feldname    | Feld                   | Beschreibung          |
|-------------|------------------------|-----------------------|
| Überschrift | `header`               | input                 |
| Icon        | `icon`                 | Icon Selector         |
| Text        | `bodytext`             | input                 |
| Link        | `vnc_content_link`     | link                  |
| Linktext    | `vnc_content_linktext` | input                 |

![](wireframes/Kacheln_1920px/Textkachel_3-spaltig_white.png)
![](wireframes/Kacheln_1920px/Textkachel_4-spaltig_white.png)

<div style="break-after: page;"></div>

### Zitat - vncquote

| **Wireframe**    | Texte/Zitat                                       |
|------------------|---------------------------------------------------|
| **Beschreibung** | Ein einfaches Zitat mit optionaler Autorenangabe. |
| Einsatz          | Content                                           |

| Feldname | Feld       | Beschreibung |
|----------|------------|--------------|
| Zitat    | `bodytext` | Textfield    |
| Autor    | `header`   |              |

NN: (Autoren-)Bild
![](./img/Zitat.png.white.png)

<div style="break-after: page;"></div>

## Special Elements

### Warnung - vncwarnings


| **Wireframe**    | Hinweis Nachricht Modal                |
|------------------|----------------------------------------|
| **Beschreibung** |                                        |
| Einsatz          | Als list-Element in Sys-Ordner anlegen |


| Feldname           | Feld                              | Beschreibung          |
|--------------------|-----------------------------------|-----------------------|
| Header             | `header`                          | Input                 |
| Text               | `text`                            | RTE Basic             |
| Link               | `link`                            | link                  |
| Linktext           | `link_text`                       | input                 |
| Icon               | `icon`                            | Icon Selector         |
| Pages              | `pages`                           | Page Selector         |

![](wireframes/Hinweise_1920px/Hinweis_Nachricht_Modal.png)

<div style="break-after: page;"></div>

### Fomulare - powermail

Keine Formulare im Modal!

![Formular_white.png](wireframes/Formulare_1920px/Formular_white.png)

### Ansprechpartner - tt_address

![Kontakt_1-spaltig_white.png](wireframes/Ansprechpartner_1920px/Kontakt_1-spaltig_white.png)
![Kontakt_2-spaltig_white.png](wireframes/Ansprechpartner_1920px/Kontakt_2-spaltig_white.png)

### News - news

Ausführung: Nicht als Slider sondern als grid mit Paginierung

![Newskachel_2-spaltig_white.png](wireframes/Kacheln_1920px/Newskachel_2-spaltig_white.png)
![Newskachel_3-spaltig_white.png](wireframes/Kacheln_1920px/Newskachel_3-spaltig_white.png)
![Newskachel_4-spaltig_white.png](wireframes/Kacheln_1920px/Newskachel_4-spaltig_white.png)

### Suche - indexed_search

Suchslot und Ergebnisseite mit Paginierung

<div class="divFooter">Typo3 Boilerplate Stand 14.02.2024</div>
