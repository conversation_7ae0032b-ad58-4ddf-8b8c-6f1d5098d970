{"name": "typo3", "version": "1.0.0", "app": "index.js", "license": "MIT", "type": "module", "scripts": {"watch": "./node_modules/.bin/vite build --watch --mode development", "build:development": "./node_modules/.bin/vite build --mode development", "build:production": "./node_modules/.bin/vite build --mode production", "deploy": "npm i && npm run build:production"}, "engines": {"node": "~18 || ~20", "npm": ">=9.0.0 <11.0.0"}, "devDependencies": {"autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "^4.1.7", "vite": "^5.4.18", "vite-plugin-auto-origin": "^1.2.1", "vite-plugin-typo3": "^1.3.0"}, "dependencies": {"@splidejs/splide": "^4.1.4", "@splidejs/splide-extension-auto-scroll": "^0.5.3", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/vite": "^4.1.7"}}