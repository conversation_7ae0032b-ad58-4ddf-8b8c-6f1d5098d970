# todo #

### Content-Element-Icons ###
Anfordern/Ändern in:
packages/vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Public/Icons
ddev composer require "georgringer/news:dev-main as 11.2" "typo3/cms-backend:^12.4" "vancado/vnc-content-tabs:^12" "typo3/cms-belog:^12.4" "typo3/cms-beuser:^12.4" "typo3/cms-core:^12.4" "typo3/cms-dashboard:^12.4" "typo3/cms-extbase:^12.4" "typo3/cms-extensionmanager:^12.4" "typo3/cms-felogin:^12.4" "typo3/cms-filelist:^12.4" "typo3/cms-fluid:^12.4" "typo3/cms-fluid-styled-content:^12.4" "typo3/cms-frontend:^12.4" "typo3/cms-impexp:^12.4" "typo3/cms-indexed-search:^12.4" "typo3/cms-info:^12.4" "typo3/cms-install:^12.4" "typo3/cms-lowlevel:^12.4" "typo3/cms-recycler:^12.4" "typo3/cms-rte-ckeditor:^12.4" "typo3/cms-scheduler:^12.4" "typo3/cms-seo:^12.4" "typo3/cms-setup:^12.4" "typo3/cms-sys-note:^12.4" "typo3/cms-t3editor:^12.4" "typo3/cms-tstemplate:^12.4" "typo3/cms-viewpage:^12.4" "brotkrueml/schema:dev-main" "deployer/deployer:^7.0" "friendsoftypo3/tt-address:^8.0" "helhum/typo3-console:^8.0" "in2code/powermail:^12.0" "t3monitor/t3monitoring_client:dev-master" "t3/min:^3.0" "vancado/vnc-icon-formelement:^12" "vancado/vnc-powermail:^3" "vancado/vnc-sitepackage:^1.0" "vancado/vnc-warnings:dev-feature/typo3-12-update" "yoast-seo-for-typo3/yoast_seo:^9.0"



